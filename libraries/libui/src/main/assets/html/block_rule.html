<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="title">黑名单规则</title>
    <link rel="stylesheet" href="../css/font.css">
    <script src="../js/i18next.min.js"></script>
    <script src="../js/language.js"></script>
    <style type="text/css">
        p {
            margin: 0px;
        }

        .title {
            color: #202530;
            font-size: 16px;
            font-weight: bold;
            margin-top: 0px;
            margin-bottom: 6px;
        }

        .detail {
            color: #202530;
            font-size: 14px;
            font-weight: normal;
            margin-bottom: 6px;
        }

        .detail1 {
            margin-bottom: 9px;
        }


        :root {
            margin-top: 15px;
            margin-bottom: 110px;
            margin-left: 15px;
            margin-right: 15px;
        }
    </style>
</head>

<body>
<div>
    <p class="title" id="t1"></p>
    <p class="detail detail1" id="d1"></p>
    <p class="detail" id="d2"></p>
    <p class="title" id="t2"></p>
    <p class="detail" id="d4"></p>
    <p class="detail detail1" id="d5"></p>
    <p class="detail detail1" id="d6"></p>
    <p class="detail detail1" id="d7"></p>
    <p class="detail detail1" id="d8"></p>
</div>
<script>
    const resources = {
        en: {
            translation: {
                "title": "Blacklist rules page",
                "t1": "How to block other users?",
                "t2": "After you block other users",
                "d1": "You can block other users by the following ways.",
                d2: 'Click the "…." button on the top right corner of the profile page, and then click the "Block" button.',
                d4: '1. All the users you have blocked will appear in the "Blocked List", you can remove the blocked users from the list.',
                d5: '2. All users you have blocked will be restricted from:',
                d6: '- Enter your chatroom.',
                d7: '- Send you messages.',
                d8: '- Follow you.',
            }
        },
        zh: {
            translation: {
                "title": "黑名单规则页",
                "t1": "如何拉黑用户？",
                "t2": "关于拉黑用户后",
                "d1": "您可以通过以下途径拉黑其他用户：",
                d2: '点击个人页右上角“···”按钮，再点击“拉黑”按钮。',
                d4: '1. 所有被您拉黑都有用户都会出现在“黑名单”列表汇总，您可以在“黑名单”列表中把已拉黑的用户移除。',
                d5: '2. 被您拉黑的用户将会被限制：',
                d6: '- 进入您的直播间',
                d7: '- 向您发送私聊消息',
                d8: '- 对您进行关注',
            }
        },
        ar: {
            translation: {
                "title": "صفحة قواعد القائمة السوداء",
                "t1": "كيفية منع المستخدمين الآخرين؟",
                "t2": "بعد حظر المستخدمين الآخرين",
                "d1": "يمكنك حظر المستخدمين الآخرين بالطرق التالية.",
                d2: 'انقر فوق الزر "…." في الزاوية اليمنى العليا من صفحة الملف الشخصي، ثم انقر فوق الزر "حظر".',
                d4: '1. سيظهر جميع المستخدمين الذين قمت بحظرهم في "قائمة المحظورين"، ويمكنك إزالة المستخدمين المحظورين من القائمة.',
                d5: '2. سيتم تقييد كافة المستخدمين الذين قمت بحظرهم من:',
                d6: '- رؤية غرفة الدردشة الخاصة بك والدخول إليها.',
                d7: '- إرسال رسائل مباشرة إليك.',
                d8: '- متابعتك.',
            }
        }
    }

    init(resources, (i18next) => {
        document.getElementById('title').innerHTML = i18next.t('title');
        // 获取所有段落元素
        const paragraphs = document.querySelectorAll('p');
        // 遍历段落元素并打印它们的ID
        paragraphs.forEach(function (paragraph) {
            if (paragraph.id && i18next.exists(paragraph.id)) {
                const value = i18next.t(paragraph.id);
                if (value) {
                    paragraph.innerText = value;
                }
            }
        });
    })
</script>
</body>

</html>

<!-- 读取 URL 参数 -->
<!--<script type="text/javascript">-->
<!--    window.onload = function() {-->
<!--        // 获取 URL 查询参数-->
<!--        const params = new URLSearchParams(window.location.search);-->
<!--        const lang = params.get('themeStyle');-->
<!--        // 根据参数来处理页面逻辑-->
<!--        document.getElementById("xxxx").innerText = lang;-->
<!--    };-->
<!--</script>-->