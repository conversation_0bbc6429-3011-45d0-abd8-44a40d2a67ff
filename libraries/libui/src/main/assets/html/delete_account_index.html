<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注销账号</title>
    <link rel="stylesheet" href="../css/font.css">
    <script src="../js/i18next.min.js"></script>
    <script src="../js/language.js"></script>
    <style type="text/css">
        p {
            margin: 0px;
        }

        .title {
            color: #202530;
            font-size: 16px;
            font-weight: bold;
            margin-top: 15px;
            margin-bottom: 6px;
        }

        .detail {
            color: #202530;
            font-size: 14px;
            font-weight: normal;
            margin-bottom: 3px;
        }

        .warning {
            color: #FF5470;
            font-size: 14px;
            font-weight: normal;
            margin-top: 15px;
            margin-bottom: 3px;
        }

        :root {
            margin-top: 0px;
            margin-bottom: 110px;
            margin-left: 15px;
            margin-right: 15px;
        }
    </style>
</head>

<body>
<div>
    <p class="warning" id="w1"/>
    <p class="title" id="t1"></p>
    <p class="detail" id="d1"></p>
    <p class="title" id="t2"></p>
    <p class="detail" id="d2"></p>
    <p class="title" id="t3"></p>
    <p class="detail" id="d3"></p>
    <p class="title" id="t4"></p>
    <p class="detail" id="d4"></p>
    <p class="warning" id="w2"/>
    <p class="detail" id="d5"></p>
</div>
<script>
    // 初始化 i18next
    const resources = {
        en: {
            translation: {
                "w1": "In order to ensure the security of your account, when submitting a cancellation application, you need to confirm that the following conditions have been met:",
                "t1": "1. The account status is normal",
                "d1": "The account is in normal use, has not been banned, and there have been no sensitive operations such as binding or changing of mobile phone numbers or third-party login methods in the past 30 days;",
                "t2": "2. The account property has been settled or voluntarily given up",
                "d2": "All types of virtual currencies such as diamonds and gold coins under the account have been settled, used or voluntarily given up, and all transactions have been completed or voluntarily given up;",
                "t3": "3. No complaints or business disputes",
                "d3": "There are no disputes with the account, including but not limited to complaints, reports, etc., and the relevant services have been terminated or voluntarily given up;",
                "t4": "4. Account identity",
                "d4": "The account is not the guild leader or family leader",
                "w2": "After the account is canceled, your account will:",
                "d5": "1. If you cannot log in to this APP, all information will be permanently deleted, including but not limited to friend relationships, number of fans, guild information, recharge and consumption records, etc.;\n2. Mobile phone number and third-party binding information will be released. "
            }
        },
        zh: {
            translation: {
                "w1": "为了保证你的账号安全，在提交注销申请时，需要确认已经满足以下条件：",
                "t1": "1、账号状态正常",
                "d1": "账号处于正常使用状态，没有处于被封禁状态，且近30天内没有手机号或第三方登录方式的绑定或换绑等敏感操作；",
                "t2": "2、账号财产已结清或自愿放弃",
                "d2": "账号下的钻石、金币等所有类型虚拟货币均已结清、使用或自愿放弃，所有交易已完成或自愿放弃；",
                "t3": "3、无申诉、业务纠纷",
                "d3": "账号无任何纠纷，包括但不限于投诉、举报等，相关服务已结束或自愿放弃；",
                "t4": "4、账号身份",
                "d4": "账号不是公会长或家族长身份",
                "w2": "账号注销后，你的账号将：",
                "d5": "1、无法登录本APP，所有的信息将被永久删除，包含但不限于好友关系、粉丝数量、公会信息、充值消费记录等；\n2、手机号、第三方绑定信息将会解除，解除后可以绑定到其他账号；\n3、账户资产将被清空，虚拟货币、背包物品、优惠券、VIP等级等资产或特权将会作废无法恢复"
            }
        },
        ar: {
            translation: {
                "w1": "من أجل ضمان أمان حسابك، عند تقديم طلب الإلغاء، يجب عليك التأكد من استيفاء الشروط التالية:",
                "t1": "1. حالة الحساب طبيعية",
                "d1": "الحساب قيد الاستخدام العادي، ولم يتم حظره، ولم تكن هناك عمليات حساسة مثل ربط أو تغيير أرقام الهواتف المحمولة أو طرق تسجيل دخول الطرف الثالث خلال الثلاثين يومًا الماضية؛",
                "t2": "2. تمت تسوية ملكية الحساب أو التنازل عنها طوعًا",
                "d2": "تمت تسوية جميع أنواع العملات الافتراضية مثل الماس والعملات الذهبية الموجودة في الحساب أو استخدامها أو التنازل عنها طوعًا، وتم إكمال جميع المعاملات أو التنازل عنها طوعًا؛",
                "t3": "3. عدم وجود شكاوى أو نزاعات تجارية",
                "d3": "عدم وجود نزاعات مع الحساب، بما في ذلك على سبيل المثال لا الحصر الشكاوى والبلاغات وما إلى ذلك، وتم إنهاء الخدمات ذات الصلة أو التخلي عنها طوعًا؛",
                "t4": "4. هوية الحساب",
                "d4": "الحساب ليس زعيم النقابة أو زعيم الأسرة",
                "w2": "بعد إلغاء الحساب، سيقوم حسابك بما يلي:",
                "d5": "1. إذا لم تتمكن من تسجيل الدخول إلى هذا التطبيق، فسيتم حذف جميع المعلومات نهائيًا، بما في ذلك على سبيل المثال لا الحصر، علاقات الأصدقاء، وعدد المعجبين، ومعلومات النقابة، وسجلات إعادة الشحن والاستهلاك، وما إلى ذلك.\n2. رقم الهاتف المحمول والثالث- سيتم تحرير معلومات ربط الطرف.\n3. سيتم مسح أصول الحساب، وستكون الأصول أو الامتيازات مثل العملة الافتراضية وعناصر حقيبة الظهر والكوبونات ومستويات VIP وما إلى ذلك غير صالحة ولا يمكن استعادتها."
            }
        }
    }

    init(resources, (i18next) => {
        // 获取所有段落元素
        const paragraphs = document.querySelectorAll('p');
        // 遍历段落元素并打印它们的ID
        paragraphs.forEach(function (paragraph) {
            if (paragraph.id && i18next.exists(paragraph.id)) {
                const value = i18next.t(paragraph.id);
                if (value) {
                    paragraph.innerText = value;
                }
            }
        });
    })
</script>
</body>

</html>

<!-- 读取 URL 参数 -->
<!--<script type="text/javascript">-->
<!--    window.onload = function() {-->
<!--        // 获取 URL 查询参数-->
<!--        const params = new URLSearchParams(window.location.search);-->
<!--        const lang = params.get('themeStyle');-->
<!--        // 根据参数来处理页面逻辑-->
<!--        document.getElementById("xxxx").innerText = lang;-->
<!--    };-->
<!--</script>-->