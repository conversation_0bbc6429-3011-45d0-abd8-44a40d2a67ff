<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="title">Help</title>
    <link rel="stylesheet" href="../css/font.css">
    <script src="../js/i18next.min.js"></script>
    <script src="../js/language.js"></script>
    <style type="text/css">
        p {
            margin: 0px;
        }

        .title {
            color: #202530;
            font-size: 16px;
            font-weight: bold;
            margin-top: 0px;
            margin-bottom: 6px;
        }

        .detail {
            color: #202530;
            font-size: 14px;
            font-weight: normal;
            margin-bottom: 6px;
        }

        .warning {
            color: #FF5470;
            font-size: 14px;
            font-weight: normal;
            margin-top: 10px;
            margin-bottom: 6px;
        }

        .detail1 {
            margin-bottom: 9px;
        }


        :root {
            margin-top: 15px;
            margin-bottom: 110px;
            margin-left: 15px;
            margin-right: 15px;
        }

        strong {
            color: #1FB58D;
        }
    </style>
</head>

<body>
<div>
    <p class="title" id="t1"></p>
    <p class="detail" id="d1"/>
    <p class="detail" id="d2"/>
    <p class="detail" id="d3"/>
    <p class="detail" id="d4"/>
    <p class="detail" id="d5"/>
    <p class="warning" id="d6"/>
    <p class="detail" id="d7"/>
</div>
<script>
    const resources = {
        en: {
            translation: {
                "title": "Introduction to special effects setting rules",
                "t1": "Rule introduction content:",
                "d1": "<strong>Gift effects:</strong> After closing, gift animations and sounds will not be played in the room;",
                d2: '<strong>Gift sound:</strong> After closing, the gift sound will not be played in the room;',
                d3: '<strong>Car effects:</strong> After closing, the car animations and sounds will not be played in the room;',
                d4: '<strong>Car sound:</strong> After turning it off, the car sound will not be played in the room;',
                d5: '<strong>World Banner:</strong> After closing, the world banner animation will not be displayed in the room',
                d6: 'Note:',
                d7: 'All switches are on by default. If you encounter lag, you can try to turn off some of the above switches',
            }
        },
        zh: {
            translation: {
                "title": "特效设置规则介绍",
                "t1": "规则介绍内容：",
                "d1": "<strong>礼物特效：</strong>关闭后在房间内将不会播放礼物动效和声音；",
                d2: '<strong>礼物声音：</strong>关闭后在房间内将不会播放礼物声音；',
                d3: '<strong>座驾特效：</strong>关闭后在房间内将不会播放座驾动效和声音；',
                d4: '<strong>座驾声音：</strong>关闭后在房间内将不会播放座驾声音；',
                d5: '<strong>世界横幅：</strong>关闭后在房间内将不会展示世界横幅动效',
                d6: '注：',
                d7: '所有开关默认为开启状态，如果遇到卡顿情况，可以尝试关闭以上部分开关',

            }
        },
        ar: {
            translation: {
                "title": "مقدمة لقواعد إعداد المؤثرات الخاصة",
                "t1": "محتوى مقدمة القاعدة:",
                "d1": "<strong>تأثيرات الهدايا:</strong> بعد الإغلاق، لن يتم تشغيل الرسوم المتحركة والأصوات الخاصة بالهدية في الغرفة؛",
                d2: '<strong>صوت الهدية:</strong> بعد الإغلاق، لن يتم تشغيل صوت الهدية في الغرفة؛',
                d3: '<strong>تأثيرات السيارة:</strong> بعد الإغلاق، لن يتم تشغيل الرسوم المتحركة والأصوات للسيارة في الغرفة؛',
                d4: '<strong>صوت السيارة:</strong> بعد إيقاف تشغيله، لن يتم تشغيل صوت السيارة في الغرفة؛',
                d5: '<strong>الشعار العالمي:</strong> بعد الإغلاق، لن يتم عرض الرسوم المتحركة للشعار العالمي في الغرفة',
                d6: 'ملاحظة:',
                d7: "جميع المفاتيح قيد التشغيل بشكل افتراضي، إذا واجهت تأخيرًا، فيمكنك محاولة إيقاف تشغيل بعض المفاتيح المذكورة أعلاه",
            }
        }
    }

    init(resources, (i18next) => {
        document.getElementById('title').innerHTML = i18next.t('title');
        // 获取所有段落元素
        const paragraphs = document.querySelectorAll('p');
        // 遍历段落元素并打印它们的ID
        paragraphs.forEach(function (paragraph) {
            if (paragraph.id && i18next.exists(paragraph.id)) {
                const value = i18next.t(paragraph.id);
                if (value) {
                    paragraph.innerHTML = value;
                }
            }
        });
    })
</script>
</body>

</html>

<!-- 读取 URL 参数 -->
<!--<script type="text/javascript">-->
<!--    window.onload = function() {-->
<!--        // 获取 URL 查询参数-->
<!--        const params = new URLSearchParams(window.location.search);-->
<!--        const lang = params.get('themeStyle');-->
<!--        // 根据参数来处理页面逻辑-->
<!--        document.getElementById("xxxx").innerText = lang;-->
<!--    };-->
<!--</script>-->