<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>检查网络</title>
    <link rel="stylesheet" href="../css/font.css">
    <script src="../js/i18next.min.js"></script>
    <script src="../js/language.js"></script>
    <style type="text/css">
        :root {
            padding-left: 15px;
            padding-right: 15px;
            background-color: #fff;
        }

        p {
            margin: 0px;
        }

        .normal {
            color: #202530;
            font-size: 15px;
            font-weight: regular;
            margin-bottom: 21px;
        }
    </style>
</head>

<body>
<div>
    <p class="normal" id="p1"/>
    <p class="normal" id="p2"/>
    <p class="normal" id="p3"/>
</div>

<script>
    const resources = {
        en: {
            translation: {
                p1: "<strong>Your device is not enabled for mobile or Wi-Fi networks</strong>",
                p2: "If you need to connect to the Internet, you can refer to the following methods:<br>Select an available Wi-Fi hotspot in the device's <strong>-&quot;Wi-Fi Network&quot;</strong> settings panel to connect.<br>Enable <strong>cellular data</strong> in the device's <strong>&quot;Settings&quot;-&quot;Cellular Network&quot;</strong> settings panel (data communication fees may be charged by the operator after enabling).",
                p3: "If you are already connected to a Wi-Fi network:<br>Please check whether the Wi-Fi hotspot you are connected to is connected to the Internet, or whether the hotspot has allowed your device to access the Internet.",
            }
        },
        zh: {
            translation: {
                p1: "<strong>您的设备未启用移动网络或Wi-Fi网络</strong>",
                p2: "如需要连接到互联网，可以参考以下方法：<br>在设备的<strong>-“Wi-Fi网络”</strong>设置面板中选择一个可用的Wi-Fi热点接入。<br>在设备的<strong>“设置”-“蜂窝网络”</strong>设置面板中启用<strong>蜂窝数据</strong>（启用后运营商可能会收取数据通信费用）。",
                p3: "如果您已接入Wi-Fi网络：<br>请检查您所连接的Wi-Fi热点是否已接入互联网，或该热点是否已允许您的设备访问互联网。",
            }
        },
        ar: {
            translation: {
                p1: "<strong>الشبكات المحمولة أو شبكات Wi-Fi غير مفعلة على جهازك</strong>",
                p2: "إذا كنت بحاجة إلى الاتصال بالإنترنت، يمكنك الرجوع إلى الطرق التالية:<br>حدد نقطة اتصال Wi-Fi متاحة في لوحة إعدادات الجهاز <strong>-&quot;شبكة Wi-Fi&quot;</strong> للاتصال.<br>قم بتمكين <strong>البيانات الخلوية</strong> في لوحة إعدادات الجهاز <strong>-&quot;الشبكة الخلوية&quot;</strong> (قد يتم فرض رسوم على اتصالات البيانات من قبل المشغل بعد التمكين).",
                p3: "إذا كنت متصلاً بشبكة Wi-Fi:<br>يرجى التحقق مما إذا كانت نقطة اتصال Wi-Fi التي تتصل بها متصلة بالإنترنت، أو ما إذا كانت نقطة الاتصال قد سمحت لجهازك بالوصول إلى الإنترنت.",
            }
        }
    }
    init(resources, (i18next) => {
        // 获取所有段落元素
        const paragraphs = document.querySelectorAll('p');
        if (paragraphs == null) {
            return
        }
        // 遍历段落元素并打印它们的ID
        paragraphs.forEach((paragraph) => {
            const paragraphId = paragraph.id;
            if (paragraphId == null || !i18next.exists(paragraphId)) {
                return
            }
            const value = i18next.t(paragraph.id);
            paragraph.innerHTML = value;
        })
    })
</script>
</body>

</html>