<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注销账号</title>
    <link rel="stylesheet" href="../css/font.css">
    <script src="../js/i18next.min.js"></script>
    <script src="../js/language.js"></script>
    <style type="text/css">
        p {
            margin: 0px;
        }

        :root {
            padding-left: 15px;
            padding-right: 15px;
            padding-bottom: 110px;
        }

        .top {
            margin-top: 15;
            margin-bottom: 18px;
            height: 122px;
            border-radius: 15px;
            background-color: #F7F8FA;
        }

        .title {
            color: #FF5470;
            font-size: 24px;
            font-weight: bold;
            padding-top: 21px;
            width: 100%;
            text-align: center;
        }

        .topD1 {
            color: #4E5969;
            font-size: 13px;
            width: 100%;
            text-align: center;
        }

        .topD2 {
            margin-top: 18px;
        }

        .detail {
            color: #202530;
            font-size: 14px;
            font-weight: normal;
            margin-bottom: 9px;
        }

        strong {
            font-size: 15px;
            font-weight: bold;
            color: #1FB58D;
        }
    </style>
</head>

<body>
<div>
    <div class="top">
        <p class="title" id="dayCount">8</p>
        <p class="topD1" id="t1"></p>
        <p class="topD2 topD1" id="t2"></p>
    </div>
    <p class="detail" id="d1"></p>
    <p class="detail" id="d2"></p>
    <p class="detail" id="d3"></p>
</div>
<script>
    const resources = {
        en: {
            translation: {
                "t1": "Day(s)",
                "t2": "Remaining review time",
                "d1": "You have successfully initiated a cancellation application. We will review your account information and cancellation application as soon as possible. The review time is expected to take <strong>10 days;</strong>",
                "d2": "After passing the review, you can go to this page and click the [Confirm Logout] button to complete this logout;",
                "d3": "During the review period, you can still log in to your account to use our services, or you can cancel this cancellation by clicking the [Cancel Application] button",
            }
        },
        zh: {
            translation: {
                "t1": "天",
                "t2": "剩余审核时间",
                "d1": "您已经成功发起注销申请，我们将尽快对您的账号信息以及注销申请进行审核，审核时长预计需要<strong>10天；</strong>",
                "d2": "审核通过后，您可以到此页面内点击【确认注销】按钮，完成本次注销；",
                "d3": "在审核期间，您仍然可以登录账号使用我们的服务，也可以通过点击【取消申请】按钮，取消本次注销",
            }
        },
        ar: {
            translation: {
                "t1": "اليوم",
                "t2": "الوقت المتبقي الحالي للمراجعة",
                "d1": "لقد قمت ببدء طلب الإلغاء بنجاح. سوف نقوم بمراجعة معلومات حسابك وطلب الإلغاء في أقرب وقت ممكن. ومن المتوقع أن يستغرق وقت المراجعة <strong>10 أيام؛</strong>",
                "d2": "بعد اجتياز المراجعة، يمكنك الذهاب إلى هذه الصفحة والنقر على زر [تأكيد الخروج] لإكمال تسجيل الخروج هذا؛",
                "d3": "خلال فترة المراجعة، لا يزال بإمكانك تسجيل الدخول إلى حسابك لاستخدام خدماتنا، أو يمكنك إلغاء هذا الإلغاء بالضغط على زر [إلغاء الطلب]",
            }
        }
    }

    init(resources, (i18next) => {
        // 获取所有段落元素
        const paragraphs = document.querySelectorAll('p');
        // 遍历段落元素并打印它们的ID
        paragraphs.forEach(function (paragraph) {
            if (paragraph.id && i18next.exists(paragraph.id)) {
                const value = i18next.t(paragraph.id);
                if (value) {
                    paragraph.innerHTML = value;
                }
            }
        });

        // const spans = document.querySelectorAll('span');
        // // 遍历段落元素并打印它们的ID
        // spans.forEach(function(paragraph) {
        //     if (paragraph.id && i18next.exists(paragraph.id)) {
        //         const value = i18next.t(paragraph.id);
        //         if (value) {
        //             paragraph.innerHTML = value;
        //         }
        //     }
        // });
    })
</script>
</body>

</html>

<!-- 读取 URL 参数 -->
<script type="text/javascript">
    window.onload = function () {
        // 获取 URL 查询参数
        const params = new URLSearchParams(window.location.search);
        const dayCount = params.get('dayCount');
        // 根据参数来处理页面逻辑
        document.getElementById("dayCount").innerText = dayCount;
    };
</script>