<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="title">Help</title>
    <link rel="stylesheet" href="../css/font.css">
    <script src="../js/i18next.min.js"></script>
    <script src="../js/language.js"></script>
    <style type="text/css">
        p {
            margin: 0px;
        }

        .title {
            color: #202530;
            font-size: 16px;
            font-weight: bold;
            margin-top: 0px;
            margin-bottom: 6px;
        }

        .detail {
            color: #202530;
            font-size: 14px;
            font-weight: normal;
            margin-bottom: 6px;
        }

        .warning {
            color: #FF5470;
            font-size: 14px;
            font-weight: normal;
            margin-top: 10px;
            margin-bottom: 6px;
        }

        .detail1 {
            margin-bottom: 9px;
        }


        :root {
            margin-top: 0px;
            margin-bottom: 110px;
            margin-left: 15px;
            margin-right: 15px;
        }

        strong {
            color: #1FB58D;
        }
    </style>
</head>

<body>
<div>
    <p class="title" id="t1"></p>
    <p class="detail" id="d1"/>
    <p class="detail" id="d2"/>
    <p class="detail" id="d3"/>
    <p class="detail" id="d4"/>
    <p class="detail" id="d5"/>
    <p class="detail" id="d6"/>
    <p class="detail" id="d7"/>
</div>
<script>
    const resources = {
        en: {
            translation: {
                t1: "Rules:",
                d1: "<b>1.</b> Party themes create an atmosphere for Laya users in room.",
                d2: '<b>2.</b> You can unlock themes with diamonds <strong>(2hours per unlock)</strong>. The cost of each unlock varies based on the number of unlocks made within a day.',
                d3: '<b>3.</b> You can toggle the number of mic seats when themes are in use.',
                d4: '<b>4.</b> You can switch your wallpaper when themes are in use.',
                d5: '<b>5.</b> You can renew themes in use with diamonds. ',
                d6: '<b>6.</b> Since you close the current theme, the remaining use duration will be reset. You need to purchase it again if you need to use it again.',
                d7: '<b>7.</b> If you switch to a special theme in your package while a store theme is in use, the store theme will be disabled and needs to be repurchased.',
            }
        },
        zh: {
            translation: {
                t1: "规则:",
                d1: "<b>1.</b> Party themes create an atmosphere for Laya users in room.",
                d2: '<b>2.</b> You can unlock themes with diamonds <strong>(2hours per unlock)</strong>. The cost of each unlock varies based on the number of unlocks made within a day.',
                d3: '<b>3.</b> You can toggle the number of mic seats when themes are in use.',
                d4: '<b>4.</b> You can switch your wallpaper when themes are in use.',
                d5: '<b>5.</b> You can renew themes in use with diamonds. ',
                d6: '<b>6.</b> Since you close the current theme, the remaining use duration will be reset. You need to purchase it again if you need to use it again.',
                d7: '<b>7.</b> If you switch to a special theme in your package while a store theme is in use, the store theme will be disabled and needs to be repurchased.',
            }
        },
        ar: {
            translation: {
                t1: "القواعد:",
                d1: "<b>1.</b> تخلق سمات الحفلة جوًا لمستخدمي Laya في الغرفة.",
                d2: '<b>2.</b> يمكنك فتح السمات بالماس <strong>(ساعتان لكل فتح)</strong>. تختلف تكلفة كل عملية إلغاء قفل بناءً على عدد عمليات إلغاء القفل التي تم إجراؤها خلال يوم واحد.',
                d3: '<b>3.</b> يمكنك تبديل عدد مقاعد الميكروفون عندما تكون السمات قيد الاستخدام.',
                d4: '<b>4.</b> يمكنك تبديل الخلفية عندما تكون السمات قيد الاستخدام.',
                d5: '<b>5.</b> يمكنك تجديد السمات المستخدمة مع الماس. ',
                d6: '<b>6.</b> نظرًا لإغلاق السمة الحالية، سيتم إعادة تعيين مدة الاستخدام المتبقية. يتعين عليك شرائه مرة أخرى إذا كنت بحاجة إلى استخدامه مرة أخرى.',
                d7: '<b>7.</b> إذا قمت بالتبديل إلى سمة خاصة في الحزمة الخاصة بك أثناء استخدام سمة المتجر، فسيتم تعطيل سمة المتجر ويجب إعادة شراؤها.',
            }
        }
    }

    init(resources, (i18next) => {
        document.getElementById('title').innerHTML = i18next.t('title');
        // 获取所有段落元素
        const paragraphs = document.querySelectorAll('p');
        // 遍历段落元素并打印它们的ID
        paragraphs.forEach(function (paragraph) {
            if (paragraph.id && i18next.exists(paragraph.id)) {
                const value = i18next.t(paragraph.id);
                if (value) {
                    paragraph.innerHTML = value;
                }
            }
        });
    })
</script>
</body>

</html>

<!-- 读取 URL 参数 -->
<!--<script type="text/javascript">-->
<!--    window.onload = function() {-->
<!--        // 获取 URL 查询参数-->
<!--        const params = new URLSearchParams(window.location.search);-->
<!--        const lang = params.get('themeStyle');-->
<!--        // 根据参数来处理页面逻辑-->
<!--        document.getElementById("xxxx").innerText = lang;-->
<!--    };-->
<!--</script>-->