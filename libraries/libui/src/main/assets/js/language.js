// create a function to get language from ua, if ua language not found, use navigator.language
function getLanguage() {
    const ua = navigator.userAgent;
    // lan/en
    const language = ua.match(/lan\/([a-z]{2})/)?.[1];
    if (language) {
        return language;
    }
    return navigator.language;
}

function init(resources, onContentChanged) {
    // 初始化 i18next
    i18next.init({
        fallbackLng: 'en',
        lng: getLanguage(), // 默认语言
        // debug: true,
        resources: resources
    }, (err, t) => {
        console.log('i18next初始化成功 => ', err, t);
        // 初始渲染
        updateContent();
    });

    let currentLang = getLanguage();
    // 监听浏览器语言变化
    setInterval(() => {
        if (currentLang !== getLanguage()) {
            console.log('浏览器语言发生变化 => prev: ', currentLang, 'current: ', getLanguage());
            currentLang = getLanguage();
            updateContent();
        }
    }, 1000);

    // 更新页面内容
    function updateContent() {
        const newLang = getLanguage();
        i18next.changeLanguage(newLang);
        if (newLang.startsWith('ar')) {
            document.documentElement.dir = 'rtl';
        } else {
            document.documentElement.setAttribute("dir", "ltr")
        }
        onContentChanged?.(i18next)
    }
}

window.init = init;