package com.layaa.libui.base

import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AppCompatDelegate
import androidx.appcompat.app.SkinAppCompatDelegateImpl
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.ViewModelProvider
import androidx.viewbinding.ViewBinding
import com.layaa.language.LanguageController
import com.layaa.libui.R
import com.layaa.libui.log.LogUtils
import com.layaa.libui.utils.GenericTypeUtils
import com.layaa.libui.utils.UIUtils
import com.layaa.libui.widget.LoadingDialog
import com.layaa.libutils.NetUtils
import com.layaa.libutils.StatusBarUtil
import com.layaa.libutils.net.NetNotice
import com.layaa.libutils.net.NetNoticeCentre
import com.layaa.skinlib.Skinable
import com.layaa.widget.SimpleViewStubProxy
import com.layaa.widget.basepage.NetWeakViewProvider
import com.layaa.widget.mvvm.BaseViewModel
import java.util.Locale

@Skinable
abstract class BaseBindingVMActivity<VM : BaseViewModel, VB : ViewBinding> :
    AppCompatActivity(), NetNotice {

    protected var TAG = javaClass.getSimpleName()

    protected var netWeakPanel: SimpleViewStubProxy<View>? = null

    protected val viewModel: VM by lazy {
        ViewModelProvider(this).get(GenericTypeUtils.getTypeCls(this, 0))
    }

    lateinit var binding: VB

    abstract fun inflateLayout(layoutInflater: LayoutInflater): VB

    protected var tag: String? = null
    private var mShowLoading: LoadingDialog? = null

    protected open fun initData() {

    }

    protected open fun initView() {

    }

    protected open fun initEvent() {

    }

    override fun getDelegate(): AppCompatDelegate {
        return SkinAppCompatDelegateImpl.get(this, this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        tag = javaClass.getSimpleName()
        initStatusBar()
        setContentView(getLayoutView())
        initData()
        initNavigationBar()
        getWindowBackgroundColor()?.let {
            window.decorView.setBackgroundColor(it)
        }
        initView()
        initEvent()

        if (!NetUtils.isNetworkAvailable()) {
            showNetWeakNotice()
        }
        NetNoticeCentre.getInstance().addNotice(this)
    }

    open fun isShowNetWeakView(): Boolean {
        return true
    }

    override fun setContentView(view: View) {
        if (isShowNetWeakView() && NetWeakViewProvider.getNetWeakView != null) {
            val baseView = getBaseView()
            baseView.addView(view)
            val networkView = NetWeakViewProvider.getNetWeakView?.invoke(this);
            netWeakPanel = SimpleViewStubProxy<View>(networkView);
            baseView.addView(networkView);
            super.setContentView(baseView)
        } else {
            super.setContentView(view)
        }
    }

    open fun getBaseView(): ViewGroup {
        val baseView: ViewGroup = FrameLayout(this)
        baseView.setLayoutParams(
            ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
        )
        return baseView
    }

    fun showNetWeakNotice() {
        netWeakPanel?.visibility = View.VISIBLE
    }

    open fun dismissNetWeakNotice() {
        netWeakPanel?.visibility = View.GONE
    }

    open fun getWindowBackgroundColor(): Int? {
        return UIUtils.getColor(R.color.bg_activity_color)
    }

    protected override fun onDestroy() {
        super.onDestroy()
        dismissLoading()
        swapActivityWithApplicationContext()
    }


    /**
     * 三星剪切板 内存泄漏
     * https://gist.github.com/jankovd/a210460b814c04d500eb12025902d60d
     * https://github.com/square/leakcanary/issues/762
     */
    private fun swapActivityWithApplicationContext() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && Build.MANUFACTURER.lowercase(
                    Locale.getDefault()
                ) == "samsung"
            ) {
                val semClipboardManager =
                    Class.forName("com.samsung.android.content.clipboard.SemClipboardManager")
                val sInstanceField = semClipboardManager.getDeclaredField("sInstance")
                sInstanceField.isAccessible = true
                val sInstance = sInstanceField[null]
                val mContextField = semClipboardManager.getDeclaredField("mContext")
                mContextField.isAccessible = true
                mContextField[sInstance] = application
            } else {
                val semEmergencyManagerClass =
                    Class.forName("com.samsung.android.emergencymode.SemEmergencyManager")
                val sInstanceField = semEmergencyManagerClass.getDeclaredField("sInstance")
                sInstanceField.isAccessible = true
                val sInstance = sInstanceField[null]
                val mContextField = semEmergencyManagerClass.getDeclaredField("mContext")
                mContextField.isAccessible = true
                mContextField[sInstance] = application
            }
        } catch (ignore: Throwable) {

        }
    }


    protected override fun onSaveInstanceState(outState: Bundle) {
        LogUtils.i("onSaveInstanceState$outState")
        //防止fragment崩溃
        try {
            super.onSaveInstanceState(outState)
        } catch (e: Exception) {
            LogUtils.e(e)
        }
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        try {
            //clear viewHierarchyState 不需要系统恢复
            savedInstanceState.remove("android:viewHierarchyState")
            super.onRestoreInstanceState(savedInstanceState)
        } catch (t: Throwable) {
            LogUtils.e(t)
        }
    }

    fun showLoading() {
        if (mShowLoading == null) {
            mShowLoading = LoadingDialog(this)
        }
        mShowLoading?.show()
    }

    fun dismissLoading() {
        mShowLoading?.dismiss()
    }

    fun isStatusTrans(): Boolean {
        return true
    }

    private fun initNavigationBar() {
        setNavigationBarColor(getNavigationBarColor())
        if (!isNavigationBarHide()) {
            return
        }
        val window = getWindow()
        if (window == null) {
            return
        }
        window.decorView.systemUiVisibility =
            window.decorView.systemUiVisibility or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
        val offsetView = findViewById<View?>(navigationBarOffsetView())
        if (offsetView == null) {
            return
        }
        offsetView(offsetView)
    }

    protected fun offsetView(offsetView: View) {
        ViewCompat.setOnApplyWindowInsetsListener(offsetView) { v: View, insets: WindowInsetsCompat ->
            val bars = insets.getInsets(WindowInsetsCompat.Type.navigationBars())
            v.setPadding(0, 0, 0, bars.bottom)
            StatusBarUtil.setInsetsNavigationBarHeight(bars.bottom)
            insets
        }
    }

    protected open fun navigationBarOffsetView(): Int {
        return 0
    }

    protected open fun getNavigationBarColor(): Int {
        if (isNavigationBarHide()) {
            return Color.TRANSPARENT
        } else {
            return ContextCompat.getColor(this, R.color.bg_activity_color)
        }
    }

    open fun isStatusBarFontDarkColor(): Boolean {
        return false
    }

    protected open fun isNavigationBarHide(): Boolean {
        return false
    }

    protected open fun setBackgroundColor(color: Int) {
        window.decorView.setBackgroundColor(color)
    }

    private fun initStatusBar() {
        setNavigationBarColor(getNavigationBarColor())
        if (isStatusTrans()) {
            StatusBarUtil.setTranslucent(this, 0)
            if (isStatusBarFontDarkColor()) {
                StatusBarUtil.setStatusBarLightMode(this, true)
            }
        } else {
            StatusBarUtil.setColor(this, Color.WHITE)
        }
    }

    protected fun setNavigationBarColor(color: Int) {
        window?.navigationBarColor = color
    }

    override fun attachBaseContext(newBase: Context) {
        super.attachBaseContext(LanguageController.attachBaseContext(newBase))
    }

    override fun getResources(): Resources {
        val res = super.getResources()
        val config = res.configuration
        config.fontScale = 1f
        res.updateConfiguration(config, res.displayMetrics)
        return res
    }

    private fun getLayoutView(): View {
        binding = inflateLayout(layoutInflater)
        return binding.root
    }

    override fun netChange(available: Boolean) {
        if (available) {
            dismissNetWeakNotice()
        } else {
            showNetWeakNotice()
        }
    }
}