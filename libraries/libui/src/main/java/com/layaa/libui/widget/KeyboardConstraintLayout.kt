package com.layaa.libui.widget

import android.app.Activity
import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import com.layaa.libui.utils.UIUtils
import com.layaa.libutils.StatusBarUtil

class KeyboardConstraintLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : ConstraintLayout(context, attrs), OnGlobalLayoutListener, LifecycleObserver {

    private var screenHeight = 0
    private var mIsSoftKeyboardShowing = false
    private var context: Context? = null
    private var onKeyboardListener: OnKeyboardListener? = null

    init {
        this.screenHeight = UIUtils.getScreenHeight()
        this.mIsSoftKeyboardShowing = false
        this.context = context
        addLifecycleObserver()
    }

    override fun onGlobalLayout() {
        val context = this.getContext()
        var activity: Activity? = null
        if (context is Activity) {
            activity = context
        }

        if (activity != null && !activity.isFinishing) {
            val r = Rect()
            activity.window.decorView.getWindowVisibleDisplayFrame(r)
            val navigationBar: Int = StatusBarUtil.getHeightOfNavigationBar(context)
            var heightDifference: Int
            if (navigationBar == 0) {
                heightDifference = this.screenHeight - r.bottom
                if (StatusBarUtil.isNavigationBarExist(activity)) {
                    heightDifference -= StatusBarUtil.getNavigationHeight(activity)
                }
            } else {
                val heightSubScreen: Int = StatusBarUtil.getHeightOfNavigationBar(context)
                val navigationHeight: Int = StatusBarUtil.getNavigationHeight(context)
                if (navigationHeight != 0 && heightSubScreen == navigationHeight) {
                    heightDifference =
                        this.screenHeight - r.bottom - StatusBarUtil.getNavigationHeight(context)
                } else {
                    heightDifference = this.screenHeight - r.bottom - (heightSubScreen - r.top)
                }
            }

            val isKeyboardShowing = heightDifference > this.screenHeight / 4
            val keyboardChange =
                this.mIsSoftKeyboardShowing && !isKeyboardShowing || !this.mIsSoftKeyboardShowing && isKeyboardShowing
            if (keyboardChange) {
                onKeyboardListener?.onChange(isKeyboardShowing, heightDifference)
                this.mIsSoftKeyboardShowing = isKeyboardShowing
            }
        }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
    private fun onResume() {
        this.viewTreeObserver.addOnGlobalLayoutListener(this)
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_PAUSE)
    private fun onPause() {
        this.viewTreeObserver.removeOnGlobalLayoutListener(this)
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    private fun onDestroy() {
        this.removeLifecycleObserver()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        this.removeLifecycleObserver()
        this.viewTreeObserver.removeOnGlobalLayoutListener(this)
        this.onKeyboardListener = null
    }

    fun setOnKeyboardListener(onKeyboardListener: OnKeyboardListener?) {
        this.onKeyboardListener = onKeyboardListener
    }

    private fun addLifecycleObserver() {
        if (context is AppCompatActivity) {
            val activity = context as AppCompatActivity
            activity.lifecycle.addObserver(this)
        }
    }

    private fun removeLifecycleObserver() {
        if (context is AppCompatActivity) {
            val activity = context as AppCompatActivity
            activity.lifecycle.removeObserver(this)
        }
    }

    interface OnKeyboardListener {
        fun onChange(showKeyboard: Boolean, height: Int)
    }
}