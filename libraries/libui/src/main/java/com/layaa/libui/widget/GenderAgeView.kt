package com.layaa.libui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.Gravity
import android.view.ViewGroup.LayoutParams
import com.layaa.libui.R
import com.layaa.libui.dp
import com.layaa.libui.utils.TypefaceHelper
import com.layaa.libui.utils.UIUtils
import com.layaa.widget.GenderAgeView

/**
 * Created by <PERSON> on 24/11/29_Fri
 */
class GenderAgeView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : GenderAgeView(context, attrs, defStyleAttr) {

    init {
        this.typeface = TypefaceHelper.getSemiBold()
        setPadding(3.dp, 0, 3.dp, 0)
        genderSize = 12.dp
        iconMale = R.drawable.icon_gender_male
        iconFemale = R.drawable.icon_gender_female
        bgMale = R.drawable.bg_gender_male
        bgFemale = R.drawable.bg_gender_female
        height = LayoutParams.WRAP_CONTENT
        gravity = Gravity.CENTER
        minHeight = UIUtils.getPixels(15f)
    }

}