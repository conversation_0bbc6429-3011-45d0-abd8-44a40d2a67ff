package com.layaa.libui.widget

import android.content.Context
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Shader
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import com.layaa.libui.databinding.LayoutMainTabBinding
import androidx.core.graphics.toColorInt
import com.layaa.libui.R
import androidx.core.content.withStyledAttributes

class MainTabView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var binding: LayoutMainTabBinding =
        LayoutMainTabBinding.inflate(LayoutInflater.from(context), this)

    private val tabTextShader = LinearGradient(
        0F, 0F, 0F, binding.tabText.textSize,
        intArrayOf(
            Color.WHITE,
            "#ECCF5F".toColorInt(),
            "#FFFAA5".toColorInt(),
            "#EAC553".toColorInt()
        ),
        floatArrayOf(0.20F, 0.4F, 0.8F, 0.9F), Shader.TileMode.CLAMP
    )

    init {
        context.withStyledAttributes(attrs, R.styleable.MainTabView) {
            val title = getString(R.styleable.MainTabView_title) ?: ""
            val icon = getResourceId(R.styleable.MainTabView_icon, 0)
            setText(title)
            setImage(icon)
        }
    }

    fun setText(text: String) {
        binding.tabText.text = text
    }

    fun setImage(resId: Int) {
        binding.tabImage.setImageResource(resId)
    }

    override fun setSelected(selected: Boolean) {
        super.setSelected(selected)
        binding.tabText.isSelected = selected
        binding.tabImage.isSelected = selected

        if (selected) {
            binding.tabText.setTextColor("#FFFFEA9B".toColorInt())
            binding.tabText.paint.shader = tabTextShader
        } else {
            binding.tabText.setTextColor("#4DFFFFFF".toColorInt())
            binding.tabText.paint.shader = null
        }
    }


    fun setUnreadCount(count: Int) {
        if (count == 0) {
            binding.unread.visibility = GONE
        } else {
            binding.unread.visibility = VISIBLE
            binding.unread.text = if (count > 99) {
                "99+"
            } else {
                count.toString()
            }
        }
    }
}