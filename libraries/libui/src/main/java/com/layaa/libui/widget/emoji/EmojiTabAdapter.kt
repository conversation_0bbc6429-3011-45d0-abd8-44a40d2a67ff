package com.layaa.libui.widget.emoji

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.layaa.emote.widget.LongPressListener
import com.layaa.emote.widget.ShortPressListener
import com.layaa.language.LanguageController
import com.layaa.libui.R
import com.layaa.libui.databinding.EmojiItemPagerBinding
import com.layaa.libui.widget.base.BaseAdapter
import com.layaa.libui.widget.base.BaseViewHolder

/**
 * Created by <PERSON> on 24/12/10_Tue
 */
class EmojiTabAdapter : BaseAdapter<EmojiTabBean>() {

    var onLockButtonClick: ((EmojiTabBean) -> Unit)? = null

    var shortPressListener: ShortPressListener? = null

    var longPressListener: LongPressListener? = null

    override fun getViewHolder(viewType: Int, parent: ViewGroup): BaseViewHolder<EmojiTabBean> {
        return EmojiTabViewHolder(
            EmojiItemPagerBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )
    }

    inner class EmojiTabViewHolder(private val binding: EmojiItemPagerBinding) :
        BaseViewHolder<EmojiTabBean>(binding.root) {

        override fun update(bean: EmojiTabBean) {
            when (bean.type) {
                EmojiTabBean.Type.NORMAL -> {
                    binding.emojiTabView.loadNormalTab()
                }

                EmojiTabBean.Type.NORMAL_WITH_GUESSING -> {
                    binding.emojiTabView.loadNormalWithGuessingTab()
                }

                EmojiTabBean.Type.ZD -> {
                    binding.emojiTabView.loadZDTab()
                    if (bean.isActive().not()) {
                        binding.txtLock.text =
                            LanguageController.getInstance().getString(R.string.vip_page_sticker_popup)
                        binding.txtLockButton.text = LanguageController.getInstance()
                            .getString(R.string.emoji_lock_vip_button)
                    }
                }
            }
            binding.layoutLock.isVisible = bean.isActive().not()
            binding.txtLockButton.tag = bean
            binding.txtLockButton.setOnClickListener {
                val b = it.tag as EmojiTabBean
                onLockButtonClick?.invoke(b)
            }
            binding.emojiTabView.shortPressListener = shortPressListener
            binding.emojiTabView.longPressListener = longPressListener
        }

    }
}