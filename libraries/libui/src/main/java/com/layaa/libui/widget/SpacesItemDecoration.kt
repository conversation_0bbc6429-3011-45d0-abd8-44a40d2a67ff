package com.layaa.libui.widget

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.layaa.skinlib.SkinKit

/**
 * Created by <PERSON> on 2024/11/08.
 */
class SpacesItemDecoration @JvmOverloads constructor(
    private val top: Int = 0,
    private val start: Int = 0,
    private val end: Int = 0,
    private val bottom: Int = 0,
) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        outRect.top = top
        if (SkinKit.getInstance().isRtl) {
            outRect.left = end
            outRect.right = start
        } else {
            outRect.left = start
            outRect.right = end
        }
        outRect.bottom = bottom
    }
}