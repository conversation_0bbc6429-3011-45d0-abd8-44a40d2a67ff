package com.layaa.libui.widget;

import android.content.Context;
import android.util.AttributeSet;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * <AUTHOR>
 * @date 2020/4/21
 * @des
 **/
public class LinearLayoutManagerWrapper extends LinearLayoutManager {

    private boolean isCanScroll = true;

    public LinearLayoutManagerWrapper(Context context) {
        super(context);
    }

    public LinearLayoutManagerWrapper(Context context, int orientation, boolean reverseLayout) {
        super(context, orientation, reverseLayout);
    }

    public LinearLayoutManagerWrapper(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    //防止 Inconsistency detected. Invalid item position 36(offset:36).state:102
    //这个问题的终极解决方案还是得让google去修复暂时try catch
    @Override
    public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
        try {
            super.onLayoutChildren(recycler, state);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean canScrollHorizontally() {
        if (!isCanScroll) {
            return false;
        }
        return super.canScrollHorizontally();
    }

    @Override
    public boolean canScrollVertically() {
        if (!isCanScroll) {
            return false;
        }
        return super.canScrollVertically();
    }

    public void setCanScroll(boolean canScroll) {
        isCanScroll = canScroll;
    }

    //修改recyclerview 滚动速率
   /* @Override
    public void smoothScrollToPosition(RecyclerView recyclerView,
                                       RecyclerView.State state, final int position) {

        LinearSmoothScroller smoothScroller =
                new LinearSmoothScroller(recyclerView.getContext()) {
                    // 返回：滑过1px时经历的时间(ms)。
                    @Override
                    protected float calculateSpeedPerPixel(DisplayMetrics displayMetrics) {
                        return 150f / displayMetrics.densityDpi;
                    }
                };

        smoothScroller.setTargetPosition(position);
        startSmoothScroll(smoothScroller);
    }*/
}
