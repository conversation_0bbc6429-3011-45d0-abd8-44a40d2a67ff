package com.layaa.libui.widget

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import androidx.viewbinding.ViewBinding
import com.layaa.libui.R
import com.layaa.libutils.module_log.LogUtils

/**
 * Created by <PERSON> on 2024/10/15
 */
abstract class BindingPopupWindow<VB : ViewBinding>(private val context: Context) :
    PopupWindow(context) {

    protected lateinit var binding: VB

    abstract fun initBinding(
        inflater: LayoutInflater,
        parent: ViewGroup?,
        attachToParent: Boolean
    ): VB

    protected open fun init() {
        height = ViewGroup.LayoutParams.WRAP_CONTENT
        width = ViewGroup.LayoutParams.WRAP_CONTENT
        isOutsideTouchable = true
        isFocusable = true
        isTouchable = true
        setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        animationStyle = R.style.popup_animation

        binding = initBinding(LayoutInflater.from(context), null, false)
        setContentView(binding.root)
    }

    override fun showAtLocation(parent: View?, gravity: Int, x: Int, y: Int) {
        try {
            super.showAtLocation(parent, gravity, x, y)
        } catch (e: Exception) {
            LogUtils.e(e.toString())
        }
    }

    override fun dismiss() {
        try {
            super.dismiss()
        } catch (e: Exception) {
            LogUtils.e(e.toString())
        }
    }
}