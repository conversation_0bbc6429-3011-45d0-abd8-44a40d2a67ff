package com.layaa.libui.widget

import android.content.Context
import android.text.SpannableString
import android.text.Spanned
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import com.layaa.libui.CenterImageSpan
import com.layaa.libui.R
import com.layaa.libui.dp
import com.layaa.libui.utils.UIUtils

class EditTextClearView(context: Context, attrs: AttributeSet?) : ConstraintLayout(context, attrs) {

    private var clearIv: ImageView? = null
    private var editText: EditText? = null
    var inputListener: ((String) -> Unit)? = null
    var serchListener: ((String) -> Unit)? = null
    val content: String?
        get() {
            return editText?.text?.toString()
        }

    init {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.EditTextClearView)
        val isClear = typedArray.getBoolean(R.styleable.EditTextClearView_isClear, true)
        val editBg =
            typedArray.getResourceId(R.styleable.EditTextClearView_editBg, R.drawable.bg_21_f7f8fa)
        val textColor =
            typedArray.getResourceId(R.styleable.EditTextClearView_textColor, R.color.color_202530)
        val hintColor =
            typedArray.getResourceId(R.styleable.EditTextClearView_hintColor, R.color.color_86909C)
        val hintText =
            typedArray.getResourceId(R.styleable.EditTextClearView_hintText, R.string.login_profile_popup_nickname_title)
        val textMaxLength = typedArray.getInt(R.styleable.EditTextClearView_maxLength, 0)
        val isNumber = typedArray.getBoolean(R.styleable.EditTextClearView_isNumber, false)
        val mTextSize =
            typedArray.getDimensionPixelSize(R.styleable.EditTextClearView_textSize, 14.dp)
                .toFloat()
        val isSearchKeyBord =
            typedArray.getBoolean(R.styleable.EditTextClearView_supportSearch, false)
        typedArray.recycle()

        val editParams = LayoutParams(0, LayoutParams.MATCH_PARENT).apply {
            startToStart = LayoutParams.PARENT_ID
            topToTop = LayoutParams.PARENT_ID
            bottomToBottom = LayoutParams.PARENT_ID
            endToEnd = LayoutParams.PARENT_ID
        }

        editText = EditText(context).apply {
            layoutParams = editParams
            setBackgroundResource(editBg)
            setPadding(UIUtils.getPixels(15f), 0, UIUtils.getPixels(15f), 0)
            setTextColor(context.resources.getColor(textColor))
            setHintTextColor(context.resources.getColor(hintColor))
            if (isSearchKeyBord) {
                val searchIconSpan =
                    CenterImageSpan(
                        context,
                        R.drawable.room_icon_searchedit
                    )
                val hintSpan =
                    SpannableString("   " + UIUtils.getString(hintText))
                hintSpan.setSpan(searchIconSpan, 0, 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
                setHint(hintSpan)
            } else {
                setHint(hintText)
            }
            setTextSize(TypedValue.COMPLEX_UNIT_PX, mTextSize)
            maxLines = 1
            isSingleLine = true
            gravity = Gravity.CENTER_VERTICAL
//            typeface = TypefaceHelper.getRegular()
            if (isNumber) {
                inputType = android.text.InputType.TYPE_CLASS_NUMBER
            }
            if (textMaxLength > 0) {
                filters = arrayOf(android.text.InputFilter.LengthFilter(textMaxLength))
            }

        }
        if (isSearchKeyBord) {
            editText?.imeOptions = EditorInfo.IME_ACTION_SEARCH
            editText?.setOnEditorActionListener { textView, actionId, keyEvent ->
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    // 处理搜索键的点击事件
                    val query = editText?.text.toString()
                    // 在这里执行搜索操作
                    serchListener?.invoke(query)
                    true
                } else {
                    false
                }
            }
        }
        if (isClear) {
            editText?.setPadding(UIUtils.getPixels(15f), 0, UIUtils.getPixels(48f), 0)

            val params = LayoutParams(UIUtils.getPixels(18f), UIUtils.getPixels(18f)).apply {
                endToEnd = LayoutParams.PARENT_ID
                topToTop = LayoutParams.PARENT_ID
                bottomToBottom = LayoutParams.PARENT_ID
                marginEnd = UIUtils.getPixels(15f)
            }

            clearIv = ImageView(context).apply {
                layoutParams = params
                setImageResource(R.drawable.room_icon_edit_clear)
            }

            clearIv?.setOnClickListener {
                editText?.setText("")
            }
        }

        clearIv?.isVisible = false

        editText?.doAfterTextChanged {
            if (isClear) {
                clearIv?.isVisible = !it.isNullOrEmpty()
            }
            inputListener?.invoke(it.toString())
        }

        // 将 editText 和 clearIv 添加到 ConstraintLayout 中
        addView(editText)
        if (isClear) {
            addView(clearIv)
        }
    }

    fun setHint(hint: String) {
        editText?.hint = hint
    }

    fun setTextStr(text: String) {
        editText?.setText(text)
    }

    fun getEditText(): EditText? {
        return editText
    }
}