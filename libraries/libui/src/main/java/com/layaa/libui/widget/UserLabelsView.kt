package com.layaa.libui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.annotation.CheckResult
import com.layaa.libui.UiKit
import com.layaa.libui.dp

/**
 * Created by <PERSON> on 24/12/09_Mon
 */
class UserLabelsView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private val views = mutableMapOf<Int, View>()

    private val countryView: ImageView by lazy {
        ImageView(context)
    }
    private val vipLevelView: VipLevelView by lazy {
        VipLevelView(context)
    }
    private val genderAgeView: GenderAgeView by lazy {
        GenderAgeView(context)
    }
    private val userLevelView: UserLevelView by lazy {
        UserLevelView(context)
    }
    private val achievementLabelsView: AchievementLabelsView by lazy {
        AchievementLabelsView(context)
    }

    private var index = 0
    private val countryIndex = index++
    private val vipLevelIndex = index++
    private val genderAgeIndex = index++
    private val userLevelIndex = index++
    private val achievementIndex = index++

    init {
        orientation = HORIZONTAL
        gravity = Gravity.CENTER_VERTICAL
    }

    @CheckResult
    fun clear(): UserLabelsView {
        views.clear()
        return this
    }

    @CheckResult
    fun vip(vipLevel: Int?): UserLabelsView {
        if ((vipLevel ?: 0) > 0) {
            vipLevelView.show(vipLevel ?: 0)
            views[vipLevelIndex] = vipLevelView
        } else {
            views.remove(vipLevelIndex)
        }
        return this
    }

    @CheckResult
    fun country(country: String?): UserLabelsView {
        if(country.isNullOrEmpty()){
            return this
        }
        countryView.layoutParams = LayoutParams(21.dp, 21.dp)
        UiKit.loadCountry(countryView, country)
        views[countryIndex] = countryView
        return this
    }

    @CheckResult
    fun genderAge(gender: String?, age: Int?): UserLabelsView {
        genderAgeView.show(gender ?: "", age)
        views[genderAgeIndex] = genderAgeView
        return this
    }

    @CheckResult
    fun level(userLevel: Int?): UserLabelsView {
        //用户等级
//        if ((userLevel ?: 0) > 0) {
//            userLevelView.setUserLevel(userLevel ?: 0)
//            views[userLevelIndex] = userLevelView
//        }
        return this
    }

    @CheckResult
    fun achievement(icons: List<String>?): UserLabelsView {
        achievementLabelsView.show(icons ?: emptyList())
        views[achievementIndex] = achievementLabelsView
        return this
    }

    fun show() {
        removeAllViews()
        views.keys.sorted().forEachIndexed { index, viewIndex ->
            views[viewIndex]?.let { view ->
                val params: LayoutParams = (view.layoutParams as? LayoutParams) ?: LayoutParams(
                    LayoutParams.WRAP_CONTENT,
                    LayoutParams.WRAP_CONTENT
                )
                if (index != views.keys.size - 1) {
                    params.marginEnd = 3.dp
                }
                (view.parent as? ViewGroup)?.removeView(view)
                addView(view, params)
            }
        }
    }
}