package com.layaa.libui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.Gravity
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.annotation.DrawableRes
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions

class OverlapImageView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val bottomImage: ImageView
    private val topImage: ImageView
    private val dp10_5 = (10.5f * resources.displayMetrics.density).toInt()

    init {
        // 上层图片（圆形/圆角）
        topImage = ImageView(context).apply {
            scaleType = ImageView.ScaleType.CENTER_CROP
            layoutParams = LayoutParams(
                LayoutParams.MATCH_PARENT,
                LayoutParams.MATCH_PARENT,
                Gravity.CENTER
            ).apply {
                setMargins(dp10_5, dp10_5, dp10_5, dp10_5)
            }
        }
        addView(topImage)
        // 底部图片
        bottomImage = ImageView(context).apply {
            scaleType = ImageView.ScaleType.CENTER_CROP
            layoutParams = LayoutParams(
                LayoutParams.MATCH_PARENT,
                LayoutParams.MATCH_PARENT,
                Gravity.CENTER
            )
        }
        addView(bottomImage)


    }

    /** 设置底部图片（本地资源） */
    fun setBottomImage(resId: Int) {
        bottomImage.setImageResource(resId)
    }

    /** 设置顶部图片（网络 url，圆形） */
    fun setTopImage(url: String) {
        Glide.with(context)
            .load(url)
            .apply(RequestOptions.circleCropTransform()) // 设置圆形
            .into(topImage)
    }
    /** 设置顶部图片（网络 url，圆形） */
    fun setTopImage(@DrawableRes drawableId: Int) {
        Glide.with(context)
            .load(drawableId)
            .apply(RequestOptions.circleCropTransform()) // 设置圆形
            .into(topImage)
    }
    /** 设置顶部图片（网络 url，圆形） */
    fun setTopImage(url: String,default: Int) {
        Glide.with(context)
            .load(url)
            .placeholder(default)
            .apply(RequestOptions.circleCropTransform()) // 设置圆形
            .into(topImage)
    }

    /** 如果需要圆角（不是完全圆形），可以用这个 */
    fun setTopImageWithRoundCorner(url: String, radiusDp: Float) {
        val radiusPx = radiusDp * resources.displayMetrics.density
        Glide.with(context)
            .load(url)
            .transform(CenterCrop(), RoundedCorners(radiusPx.toInt()))
            .into(topImage)
    }
}
