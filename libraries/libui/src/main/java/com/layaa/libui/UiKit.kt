package com.layaa.libui

import android.widget.ImageView

/**
 * Created by <PERSON> on 24/12/09_Mon
 */
object UiKit {

    @JvmStatic
    var countryLoader: ((ImageView, String?) -> Unit)? = null

    @JvmStatic
    fun loadCountry(view: ImageView, country: String?) {
        countryLoader?.invoke(view, country)
    }

    @JvmStatic
    var vipRouter: ((Int) -> Unit)? = null

    @JvmStatic
    var openNetDescPage: (() -> Unit)? = null

    @JvmStatic
    fun gotoVipHome(level: Int) {
        vipRouter?.invoke(level)
    }
}