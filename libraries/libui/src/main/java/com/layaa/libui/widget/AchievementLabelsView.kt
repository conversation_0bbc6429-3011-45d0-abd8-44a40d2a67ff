package com.layaa.libui.widget

import android.content.Context
import android.util.AttributeSet
import android.widget.ImageView
import android.widget.LinearLayout
import com.bumptech.glide.Glide
import com.layaa.libutils.dp

/**
 * Created by <PERSON> on 24/12/17_Tue
 */
class AchievementLabelsView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    fun show(icons: List<String>) {
        removeAllViews()
        icons.forEachIndexed { index, icon ->
            val imageView = ImageView(context)
            imageView.adjustViewBounds = true
            //Glide.with(this)
            Glide.with(this)
                .load(icon)
                .into(imageView)
            val layoutParams = LayoutParams(LayoutParams.WRAP_CONTENT, 21.dp)
            if (index != icons.size - 1) {
                layoutParams.marginEnd = 3.dp
            }
            addView(imageView, layoutParams)
        }
    }

}