package com.layaa.libui.widget.nickname

import android.content.Context
import android.util.AttributeSet
import com.layaa.libui.widget.ColorfulTextView

/**
 * Created by <PERSON> on 24/12/05_Thu
 */
class NicknameTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ColorfulTextView(context, attrs, defStyleAttr) {

    fun show(bean: NicknameBean?) {
        gradientColors = bean?.colorfulColors?.toIntArray() ?: intArrayOf()
        gradientShader = null
        text = bean?.name
    }

}