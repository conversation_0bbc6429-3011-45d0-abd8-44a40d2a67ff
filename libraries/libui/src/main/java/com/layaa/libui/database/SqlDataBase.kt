package com.layaa.libui.database

import android.content.Context
import android.database.SQLException
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteDatabase.CursorFactory
import android.database.sqlite.SQLiteOpenHelper
import android.text.TextUtils
import com.layaa.libui.file.AppDirUtils
import com.layaa.libui.file.FileUtils
import com.layaa.libui.log.LogUtils
import java.io.File
import java.util.Hashtable

abstract class SqlDataBase(
    context: Context,
    private val name: String?,
    factory: CursorFactory?,
    private val version: Int
) : SQLiteOpenHelper(
    context,
    name,
    factory,
    version,
) {

    private val TAG = "SqlDataBase"

    private val mDbTables = Hashtable<Class<out BaseTable<*>?>?, BaseTable<*>>()

    private var mDefaultWritableDatabase: SQLiteDatabase? = null

    /**
     * 注册数据库中的表
     */
    protected fun registerTable(
        table: Class<out BaseTable<*>?>?,
        baseTable: BaseTable<*>?
    ) {
        if (table == null || baseTable == null) {
            LogUtils.e(
                TAG,
                "registTable() --- table == null || baseTable == null"
            )
            return
        }
        val tmpBaseTable: BaseTable<*>? = getTable(table)
        if (tmpBaseTable == null) {
            mDbTables.put(table, baseTable)
        }
    }

    override fun onCreate(db: SQLiteDatabase?) {
        mDefaultWritableDatabase = db
        createAllTable(db)
    }

    /**
     * 获得表实例
     *
     * @param table
     * @return T extends BaseTable
     */
    fun <T : BaseTable<*>?> getTable(table: Class<T>?): T? {
        return mDbTables.get(table) as T?
    }

    /**
     * 数据库升级时调用
     */
    override fun onUpgrade(db: SQLiteDatabase?, oldVersion: Int, newVersion: Int) {
        LogUtils.d(
            TAG, ("+++SqlDataBase,fromVersion:" + oldVersion
                    + ",toVersion:" + newVersion)
        )
        if (mDbTables.isNotEmpty()) {
            for (baseTable in mDbTables.values) {
                if (!baseTable.tableIsExist()) {
                    createTable(baseTable, db)
                    //createTable(baseTable, mDefaultWritableDatabase);
                } else {
                    baseTable.onUpgraded(this, oldVersion, newVersion)
                }
            }
        }

        if (FileUtils.isFile(File(getDatabaseFile(name)))) {
            FileUtils.deleteFile(getDatabaseFile(name))
        }
    }

    /**
     * 创建所有数据库表
     */
    protected fun createAllTable(db: SQLiteDatabase?) {
        if (mDbTables.isNotEmpty()) {
            for (baseTable in mDbTables.values) {
                createTable(baseTable, db)
            }
        }
    }

    // 在数据库中创建一个新表
    private fun createTable(baseTable: BaseTable<*>?, db: SQLiteDatabase?) {
        if (baseTable == null || db == null) {
            LogUtils.d(TAG, "createTable() --- baseTable == null || db == null")
            return
        }
        if (baseTable.tableIsExist()) {
            LogUtils.d(TAG, baseTable.getTableName() + " has created!")
            return
        }
        val sqls = ArrayList<String>()
        val sql = baseTable.onBuildSqlCmdForCreate()
        if (!TextUtils.isEmpty(sql)) {
            sqls.add(sql)
            execSQL(sqls, db)
            baseTable.onCreated(this)
        }
    }

    /**
     * 执行 SQLexec cmd
     *
     * @return
     */
    fun execSQL(sqls: MutableList<String>?, db: SQLiteDatabase) {

        if (sqls != null && sqls.isNotEmpty()) {
            for (i in sqls.indices) {
                val sql = sqls[i]
                if (!TextUtils.isEmpty(sql)) {
                    db.execSQL(sql)
                }
            }
        }
    }

    fun removeDb() {
        FileUtils.deleteFile(getDatabaseFile(name))
    }

    /**
     * 删除一个数据库表
     *
     * @return
     */
    fun dropTable(db: SQLiteDatabase, strTableName: String?) {
        try {
            db.execSQL("DROP TABLE IF EXISTS " + strTableName)
        } catch (ex: SQLException) {
            LogUtils.e(TAG, "can not  drop table $strTableName")
            ex.printStackTrace()
        }
    }

    /**
     * 给某个数据库表增加一列
     */
    @Throws(SQLException::class)
    fun addColumn(baseTable: BaseTable<*>?, column: String?, append: String?) { // String tableName,
        if (baseTable == null) {
            LogUtils.d(
                TAG,
                "addColumn() --- baseTable == null || mCheckDatabaseAtAsserting is true"
            )
            return
        }
        baseTable.addColumn(column?.toString(), append)
    }


    private fun getDatabaseFile(dbFile: String?): String {
        return AppDirUtils.getThirdDirPath(AppDirUtils.getCacheDatabase(), dbFile)
    }


    /**
     * 重写getWritableDatabase方法防止递归的出现
     */
    override fun getWritableDatabase(): SQLiteDatabase? {
        val db: SQLiteDatabase?
        if (mDefaultWritableDatabase != null) {
            db = mDefaultWritableDatabase
        } else {
            db = super.getWritableDatabase()
        }
        return db
    }
}