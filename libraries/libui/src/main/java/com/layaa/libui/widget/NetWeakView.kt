package com.layaa.libui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.layaa.language.LanguageController
import com.layaa.libui.R
import com.layaa.libui.UiKit
import com.layaa.libui.databinding.ViewNetErrorBinding
import com.layaa.libui.getString

/**
 * Created by <PERSON> on 25/02/13_Thu
 */
class NetWeakView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    val binding = ViewNetErrorBinding.inflate(LayoutInflater.from(context), this)

    init {
        setBackgroundColor(0xFFFFF2F2.toInt())
        setOnClickListener {
            UiKit.openNetDescPage?.invoke()
        }
    }

    fun onConnecting() {
        binding.txtTitle.text = getString(R.string.net_connecting)
        binding.imgArrow.isVisible = false
        isEnabled = false
    }

    fun reset() {
        binding.txtTitle.text = getString(R.string.vr_network_unavailable)
        binding.imgArrow.isVisible = true
        isEnabled = true
    }

    fun isConnecting(): Boolean {
        return isEnabled.not()
    }
}