package com.layaa.libui

/**
 * Created by <PERSON> on 2024/10/30
 */
enum class GotoHost(val host: String) {
    MAIN(GotoConst.Host.MAIN),
    PARTY(GotoConst.Host.PARTY),
    ROOM(GotoConst.Host.ROOM),
    SYSTEM_MESSAGE(GotoConst.Host.SYSTEM_MESSAGE),
    IM_CHAT(GotoConst.Host.IM_CHAT),
    PROFILE(GotoConst.Host.PROFILE),
    ME(GotoConst.Host.ME),
    WALLET(GotoConst.Host.WALLET),
    SETTING(GotoConst.Host.SETTING),
    ACCOUNT(GotoConst.Host.ACCOUNT),
    FAMILY(GotoConst.Host.FAMILY),
    SHOP(GotoConst.Host.SHOP),
    WEBVIEW(GotoConst.Host.WEBVIEW),
    BACKPACK(GotoConst.Host.BACKPACK),
    <PERSON><PERSON><PERSON><PERSON>(GotoConst.Host.LEVEL),
    UNKNOWN(GotoConst.Host.UNKNOWN);

    companion object {

        fun fromString(host: String?): GotoHost {
            return values().find { it.host == host } ?: UNKNOWN
        }
    }
}

object GotoConst {
    const val SCHEME = "goto"
    const val PARAM_SUB_PAGE = "page"

    object Host {
        const val MAIN = "main"
        const val PARTY = "party"
        const val ROOM = "room"
        const val SYSTEM_MESSAGE = "systemMessage"
        const val IM_CHAT = "imChat"
        const val PROFILE = "profile"
        const val ME = "me"
        const val WALLET = "wallet"
        const val SETTING = "setting"
        const val ACCOUNT = "account"
        const val FAMILY = "family"
        const val SHOP = "shop"
        const val WEBVIEW = "webview"
        const val UNKNOWN = "unknown"
        const val BACKPACK = "package"
        const val LEVEL = "level"
    }

    object Main {
        const val TAB_HOME = "home"
        const val TAB_GAME = "game"
        const val TAB_MESSAGE = "message"
        const val TAB_ME = "me"
        const val TAB_PARTY_CHAT = "party_chat"
        const val TAB_PARTY_MINE_RECENT = "party_mine_recent"
        const val TAB_PARTY_MINE_FOLLOW = "party_mine_follow"
    }

    object Wallet {
        const val PAGE_BILL = "bill"
        const val PAGE_MAIN = "main"
        const val PAGE_COIN = "golden"
        const val PAGE_GEM = "gem"
        const val PAGE_BEAN_TO_DIAMOND = "beanToDiamond"
        const val PAGE_BEAN_TO_CASH = "beanToCash"
        const val PAGE_CASH_TO_DIAMOND = "cashToDiamond"
        const val PAGE_DIAMOND = "diamond"
        const val PAGE_DEALER = "businessDiamond"
    }

}