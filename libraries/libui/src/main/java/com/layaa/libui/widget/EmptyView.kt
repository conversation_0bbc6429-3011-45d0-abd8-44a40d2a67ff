package com.layaa.libui.widget

import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.layaa.language.LanguageController
import com.layaa.libui.R
import com.layaa.libui.dp
import androidx.core.content.withStyledAttributes

/**
 * Created by <PERSON> on 24/11/14_Thu
 */
class EmptyView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : LinearLayout(context, attrs) {

    private val imageView = ImageView(context)

    private val textView = TextView(context)

    init {
        context.withStyledAttributes(attrs, R.styleable.EmptyView) {
            val iconRes =
                getResourceId(R.styleable.EmptyView_icon, R.drawable.icon_empty_default)
            imageView.setImageResource(iconRes)
            val iconWidth = getDimensionPixelSize(
                R.styleable.EmptyView_iconWidth,
                150.dp
            )
            val iconHeight = getDimensionPixelSize(
                R.styleable.EmptyView_iconHeight,
                150.dp
            )
            imageView.layoutParams = LayoutParams(iconWidth, iconHeight)

            val text = getResourceId(R.styleable.EmptyView_text, 0)
            if (text > 0) {
                textView.setText(text)
            } else {
                textView.text = ContextCompat.getString(context, R.string.empty_default_text)
            }
            val textSize =
                getDimensionPixelSize(R.styleable.EmptyView_textSize, 16.dp)
            val textColor = getColor(
                R.styleable.EmptyView_textColor,
                ContextCompat.getColor(context, R.color.color_86909C)
            )
            val innerPadding = getDimensionPixelSize(
                R.styleable.EmptyView_innerPadding,
                10.dp
            )
            textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize.toFloat())
            textView.setTextColor(textColor)
            textView.gravity = Gravity.CENTER
            textView.layoutParams = LayoutParams(
                LayoutParams.WRAP_CONTENT,
                LayoutParams.WRAP_CONTENT
            ).apply {
                topMargin = innerPadding
                marginStart = 20.dp
                marginEnd = 20.dp
            }

        }

        orientation = VERTICAL
        gravity = Gravity.CENTER
        addView(imageView)
        addView(this.textView)
    }

    fun setIcon(iconRes: Int) {
        imageView.setImageResource(iconRes)
    }

    fun setText(text: String) {
        textView.text = text
    }

    fun setText(textRes: Int) {
        setText(context.getString(textRes))
    }
}