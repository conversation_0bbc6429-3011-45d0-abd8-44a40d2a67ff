package com.layaa.libui.widget.emoji

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.layaa.emote.widget.LongPressListener
import com.layaa.emote.widget.ShortPressListener
import com.layaa.libui.R
import com.layaa.libui.UiKit
import com.layaa.libui.databinding.EmojiViewPagerBinding
import com.layaa.libutils.dp
import com.layaa.skinlib.SkinKit


/**
 * Created by <PERSON> on 24/12/10_Tue
 */
class EmojiPagerView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val binding = EmojiViewPagerBinding.inflate(LayoutInflater.from(context), this, true)

    var shortPressListener: ShortPressListener? = null

    var longPressListener: LongPressListener? = null

    private val tabAdapter by lazy {
        EmojiTabAdapter().apply {
            onLockButtonClick = { bean ->
                when (bean.type) {
                    EmojiTabBean.Type.NORMAL -> {

                    }

                    EmojiTabBean.Type.NORMAL_WITH_GUESSING -> {

                    }

                    EmojiTabBean.Type.ZD -> {
                        UiKit.gotoVipHome(level = 1)
                    }
                }
            }
            shortPressListener = <EMAIL>
            longPressListener = <EMAIL>
        }
    }

    fun load(canUseVipEmoji: (() -> Boolean), isShowGuessing: Boolean) {
        binding.pagerEmoji.adapter = tabAdapter
        binding.pagerEmoji.layoutDirection = if (SkinKit.getInstance().isRtl) {
            LAYOUT_DIRECTION_RTL
        } else {
            LAYOUT_DIRECTION_LTR
        }
        tabAdapter.refreshList(
            arrayListOf(
                if (isShowGuessing) {
                    EmojiTabBean(
                        EmojiTabBean.Type.NORMAL_WITH_GUESSING,
                        { true },
                        R.drawable.common_03_dazhaohu
                    )
                } else {
                    EmojiTabBean(
                        EmojiTabBean.Type.NORMAL,
                        { true },
                        R.drawable.common_03_dazhaohu
                    )
                },
                EmojiTabBean(
                    EmojiTabBean.Type.ZD,
                    canUseVipEmoji,
                    R.drawable.zd_07_youqian
                )
            )
        )
        binding.pagerEmoji.offscreenPageLimit = tabAdapter.getList().size

        TabLayoutMediator(
            binding.tabEmoji,
            binding.pagerEmoji
        ) { tab: TabLayout.Tab, position: Int ->
            val customView = FrameLayout(context)
            customView.setPadding(12.dp, 3.dp, 12.dp, 3.dp)
            customView.addView(ImageView(context).apply {
                layoutParams = FrameLayout.LayoutParams(27.dp, 27.dp)
                val bean = tabAdapter.getList()[position]
                setImageResource(bean.previewIcon)
            })
            customView.setBackgroundResource(R.drawable.emoji_bg_tab_item)
            tab.customView = customView
        }.attach()

    }

    fun setTabBackground(color: Int) {
        binding.tabEmoji.setBackgroundColor(color)
    }

}