package com.layaa.libui

import java.util.Locale
import kotlin.math.floor

/**
 * Created by <PERSON> on 25/01/22_Wed
 */

fun Long?.numberCommonFormat(): String {

    fun formatWithSuffix(value: Long, divisor: Long, suffix: String): String {
        val result = value / divisor.toDouble()
        return if (result >= 100) {
            "${floor(result).toLong()}$suffix"
        } else {
            "${String.format(Locale.ENGLISH, "%.1f", result)}$suffix"
        }
    }

    val value = this ?: 0
    return when {
        value < 1_000 -> value.toString()
        value < 1_000_000 -> formatWithSuffix(value, 1_000, "K")
        value < 1_000_000_000 -> formatWithSuffix(value, 1_000_000, "M")
        value < 1_000_000_000_000 -> formatWithSuffix(value, 1_000_000_000, "B")
        else -> formatWithSuffix(value, 1_000_000_000_000, "T")
    }
}

fun Int.giftCountFormat(): String {
    // 多一个空格解决斜体被裁切的问题
    return "×${this} "
}