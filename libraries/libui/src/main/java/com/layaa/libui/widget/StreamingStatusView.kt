package com.layaa.libui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.core.view.isVisible
import com.layaa.libui.R
import com.layaa.libui.databinding.ViewStreamingStatusBinding
import com.layaa.libui.getString

/**
 * Created by <PERSON> on 2024/10/11
 */
class StreamingStatusView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val binding =
        ViewStreamingStatusBinding.inflate(LayoutInflater.from(context), this, true)

    fun setRoomType(type: Int) {
        when (type) {
            1 -> {
                binding.txtType.text = getString(R.string.room_streaming_status_voice)
            }
            else -> binding.txtType.text = getString(R.string.room_streaming_status_video)
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        if (isVisible) {
            binding.lottieWave.cancelAnimation()
            binding.lottieWave.playAnimation()
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        if (isVisible) {
            binding.lottieWave.cancelAnimation()
        }
    }

    override fun setVisibility(visibility: Int) {
        super.setVisibility(visibility)
        if (visibility == VISIBLE) {
            binding.lottieWave.setAnimation("lottie/room_streaming_wave.json")
            if (binding.lottieWave.isAnimating.not()) {
                binding.lottieWave.playAnimation()
            }
        } else {
            binding.lottieWave.cancelAnimation()
        }
    }
}