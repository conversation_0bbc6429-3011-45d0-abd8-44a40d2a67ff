package com.layaa.libui.widget

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Shader
import android.text.SpannableString
import android.text.TextUtils
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import com.layaa.language.LanguageController
import com.layaa.libui.R
import com.layaa.libui.dialog.ViewTopPopWindow
import com.layaa.libutils.ClickValidUtil
import com.layaa.libutils.dp
import com.layaa.libutils.toast.ToastUtils
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class PrettyUidView(context: Context, attrs: AttributeSet?) : FrameLayout(context, attrs) {

    private var prettyImg: ImageView? = null
    private var prettyUidTv: TextView? = null
    private var userUidTv: TextView? = null
    private var expTimeTv: TextView? = null
    private var seeExpTimeIv: ImageView? = null
    private var copyIv: ImageView? = null
    private val clipboardManager by lazy {
        context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    }
    private val tipsPopDialog by lazy {
        ViewTopPopWindow(context)
    }
    private var isDialogExpTime = false

    init {
        val typeArray = context.obtainStyledAttributes(attrs, R.styleable.PrettyUidView)
        val textSize =
            typeArray.getDimensionPixelSize(R.styleable.PrettyUidView_textSize, 13.dp).toFloat()
        val uidColor =
            typeArray.getResourceId(R.styleable.PrettyUidView_idColor, R.color.color_86909C)
        val timeColor =
            typeArray.getResourceId(R.styleable.PrettyUidView_timeColor, R.color.color_86909C)
        val copyImg = typeArray.getResourceId(
            R.styleable.PrettyUidView_copyImg,
            R.drawable.account_id_copy_icon
        )
        isDialogExpTime = typeArray.getBoolean(R.styleable.PrettyUidView_isDialogExpTime, false)
        typeArray.recycle()
        addView(LayoutInflater.from(context).inflate(R.layout.view_pretty_uid, this, false))
        prettyImg = findViewById(R.id.prettyImg)
        prettyUidTv = findViewById(R.id.prettyUidTv)
        userUidTv = findViewById(R.id.userUidTv)
        expTimeTv = findViewById(R.id.expTimeTv)
        seeExpTimeIv = findViewById(R.id.seeExpTimeIv)
        copyIv = findViewById(R.id.copyIv)

        prettyUidTv?.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize)
        userUidTv?.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize)
        userUidTv?.setTextColor(ContextCompat.getColor(context, uidColor))
        expTimeTv?.setTextColor(ContextCompat.getColor(context, timeColor))
        userUidTv?.setCompoundDrawablesWithIntrinsicBounds(0, 0, copyImg, 0)
        if (!isDialogExpTime) {
            prettyUidTv?.setCompoundDrawablesWithIntrinsicBounds(0, 0, copyImg, 0)
        }
        copyIv?.setImageResource(copyImg)

        seeExpTimeIv?.setOnClickListener {
            tipsPopDialog.show(expTimeTv?.text.toString().replace("(", "").replace(")", ""), it)
        }

        setOnClickListener {
            if (!ClickValidUtil.clickValidNormal()) {
                return@setOnClickListener
            }
            clipboardManager.setPrimaryClip(
                ClipData.newPlainText(
                    null,
                    if (TextUtils.isEmpty(prettyUidTv?.text?.toString())) {
                        userUidTv?.text?.toString()?.substring(3)
                    } else {
                        prettyUidTv?.text?.toString()
                    }
                )
            )
            ToastUtils.show(
                LanguageController.getInstance()
                    .getString("account_copy_success", R.string.vr_share_copy_success)
            )
        }
    }

    fun setUserId(userId: String) {
        prettyImg?.isVisible = false
        expTimeTv?.isVisible = false
        prettyUidTv?.isVisible = false
        seeExpTimeIv?.isVisible = false
        copyIv?.isVisible = false

        userUidTv?.isVisible = true
        userUidTv?.text = "ID: $userId"
    }

    fun setPrettyUid(prettyUid: String) {
        prettyImg?.isVisible = true
        expTimeTv?.isVisible = true
        prettyUidTv?.isVisible = true
        expTimeTv?.isVisible = !isDialogExpTime
        seeExpTimeIv?.isVisible = isDialogExpTime
        copyIv?.isVisible = isDialogExpTime

        userUidTv?.isVisible = false
        prettyUidTv?.text = prettyUid
    }

    fun setCopyImgVisible(isVisible: Boolean) {
        if (!isVisible) {
            prettyUidTv?.setCompoundDrawables(null, null, null, null)
        }
    }

    fun setAutoUid(userId: String?, prettyUid: String?, expireMs: String?) {
        if (TextUtils.isEmpty(prettyUid)) {
            //无靓号
            userId?.let { setUserId(it) }
        } else {
            prettyUid?.let { setPrettyUid(it) }
            if (!TextUtils.isEmpty(expireMs)) {
                expTimeTv?.text = "(${timestampToLocalDateTime(expireMs!!.toLong())})"
            }

        }
    }

    fun setAutoUid(userId: SpannableString?, prettyUid: String?) {
        if (TextUtils.isEmpty(prettyUid)) {
            //无靓号
            prettyImg?.isVisible = false
            expTimeTv?.isVisible = false
            prettyUidTv?.isVisible = false
            userUidTv?.isVisible = true
            userUidTv?.text = userId
        } else {
            prettyUid?.let { setPrettyUid(it) }
        }
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        prettyUidTv?.let {
            applyGradient(it)
        }
    }

    private fun applyGradient(textView: TextView) {
        val shader = LinearGradient(
            0f,
            0f,
            textView.width.toFloat(),
            0f,
            intArrayOf(Color.parseColor("#2EF57A"), Color.parseColor("#3DE4A1")),
            floatArrayOf(0f, 1f),
            Shader.TileMode.CLAMP
        )
        textView.paint.shader = shader
    }

    private fun timestampToLocalDateTime(timestamp: Long): String {
        // 创建一个 SimpleDateFormat 对象，指定日期时间格式
        val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        // 将时间戳转换为 Date 对象
        val date = Date(timestamp)
        // 格式化 Date 对象为字符串
        return sdf.format(date)
    }

}