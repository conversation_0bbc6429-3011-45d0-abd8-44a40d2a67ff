package com.layaa.libui.widget

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.view.isVisible
import com.layaa.libui.R

/**
 * Created by <PERSON> on 24/12/09_Mon
 */
class VipLevelView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {

    fun show(level: Int) {
        val resource = when (level) {
            1 -> R.drawable.vip_icon_label_level1
            2 -> R.drawable.vip_icon_label_level2
            3 -> R.drawable.vip_icon_label_level3
            4 -> R.drawable.vip_icon_label_level4
            5 -> R.drawable.vip_icon_label_level5
            6 -> R.drawable.vip_icon_label_level6
            else -> -1
        }

        if (resource >= 0) {
            isVisible = true
            setImageResource(resource)
        } else {
            isVisible = false
        }
    }
}