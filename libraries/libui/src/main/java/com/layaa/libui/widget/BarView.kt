package com.layaa.libui.widget

import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.appcompat.view.ContextThemeWrapper
import androidx.core.view.isGone
import com.layaa.libutils.StatusBarUtil

class BarView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {

    private var statusBarHeight = 0
    private var height = 0

    init {

        statusBarHeight =
            if (isTranslucentStatus(context)) StatusBarUtil.getStatusBarHeight(context) else 0

    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val count = childCount
        require(count == 1) { "titleBar child view count most is 1" }
        val heightMode = MeasureSpec.getMode(heightMeasureSpec)
        val widthSize = MeasureSpec.getSize(widthMeasureSpec)
        val child = getChildAt(0)
        measureChildWithMargins(child, widthMeasureSpec, 0, heightMeasureSpec, 0)
        val heightSize = child.measuredHeight
        if (child.isGone) {
            height = statusBarHeight
        } else {
            if (heightMode == MeasureSpec.AT_MOST) {
                val layoutParams = child.layoutParams as LayoutParams
                height = (child.measuredHeight + layoutParams.topMargin
                        + layoutParams.bottomMargin + statusBarHeight)
            } else {
                height = heightSize + statusBarHeight
            }
        }
        setPadding(0, statusBarHeight, 0, 0)
        setMeasuredDimension(widthSize, height)
    }

    fun isTranslucentStatus(context: Context?): Boolean {
        var context = context
        try {
            if (context is ContextThemeWrapper) {
                context = context.baseContext
            }

            if (context is Activity) {
                val decorView = context.window.decorView as ViewGroup
                return (decorView.systemUiVisibility and SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN) != 0
            } else if (context is android.view.ContextThemeWrapper) {
                val activity = context.baseContext as Activity
                val decorView = (activity.window.decorView) as ViewGroup
                return (decorView.systemUiVisibility and SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN) != 0
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return false
    }

}