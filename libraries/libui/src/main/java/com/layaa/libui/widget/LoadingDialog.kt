package com.layaa.libui.widget

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.animation.AccelerateInterpolator
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import com.layaa.libui.R
import com.layaa.libui.base.BaseVBDialog
import com.layaa.libui.databinding.DialogLoadingBinding

class LoadingDialog(context: Context) : BaseVBDialog<DialogLoadingBinding>(context) {

    private var animator: Animation? = null
    override fun getViewBinding(): DialogLoadingBinding {
        return DialogLoadingBinding.inflate(LayoutInflater.from(context))
    }

    override fun initAction() {

        animator = AnimationUtils.loadAnimation(context, R.anim.anim_loading)
        animator?.repeatMode = Animation.RESTART
        animator?.repeatCount = Animation.INFINITE
        animator?.duration = 1000
        animator?.interpolator = AccelerateInterpolator()
        setCancelable(false)
        setCanceledOnTouchOutside(false)
    }

    override fun getGravity(): Int {
        return Gravity.CENTER
    }

    override fun getAnimationStyle(): Int {
        return R.style.popupAnimation
    }

    override fun show() {
        super.show()
        binding.loading.startAnimation(animator)
    }

    override fun dismiss() {
        super.dismiss()
        animator?.cancel()
        binding.loading.clearAnimation()
    }
}