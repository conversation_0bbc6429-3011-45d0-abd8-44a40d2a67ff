package com.layaa.libui.livedata;

import androidx.annotation.NonNull;

/**
 * <AUTHOR>
 * @date 2024/5/28
 * @des
 **/
public class UnPeekLiveData<T> extends ProtectedUnPeekLiveData<T> {

    public UnPeekLiveData(T value) {
        super(value);
    }

    public UnPeekLiveData() {
        super();
    }

    @Override
    public void setValue(T value) {
        super.setValue(value);
    }

    @Override
    public void postValue(T value) {
        super.postValue(value);
    }

    public static class Builder<T> {

        /**
         * 是否允许传入 null value
         */
        private boolean isAllowNullValue;

        public Builder<T> setAllowNullValue(boolean allowNullValue) {
            this.isAllowNullValue = allowNullValue;
            return this;
        }

        @NonNull
        public UnPeekLiveData<T> create() {
            UnPeekLiveData<T> liveData = new UnPeekLiveData<>();
            liveData.isAllowNullValue = this.isAllowNullValue;
            return liveData;
        }
    }
}
