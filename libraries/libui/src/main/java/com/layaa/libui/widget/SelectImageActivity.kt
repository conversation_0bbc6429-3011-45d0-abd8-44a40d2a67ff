package com.layaa.libui.widget

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import androidx.appcompat.app.AppCompatActivity
import com.layaa.libui.R
import com.layaa.libui.utils.photo.PhotoUtil
import com.layaa.libutils.toast.ToastUtils

class SelectImageActivity : AppCompatActivity() {

    private val photoUtil = PhotoUtil(this)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_select_image)
        photoUtil.registerCallBack()
        photoUtil.startImage()
        photoUtil.setResultListener(object : PhotoUtil.OnPictureResultListener {
            override fun onSuccess(path: String) {
                if (!TextUtils.isEmpty(path)) {
                    val data = Intent()
                    data.putExtra(PATH, path)
                    setResult(RESULT_OK, data)
                }
                finish()
            }

            override fun onFail(message: String) {
                if (message.isNotEmpty()) {
                    ToastUtils.show(message)
                }
                finish()
            }
        })
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        photoUtil.onActivityResult(requestCode, resultCode, data)
    }

    override fun onDestroy() {
        super.onDestroy()
        photoUtil.unregisterCallBack()
    }

    companion object {
        const val PATH = "path"
    }
}