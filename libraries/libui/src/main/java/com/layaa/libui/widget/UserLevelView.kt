package com.layaa.libui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateMargins
import com.layaa.libui.R
import com.layaa.libui.utils.UIUtils

/**
 * 用户等级
 */
class UserLevelView(context: Context, attrs: AttributeSet? = null) : FrameLayout(context, attrs) {
    private var userLevelTv: TextView? = null
    private var levelBgIv: ImageView? = null
    private var levelIcon: ImageView? = null

    init {
        addView(LayoutInflater.from(context).inflate(R.layout.view_userlevel, this, false))
        userLevelTv = findViewById(R.id.userLeveTv)
        levelBgIv = findViewById(R.id.leveBgIv)
        levelIcon = findViewById(R.id.levelIcon)
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
    }

    fun setUserLevel(level: Int) {
        userLevelTv?.text = level.toString()
        val layoutParams = userLevelTv?.layoutParams as ConstraintLayout.LayoutParams
        if (level > 99) {
            layoutParams.updateMargins(
                left = UIUtils.getPixels(1f),
            )
        } else {
            layoutParams.updateMargins(
                left = UIUtils.getPixels(0f),
            )
        }
        userLevelTv?.layoutParams = layoutParams

        when (level) {
            in 0..20 -> {
                levelBgIv?.setImageResource(R.drawable.bg_user_level_1)
                levelIcon?.setImageResource(R.drawable.icon_user_level_1)
            }

            in 21..40 -> {
                levelBgIv?.setImageResource(R.drawable.bg_user_level_2)
            }

            in 41..60 -> {
                levelBgIv?.setImageResource(R.drawable.bg_user_level_3)
            }

            in 61..80 -> {
                levelBgIv?.setImageResource(R.drawable.bg_user_level_4)
            }

            in 81..100 -> {
                levelBgIv?.setImageResource(R.drawable.bg_user_level_5)
            }

            in 101..120 -> {
                levelBgIv?.setImageResource(R.drawable.bg_user_level_6)
            }

            in 121..140 -> {
                levelBgIv?.setImageResource(R.drawable.bg_user_level_7)
            }

            else -> {
                levelBgIv?.setImageResource(R.drawable.bg_user_level_8)
            }
        }
        levelIcon?.setImageResource(getLevelIcon(level))
    }

    companion object {
        fun getLevelIcon(level: Int): Int {
            return when (level) {
                in 0..20 -> R.drawable.icon_user_level_1
                in 21..40 -> R.drawable.icon_user_level_2
                in 41..60 -> R.drawable.icon_user_level_3
                in 61..80 -> R.drawable.icon_user_level_4
                in 81..100 -> R.drawable.icon_user_level_5
                in 101..120 -> R.drawable.icon_user_level_6
                in 121..140 -> R.drawable.icon_user_level_7
                else -> R.drawable.icon_user_level_8
            }
        }
    }
}