package com.layaa.libui.database

import android.annotation.SuppressLint
import android.content.ContentValues
import android.database.Cursor
import android.database.SQLException
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper
import android.provider.BaseColumns
import com.layaa.libui.log.LogUtils
import java.sql.Date
import java.util.concurrent.atomic.AtomicBoolean

abstract class BaseTable<T>(private val tableName: String, val helper: SQLiteOpenHelper?) :
    BaseColumns {

    private val checkTable = AtomicBoolean(false)

    /**
     * 获得表名
     *
     * @return
     */
    fun getTableName(): String {
        return tableName
    }

    /**
     * 通过cursor转换为对象
     *
     * @param cursor
     * @return
     */
    abstract fun getItemFromCursor(cursor: Cursor?): T?

    /**
     * 创建表
     */
    protected abstract fun createTable()

    /**
     * 创建 数据库 SQL exec cmd
     *
     * @return
     */
    abstract fun onBuildSqlCmdForCreate(): String

    /**
     * 对象转成contentValues
     *
     * @param item
     * @return
     */
    abstract fun getContentValues(item: T?): ContentValues?

    /**
     * 通过特定条件检索数据
     *
     * @param sqlQuery
     * @param
     * @return
     */
    fun query(sqlQuery: String, arg: Array<String>?): MutableList<T>? {
        var items: MutableList<T>? = null
        var cursor: Cursor? = null
        try {
            val db: SQLiteDatabase? = getWritableDatabase()
            if (db == null) return null
            items = ArrayList<T>()
            cursor = db.rawQuery(sqlQuery, arg)
            while (cursor.moveToNext()) {
                getItemFromCursor(cursor)?.let {
                    items.add(it)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            cursor?.close()
        }
        return items
    }

    fun queryFirst(sqlQuery: String, arg: Array<String>?): T? {
        var firstItem: T? = null
        var cursor: Cursor? = null
        try {
            val db: SQLiteDatabase? = getWritableDatabase()
            if (db == null) return null
            cursor = db.rawQuery(sqlQuery, arg)
            if (cursor.moveToNext()) {
                firstItem = getItemFromCursor(cursor)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            cursor?.close()
        }
        return firstItem
    }

    /**
     * 表是否已被创建
     *
     * @return true 已经在数据库中创建
     * <AUTHOR> HParty(modify)
     */
    fun tableIsExist(): Boolean {
        var result = false
        var cursor: Cursor? = null
        val whereClause = buildString {
            append("type ='table' and name ='sqlite_master'")
        }
        try {
            val db: SQLiteDatabase? = getWritableDatabase()
            if (db == null) {
                return false
            }
            cursor = db.rawQuery(whereClause, null)

            if (cursor.count > 0) {
                result = true
            }
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            cursor?.close()
        }
        return result
    }

    /**
     * 插入数据
     *
     * @param item
     * @return
     */
    fun insert(item: T?): Long {
        val c: Cursor? = null
        try {
            checkTable()
            val db: SQLiteDatabase? = getWritableDatabase()
            if (db == null) {
                return -1
            }

            return db.insertWithOnConflict(
                getTableName(),
                null,
                getContentValues(item),
                SQLiteDatabase.CONFLICT_REPLACE
            )
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            c?.close()
        }
        return -1
    }

    fun insert(cv: ContentValues?): Long {
        val c: Cursor? = null
        try {
            checkTable()
            val db: SQLiteDatabase? = getWritableDatabase()
            if (db == null) return -1
            return db.insertWithOnConflict(
                getTableName(),
                null,
                cv,
                SQLiteDatabase.CONFLICT_REPLACE
            )
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            c?.close()
        }
        return -1
    }

    fun dropTable() {
        var cursor: Cursor? = null
        try {
            val db: SQLiteDatabase? = getWritableDatabase()
            if (db == null || !db.isOpen) return
            cursor = db.rawQuery("DROP TABLE IF EXISTS " + getTableName(), null)
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            cursor?.close()
        }
    }

    /**
     * 通过特定条件删除数据
     *
     * @param where
     * @param args
     * @return
     */
    fun deleteByCase(where: String?, args: Array<String?>?): Int {
        try {
            val db: SQLiteDatabase? = getWritableDatabase()
            if (db == null) return -1
            return db.delete(getTableName(), where, args)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return -1
    }

    /**
     * 更新记录
     *
     * @param item
     * @param where
     * @param args
     * @return
     */
    fun updateByCase(item: T?, where: String?, args: Array<String?>?): Int {
        try {
            val db: SQLiteDatabase? = getWritableDatabase()
            if (db == null) return -1

            val values = getContentValues(item)
            return db.updateWithOnConflict(
                getTableName(),
                values,
                where,
                args,
                SQLiteDatabase.CONFLICT_REPLACE
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return -1
    }

    /**
     * 更新记录
     *
     * @param cv
     * @param where
     * @param args
     * @return
     */
    fun updateByCase(cv: ContentValues?, where: String?, args: Array<String?>?): Int {
        try {
            val db: SQLiteDatabase? = getWritableDatabase()
            if (db == null) return -1

            return db.updateWithOnConflict(
                getTableName(),
                cv,
                where,
                args,
                SQLiteDatabase.CONFLICT_REPLACE
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return -1
    }

    /**
     * 获得String
     *
     * @param cursor
     * @param columnName
     * @return
     */
    @SuppressLint("UseValueOf")
    fun <T> getValue(cursor: Cursor, columnName: String?, t: Class<T?>): T? {
        val index = cursor.getColumnIndex(columnName)
        if (String::class.java.getName() == t.getName()) {
            if (index >= 0) return cursor.getString(index) as T?
            return null
        } else if (Int::class.java.getName() == t.getName()) {
            if (index >= 0) return cursor.getInt(index) as T
            return 0 as T
        } else if (Long::class.java.getName() == t.getName()) {
            if (index >= 0) return cursor.getLong(index) as T
            return 0 as T
        } else if (Float::class.java.getName() == t.getName()) {
            if (index >= 0) return cursor.getFloat(index) as T
            return 0 as T
        } else if (Double::class.java.getName() == t.getName()) {
            if (index >= 0) return cursor.getDouble(index) as T
            return 0 as T
        } else if (Date::class.java.getName() == t.getName()) {
            if (index >= 0) return Date(cursor.getLong(index)) as T
            return Date(System.currentTimeMillis()) as T
        }
        return null
    }

    /**
     * 获得可写的数据库
     *
     * @return SQLiteDatabase
     */
    fun getWritableDatabase(): SQLiteDatabase? {
        return helper?.writableDatabase
    }


    /**
     * 获得只读的数据库
     * <AUTHOR>
     * @return SQLiteDatabase
     */
    /*
  public SQLiteDatabase getReadableDatabase(){
		if(mSqLiteOpenHelper != null)
			return mSqLiteOpenHelper.getReadableDatabase();

		SqlDataBase helper = DataBaseManager.getDataBaseManager().getDefaultDB();
		if(helper == null)
			return null;
		return helper.getReadableDatabase();
	}*/
    /**
     * 获得数据库
     *
     * @return SqlDataBase
     */
    fun getSqlDataBase(): SQLiteOpenHelper? {
        return helper
    }

    fun execSQL(sql: String?) {
        val db: SQLiteDatabase? = getWritableDatabase()
        if (db == null) {
            return
        }
        try {
            db.execSQL(sql)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun execSQL(sql: String, arg: Array<Any>) {
        val db: SQLiteDatabase? = getWritableDatabase()
        if (db == null) {
            return
        }
        try {
            db.execSQL(sql, arg)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 给表增加一列
     */
    @Throws(SQLException::class)
    fun addColumn(column: String?, append: String?) {
        val db: SQLiteDatabase? = getWritableDatabase()
        if (db == null) return
        try {
            db.execSQL("ALTER TABLE " + getTableName() + " DROP COLUMN " + column + ";")
        } catch (e: Exception) {
            e.printStackTrace()
        }

        try {
            db.execSQL("ALTER TABLE " + getTableName() + " ADD COLUMN " + column + " " + append + ";")
        } catch (e: SQLException) {
            e.printStackTrace()
            val strException = e.message
            if (strException!!.contains("duplicate column name")) {
                // 如果是这个异常信息，表示数据库中已经有这个字段了，这是正常的，不会对数据有异常行为
                return
            } else {
                // throw e;
            }
        }
    }

    private fun checkTable() {
        if (!checkTable.get()) {
            checkTable.set(true)
            createTable()
        }
    }


    /**
     * 表被创建完后回调
     *
     * @param db
     */
    fun onCreated(db: SQLiteOpenHelper?) {
    }

    /**
     * 表被升级更新后回调
     *
     * @param db
     */
    fun onUpgraded(db: SQLiteOpenHelper?, oldDBVersion: Int, newDBVersion: Int) {
        LogUtils.d(getTableName(), getTableName() + " Upgraded!")
        if (!tableIsExist()) {
            LogUtils.e(
                getTableName(),
                getTableName() + " is not Exist! After add a new table,please upgrade DataBase version!"
            )
        }
    }
}