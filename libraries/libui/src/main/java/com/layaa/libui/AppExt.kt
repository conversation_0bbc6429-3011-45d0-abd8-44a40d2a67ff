package com.layaa.libui

import android.graphics.Color
import android.graphics.drawable.Drawable
import android.text.StaticLayout
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.StringRes
import androidx.core.graphics.toColorInt
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.layaa.language.LanguageController
import com.layaa.libui.utils.UIUtils
import com.layaa.libutils.UtilKit
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.suspendCancellableCoroutine
import java.text.NumberFormat
import java.util.Locale
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException


fun Int?.toEnString(): String {
    return String.format(Locale.ENGLISH, "%d", this ?: return "")
}

fun Long?.toEnString(): String {

    return String.format(Locale.ENGLISH, "%d", this ?: return "")
}

val Int.dp
    get() = UIUtils.getPixels(this.toFloat())

val Float.dp
    get() = UIUtils.getPixels(this)

fun String?.parseColor(fallback: Int = Color.WHITE): Int {
    return try {
        this?.toColorInt() ?: fallback
    } catch (t: Throwable) {
        fallback
    }
}

fun getString(@StringRes resId: Int, vararg args: Any?): String {
    if (args.isNotEmpty()) {
        return String.format(Locale.ENGLISH, UtilKit.getString(resId), *args)
    }
    return UtilKit.getString(resId)
}

fun String?.currencyFormat(): String {
    return try {
        this?.toDoubleOrNull()?.toLong()?.currencyFormat() ?: "0"
    } catch (t: Throwable) {
        "0"
    }
}

fun Long?.currencyFormat(): String {
    if (this == null) {
        return "0"
    }
    if (this < 1000000) {
        return this.toString()
    }
//    if (count < 1000000) {
//        var tpNum = count / 1000.0
//        if (tpNum >= 1000) {
//            tpNum = Math.floor(tpNum)
//        } else {
//            tpNum = Math.floor(count / (1000.0 / 100)) / 100.0
//        }
//        return "$tpNum" + "K"
//    }
    if (this < 1000000000) {
        var tpNum = this / 1000000.0
        if (tpNum >= 1000) {
            tpNum = Math.floor(tpNum)
        } else {
            tpNum = Math.floor(this / (1000000.0 / 100)) / 100.0
        }
        return "$tpNum" + "M"
    }

    var tpNum = this / 1000000000.0
    if (tpNum >= 1000) {
        tpNum = Math.floor(tpNum)
    } else {
        tpNum = Math.floor(this / (1000000000.0 / 100)) / 100.0
    }

    if (this > 999999999999) {
        return "999.99+B"
    }
    return "$tpNum" + "B"
}

fun String.formatNumberString(): String {
    return if (this.contains('.')) {
        val parts = this.split('.')
        // 如果小数部分全是0，则返回整数部分；否则返回原串
        if (parts.size == 2 && parts[1].toIntOrNull() == 0) parts[0] else this
    } else {
        this
    }
}

fun Long.thousandSeparatedUnit(): String? {
    return createThousandFormatter().format(this)
}

fun Double.thousandSeparatedUnit(): String? {
    return createThousandFormatter().format(this)
}

private fun createThousandFormatter(): NumberFormat {
    return NumberFormat.getInstance(createLocale())
}

fun createLocale(): Locale {
    val language: String = LanguageController.getLanguage()
    val locale = if (language == "ar") {
        Locale.US // 阿语下，也使用 US 的数字格式
    } else {
        Locale.forLanguageTag(language)
    }
    return locale
}

fun TextView.calculateLayout(text: String, maxWidth: Int): StaticLayout {
    val textPaint = this.paint
    val staticLayout = StaticLayout(
        text,
        textPaint,
        maxWidth,
        android.text.Layout.Alignment.ALIGN_NORMAL,
        this.lineSpacingMultiplier,
        this.lineSpacingExtra,
        this.includeFontPadding
    )
    return staticLayout
}

inline fun View.applyWindowInsets(
    crossinline block: (View, WindowInsetsCompat) -> Unit
) {
    ViewCompat.setOnApplyWindowInsetsListener(this) { v, insets ->
        block(v, insets)
        insets
    }

    if (isAttachedToWindow) {
        ViewCompat.requestApplyInsets(this)
    } else {
        addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(v: View) {
                v.removeOnAttachStateChangeListener(this)
                ViewCompat.requestApplyInsets(v)
            }

            override fun onViewDetachedFromWindow(v: View) = Unit
        })
    }
}

suspend fun ImageView.loadDrawableAsync(
    resource: Any
): Drawable = suspendCancellableCoroutine { cont ->
    val request = Glide.with(this)
        .asDrawable()
        .load(resource)

    val target = object : CustomTarget<Drawable>() {
        override fun onResourceReady(resource: Drawable, transition: Transition<in Drawable>?) {
            if (cont.isActive) {
                cont.resume(resource)
            }
        }

        override fun onLoadCleared(placeholder: Drawable?) {
            if (cont.isActive) {
                cont.resumeWithException(CancellationException("Glide load cleared"))
            }
        }

        override fun onLoadFailed(errorDrawable: Drawable?) {
            if (cont.isActive) {
                cont.resumeWithException(Exception("Glide load failed"))
            }
        }
    }

    cont.invokeOnCancellation {
        Glide.with(this).clear(target)
    }

    request.into(target)
}
