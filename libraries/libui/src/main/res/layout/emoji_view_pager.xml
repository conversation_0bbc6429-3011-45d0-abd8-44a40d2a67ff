<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/pagerEmoji"
        android:layout_width="match_parent"
        android:layout_height="230dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabEmoji"
        app:layout_constraintTop_toBottomOf="@id/pagerEmoji"
        app:layout_constraintStart_toStartOf="@id/pagerEmoji"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        app:tabMode="scrollable"
        app:tabIndicatorHeight="0dp"
        app:tabMinWidth="60dp"
        app:tabPaddingEnd="0dp"
        app:tabPaddingStart="0dp"
        app:tabPaddingTop="3dp"
        app:tabPaddingBottom="3dp" />

</androidx.constraintlayout.widget.ConstraintLayout>