package com.google.android.material.tabs

/**
 *<AUTHOR>
 *@date 2020/9/21
 *@des Callback interface invoked when a tab's selection state changes.
 **/
interface OnTabSelectedListener {
    /**
     * Called when a tab enters the selected state.
     *
     * @param tab The tab that was selected
     */
    fun onTabSelected(tab: ViewPageTabLayout.Tab?)

    /**
     * Called when a tab exits the selected state.
     *
     * @param tab The tab that was unselected
     */
    fun onTabUnselected(tab: ViewPageTabLayout.Tab?)

    /**
     * Called when a tab that is already selected is chosen again by the user. Some applications
     * may use this action to return to the top level of a category.
     *
     * @param tab The tab that was reselected.
     */
    fun onTabReselected(tab: ViewPageTabLayout.Tab?)
}