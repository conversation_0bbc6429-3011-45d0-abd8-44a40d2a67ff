package com.google.android.material.tabs

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.animation.ValueAnimator.AnimatorUpdateListener
import android.annotation.SuppressLint
import android.annotation.TargetApi
import android.content.Context
import android.content.res.ColorStateList
import android.database.DataSetObserver
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.os.Build
import android.text.Layout
import android.text.TextUtils
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.FrameLayout
import android.widget.HorizontalScrollView
import android.widget.LinearLayout
import android.widget.Toast
import androidx.annotation.ColorInt
import androidx.annotation.IntDef
import androidx.annotation.StringRes
import androidx.appcompat.app.ActionBar
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.util.Pools
import androidx.core.view.GravityCompat
import androidx.core.view.ViewCompat
import androidx.interpolator.view.animation.FastOutSlowInInterpolator
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.PagerAdapter
import androidx.viewpager.widget.ViewPager
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.R
import com.google.android.material.animation.AnimationUtils
import java.lang.annotation.Retention
import java.lang.annotation.RetentionPolicy
import java.util.*

/**
 *<AUTHOR>
 *@date 2020/9/21
 *@des
 **/
class ViewPageTabLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : HorizontalScrollView(context, attrs, defStyleAttr) {

    companion object {
        private const val DEFAULT_HEIGHT_WITH_TEXT_ICON = 72 // dps

        private const val DEFAULT_GAP_TEXT_ICON = 8 // dps

        private const val INVALID_WIDTH = -1
        private const val DEFAULT_HEIGHT = 48 // dps

        private const val TAB_MIN_WIDTH_MARGIN = 56 //dps

        private const val FIXED_WRAP_GUTTER_MIN = 16 //dps

        private const val MOTION_NON_ADJACENT_OFFSET = 24

        private const val ANIMATION_DURATION = 300

        const val INVALID_POSITION = -1

        /**
         * Scrollable tabs display a subset of tabs at any given moment, and can contain longer tab
         * labels and a larger number of tabs. They are best used for browsing contexts in touch
         * interfaces when users don’t need to directly compare the tab labels.
         *
         * @see .setTabMode
         * @see .getTabMode
         */
        const val MODE_SCROLLABLE = 0

        /**
         * Fixed tabs display all tabs concurrently and are best used with content that benefits from
         * quick pivots between tabs. The maximum number of tabs is limited by the view’s width.
         * Fixed tabs have equal width, based on the widest tab label.
         *
         * @see .setTabMode
         * @see .getTabMode
         */
        const val MODE_FIXED = 1

        private val sTabPool: Pools.Pool<Tab> = Pools.SynchronizedPool<Tab>(16)

        /**
         * Gravity used to fill the [TabLayout] as much as possible. This option only takes effect
         * when used with [.MODE_FIXED].
         *
         * @see .setTabGravity
         * @see .getTabGravity
         */
        const val GRAVITY_FILL = 0

        /**
         * Gravity used to lay out the tabs in the center of the [TabLayout].
         *
         * @see .setTabGravity
         * @see .getTabGravity
         */
        const val GRAVITY_CENTER = 1
    }

    /**
     * @hide
     */
    @IntDef(value = [MODE_SCROLLABLE, MODE_FIXED])
    @Retention(RetentionPolicy.SOURCE)
    annotation class Mode

    private val mTabs: ArrayList<Tab> = ArrayList()
    private var mSelectedTab: Tab? = null

    private var mTabStrip: SlidingTabStrip

    var mTabPaddingStart = 0
    var mTabPaddingTop = 0
    var mTabPaddingEnd = 0
    var mTabPaddingBottom = 0

    var mTabTextAppearance = 0
    var mTabTextColors: ColorStateList? = null
    var mTabTextSize = 0f
    var mTabTextMultiLineSize = 0f

    var mTabBackgroundResId = 0

    var mTabMaxWidth = Int.MAX_VALUE
    private var mRequestedTabMinWidth = 0
    private var mRequestedTabMaxWidth = 0
    private var mScrollableTabMinWidth = 0

    private var mContentInsetStart = 0

    var mTabGravity = 0
    var mMode = 0
    var mEnableScale = true

    private var useCustomTab = false

    private var mSelectedListener: OnTabSelectedListener? = null
    private val mSelectedListeners: ArrayList<OnTabSelectedListener> =
        ArrayList<OnTabSelectedListener>()
    private var mCurrentVpSelectedListener: OnTabSelectedListener? = null

    private var mScrollAnimator: ValueAnimator? = null

    var mViewPager: ViewPager2? = null
    private var mPagerAdapter: TitlePagerAdapter? = null
    private var mPagerAdapterObserver: RecyclerView.AdapterDataObserver? = null
    private var mPageChangeListener: TabLayoutOnPageChangeListener? = null
    private var mSetupViewPagerImplicitly = false

    // Pool we use as a simple RecyclerBin
    private val mTabViewPool: Pools.Pool<TabView> = Pools.SimplePool<TabView>(12)

    init {

        // Disable the Scroll Bar

        // Disable the Scroll Bar
        isHorizontalScrollBarEnabled = false

        // Add the TabStrip

        // Add the TabStrip
        mTabStrip = SlidingTabStrip(context)
        super.addView(
            mTabStrip, 0, LayoutParams(
                LayoutParams.WRAP_CONTENT, LayoutParams.MATCH_PARENT
            )
        )

        if (attrs != null) {
            val a = context.obtainStyledAttributes(
                attrs, R.styleable.TabLayout,
                defStyleAttr, R.style.Widget_Design_TabLayout
            )

            mTabStrip.setSelectedIndicatorHeight(
                a.getDimensionPixelSize(R.styleable.TabLayout_tabIndicatorHeight, 0)
            )
            mTabStrip.setSelectedIndicatorColor(
                a.getColor(
                    R.styleable.TabLayout_tabIndicatorColor,
                    0
                )
            )

            mTabPaddingStart = a
                .getDimensionPixelSize(R.styleable.TabLayout_tabPadding, 0)
                .also { mTabPaddingBottom = it }.also { mTabPaddingEnd = it }
                .also { mTabPaddingTop = it }
            mTabPaddingStart = a.getDimensionPixelSize(
                R.styleable.TabLayout_tabPaddingStart,
                mTabPaddingStart
            )
            mTabPaddingTop = a.getDimensionPixelSize(
                R.styleable.TabLayout_tabPaddingTop,
                mTabPaddingTop
            )
            mTabPaddingEnd = a.getDimensionPixelSize(
                R.styleable.TabLayout_tabPaddingEnd,
                mTabPaddingEnd
            )
            mTabPaddingBottom = a.getDimensionPixelSize(
                R.styleable.TabLayout_tabPaddingBottom,
                mTabPaddingBottom
            )

            mTabTextAppearance = a.getResourceId(
                R.styleable.TabLayout_tabTextAppearance,
                0
            )

            // Text colors/sizes come from the text appearance first

            // Text colors/sizes come from the text appearance first
            val ta = context.obtainStyledAttributes(
                mTabTextAppearance,
                R.styleable.TextAppearance
            )
            try {
                mTabTextSize = ta.getDimensionPixelSize(
                    R.styleable.TextAppearance_android_textSize, 0
                ).toFloat()
                mTabTextColors = ta.getColorStateList(
                    R.styleable.TextAppearance_android_textColor
                )
            } finally {
                ta.recycle()
            }

            if (a.hasValue(R.styleable.TabLayout_tabTextColor)) {
                // If we have an explicit text color set, use it instead
                mTabTextColors = a.getColorStateList(R.styleable.TabLayout_tabTextColor)
            }

            if (a.hasValue(R.styleable.TabLayout_tabSelectedTextColor)) {
                // We have an explicit selected text color set, so we need to make merge it with the
                // current colors. This is exposed so that developers can use theme attributes to set
                // this (theme attrs in ColorStateLists are Lollipop+)
                val selected = a.getColor(R.styleable.TabLayout_tabSelectedTextColor, 0)
                mTabTextColors = createColorStateList(mTabTextColors!!.defaultColor, selected)
            }
            mRequestedTabMinWidth = a.getDimensionPixelSize(
                R.styleable.TabLayout_tabMinWidth,
                INVALID_WIDTH
            )
            mRequestedTabMaxWidth = a.getDimensionPixelSize(
                R.styleable.TabLayout_tabMaxWidth,
                INVALID_WIDTH
            )
            mTabBackgroundResId = a.getResourceId(R.styleable.TabLayout_tabBackground, 0)
            mContentInsetStart = a.getDimensionPixelSize(R.styleable.TabLayout_tabContentStart, 0)
            mMode = a.getInt(R.styleable.TabLayout_tabMode, MODE_FIXED)
            mTabGravity = a.getInt(R.styleable.TabLayout_tabGravity, GRAVITY_FILL)
            a.recycle()
        }


        val res = resources
        mTabTextMultiLineSize =
            res.getDimensionPixelSize(R.dimen.design_tab_text_size_2line).toFloat()
        mScrollableTabMinWidth = res.getDimensionPixelSize(R.dimen.design_tab_scrollable_min_width)

        // Now apply the tab mode and gravity

        // Now apply the tab mode and gravity
        applyModeAndGravity()
    }

    fun setTabStripGravity(gravity: Int) {
        if (mTabStrip != null) {
            val layoutParams = mTabStrip.layoutParams as LayoutParams
            if (layoutParams != null) {
                layoutParams.gravity = gravity
                mTabStrip.requestLayout()
            }
        }
    }

    /**
     * Sets the tab indicator's color for the currently selected tab.
     *
     * @param color color to use for the indicator
     * @attr ref android.support.design.R.styleable#TabLayout_tabIndicatorColor
     */
    fun setSelectedTabIndicatorColor(@ColorInt color: Int) {
        mTabStrip.setSelectedIndicatorColor(color)
    }

    /**
     * Sets the tab indicator's height for the currently selected tab.
     *
     * @param height height to use for the indicator in pixels
     * @attr ref android.support.design.R.styleable#TabLayout_tabIndicatorHeight
     */
    fun setSelectedTabIndicatorHeight(height: Int) {
        mTabStrip.setSelectedIndicatorHeight(height)
    }

    fun setSelectedTabSlidingIndicator(slidingIndicator: ISlidingIndicator?) {
        mTabStrip.setSlidingIndicator(slidingIndicator)
    }

    /**
     * Set the scroll position of the tabs. This is useful for when the tabs are being displayed as
     * part of a scrolling container such as [ViewPager].
     *
     *
     * Calling this method does not update the selected tab, it is only used for drawing purposes.
     *
     * @param position           current scroll position
     * @param positionOffset     Value from [0, 1) indicating the offset from `position`.
     * @param updateSelectedText Whether to update the text's selected state.
     */
    fun setScrollPosition(position: Int, positionOffset: Float, updateSelectedText: Boolean) {
        setScrollPosition(position, positionOffset, updateSelectedText, true)
    }

    fun setScrollPosition(
        position: Int, positionOffset: Float, updateSelectedText: Boolean,
        updateIndicatorPosition: Boolean
    ) {
        val roundedPosition = Math.round(position + positionOffset)
        if (roundedPosition < 0 || roundedPosition >= mTabStrip.childCount) {
            return
        }

        // Set the indicator position, if enabled
        if (updateIndicatorPosition) {
            mTabStrip.setIndicatorPositionFromTabPosition(position, positionOffset)
        }

        mScrollAnimator?.let {
            if (it.isRunning) {
                it.cancel()
            }
        }
        // Now update the scroll position, canceling any running animation

        scrollTo(calculateScrollXForTab(position, positionOffset), 0)

        // Update the 'selected state' view as we scroll, if enabled
        if (updateSelectedText) {
            setSelectedTabView(roundedPosition)
        }
    }

    private fun getScrollPosition(): Float {
        return mTabStrip.getIndicatorPosition()
    }

    fun addCustomTabInfo(tabInfo: SimpleTextTabInfo?) {
        useCustomTab = true
        val tab = newTab()
        tab.setTabInfo(tabInfo)
        addTab(tab)
    }

    /**
     * Add a tab to this layout. The tab will be added at the end of the list.
     * If this is the first tab to be added it will become the selected tab.
     *
     * @param tab Tab to add
     */
    fun addTab(tab: Tab) {
        addTab(tab, mTabs.isEmpty())
    }

    /**
     * Add a tab to this layout. The tab will be inserted at `position`.
     * If this is the first tab to be added it will become the selected tab.
     *
     * @param tab      The tab to add
     * @param position The new position of the tab
     */
    fun addTab(tab: Tab, position: Int) {
        addTab(tab, position, mTabs.isEmpty())
    }

    /**
     * Add a tab to this layout. The tab will be added at the end of the list.
     *
     * @param tab         Tab to add
     * @param setSelected True if the added tab should become the selected tab.
     */
    fun addTab(tab: Tab, setSelected: Boolean) {
        addTab(tab, mTabs.size, setSelected)
    }

    /**
     * Add a tab to this layout. The tab will be inserted at `position`.
     *
     * @param tab         The tab to add
     * @param position    The new position of the tab
     * @param setSelected True if the added tab should become the selected tab.
     */
    fun addTab(tab: Tab, position: Int, setSelected: Boolean) {
        require(!(tab.mParent !== this)) { "Tab belongs to a different TabLayout." }
        configureTab(tab, position)
        addTabView(tab)
        if (setSelected) {
            tab.select()
        }
    }

    private fun addTabFromItemView(item: TabItem) {
        val tab: Tab = newTab()
        if (item.text != null) {
            tab.setText(item.text)
        }
        if (!TextUtils.isEmpty(item.getContentDescription())) {
            tab.setContentDescription(item.getContentDescription())
        }
        addTab(tab)
    }


    @Deprecated(
        """Use {@link #addOnTabSelectedListener(OnTabSelectedListener)} and
      {@link #removeOnTabSelectedListener(OnTabSelectedListener)}."""
    )
    fun setOnTabSelectedListener(listener: OnTabSelectedListener) {
        // The logic in this method emulates what we had before support for multiple
        // registered listeners.
        if (mSelectedListener != null) {
            removeOnTabSelectedListener(mSelectedListener!!)
        }
        // Update the deprecated field so that we can remove the passed listener the next
        // time we're called
        mSelectedListener = listener
        if (listener != null) {
            addOnTabSelectedListener(listener)
        }
    }

    /**
     * Add a [TabLayout.OnTabSelectedListener] that will be invoked when tab selection
     * changes.
     *
     *
     *
     * Components that add a listener should take care to remove it when finished via
     * [.removeOnTabSelectedListener].
     *
     * @param listener listener to add
     */
    fun addOnTabSelectedListener(listener: OnTabSelectedListener) {
        if (!mSelectedListeners.contains(listener)) {
            mSelectedListeners.add(listener)
        }
    }

    /**
     * Remove the given [TabLayout.OnTabSelectedListener] that was previously added via
     * [.addOnTabSelectedListener].
     *
     * @param listener listener to remove
     */
    fun removeOnTabSelectedListener(listener: OnTabSelectedListener) {
        mSelectedListeners.remove(listener)
    }

    /**
     * Remove all previously added [TabLayout.OnTabSelectedListener]s.
     */
    fun clearOnTabSelectedListeners() {
        mSelectedListeners.clear()
    }

    /**
     * Create and return a new [Tab]. You need to manually add this using
     * [.addTab] or a related method.
     *
     * @return A new Tab
     * @see .addTab
     */
    fun newTab(): Tab {
        var tab: Tab? = sTabPool.acquire()
        if (tab == null) {
            tab = Tab()
        }
        tab.mParent = this
        tab.mView = createTabView(tab)
        return tab
    }

    /**
     * Returns the number of tabs currently registered with the action bar.
     *
     * @return Tab count
     */
    fun getTabCount(): Int {
        return mTabs.size
    }

    /**
     * Returns the tab at the specified index.
     */
    fun getTabAt(index: Int): Tab? {
        return if (index < 0 || index >= getTabCount()) null else mTabs[index]
    }

    /**
     * Returns the position of the current selected tab.
     *
     * @return selected tab position, or `-1` if there isn't a selected tab.
     */
    fun getSelectedTabPosition(): Int {
        return mSelectedTab?.getPosition() ?: -1
    }

    /**
     * Remove a tab from the layout. If the removed tab was selected it will be deselected
     * and another tab will be selected if present.
     *
     * @param tab The tab to remove
     */
    fun removeTab(tab: Tab) {
        require(!(tab.mParent !== this)) { "Tab does not belong to this TabLayout." }
        removeTabAt(tab.getPosition())
    }

    /**
     * Remove a tab from the layout. If the removed tab was selected it will be deselected
     * and another tab will be selected if present.
     *
     * @param position Position of the tab to remove
     */
    fun removeTabAt(position: Int) {
        val selectedTabPosition = mSelectedTab?.getPosition() ?: 0
        removeTabViewAt(position)
        val removedTab: Tab = mTabs.removeAt(position)
        if (removedTab != null) {
            removedTab.reset()
            sTabPool.release(removedTab)
        }
        val newTabCount = mTabs.size
        for (i in position until newTabCount) {
            mTabs[i].setPosition(i)
        }
        if (selectedTabPosition == position) {
            selectTab(if (mTabs.isEmpty()) null else mTabs[Math.max(0, position - 1)])
        }
    }

    /**
     * Remove all tabs from the action bar and deselect the current tab.
     */
    fun removeAllTabs() {
        // Remove all the views
        for (i in mTabStrip.childCount - 1 downTo 0) {
            removeTabViewAt(i)
        }
        val i: MutableIterator<Tab> = mTabs.iterator()
        while (i.hasNext()) {
            val tab: Tab = i.next()
            i.remove()
            tab.reset()
            sTabPool.release(tab)
        }
        mSelectedTab = null
    }

    /**
     * Set the behavior mode for the Tabs in this layout. The valid input options are:
     *
     *  * [.MODE_FIXED]: Fixed tabs display all tabs concurrently and are best used
     * with content that benefits from quick pivots between tabs.
     *  * [.MODE_SCROLLABLE]: Scrollable tabs display a subset of tabs at any given moment,
     * and can contain longer tab labels and a larger number of tabs. They are best used for
     * browsing contexts in touch interfaces when users don’t need to directly compare the tab
     * labels. This mode is commonly used with a [ViewPager].
     *
     *
     * @param mode one of [.MODE_FIXED] or [.MODE_SCROLLABLE].
     * @attr ref android.support.design.R.styleable#TabLayout_tabMode
     */
    fun setTabMode(@Mode mode: Int) {
        if (mode != mMode) {
            mMode = mode
            applyModeAndGravity()
        }
    }

    /**
     * Returns the current mode used by this [TabLayout].
     *
     * @see .setTabMode
     */
    @Mode
    fun getTabMode(): Int {
        return mMode
    }

    /**
     * Set the gravity to use when laying out the tabs.
     *
     * @param gravity one of [.GRAVITY_CENTER] or [.GRAVITY_FILL].
     * @attr ref android.support.design.R.styleable#TabLayout_tabGravity
     */
    fun setTabGravity(@TabGravity gravity: Int) {
        if (mTabGravity != gravity) {
            mTabGravity = gravity
            applyModeAndGravity()
        }
    }

    /**
     * The current gravity used for laying out tabs.
     *
     * @return one of [.GRAVITY_CENTER] or [.GRAVITY_FILL].
     */
    @TabGravity
    fun getTabGravity(): Int {
        return mTabGravity
    }

    /**
     * enable scale effect
     */
    fun setEnableScale(mEnableScale: Boolean) {
        val isChanged = this.mEnableScale != mEnableScale
        this.mEnableScale = mEnableScale
        if (isChanged) {
            updateAllTabs()
        }
    }

    fun isEnableScale(): Boolean {
        return mEnableScale
    }

    /**
     * Sets the text colors for the different states (normal, selected) used for the tabs.
     *
     * @see .getTabTextColors
     */
    fun setTabTextColors(textColor: ColorStateList?) {
        if (mTabTextColors !== textColor) {
            mTabTextColors = textColor
            updateAllTabs()
        }
    }

    /**
     * Gets the text colors for the different states (normal, selected) used for the tabs.
     */
    fun getTabTextColors(): ColorStateList? {
        return mTabTextColors
    }

    /**
     * Sets the text colors for the different states (normal, selected) used for the tabs.
     *
     * @attr ref android.support.design.R.styleable#TabLayout_tabTextColor
     * @attr ref android.support.design.R.styleable#TabLayout_tabSelectedTextColor
     */
    fun setTabTextColors(normalColor: Int, selectedColor: Int) {
        setTabTextColors(createColorStateList(normalColor, selectedColor))
    }

    /**
     * The one-stop shop for setting up this [TabLayout] with a [ViewPager].
     *
     *
     *
     * This is the same as calling [.setupWithViewPager] with
     * auto-refresh enabled.
     *
     * @param viewPager the ViewPager to link to, or `null` to clear any previous link
     */
    fun setupWithViewPager(viewPager: ViewPager2?) {
        setupWithViewPager(viewPager, true)
    }

    /**
     * The one-stop shop for setting up this [TabLayout] with a [ViewPager].
     *
     *
     *
     * This method will link the given ViewPager and this TabLayout together so that
     * changes in one are automatically reflected in the other. This includes scroll state changes
     * and clicks. The tabs displayed in this layout will be populated
     * from the ViewPager adapter's page titles.
     *
     *
     *
     * If `autoRefresh` is `true`, any changes in the [PagerAdapter] will
     * trigger this layout to re-populate itself from the adapter's titles.
     *
     *
     *
     * If the given ViewPager is non-null, it needs to already have a
     * [PagerAdapter] set.
     *
     * @param viewPager   the ViewPager to link to, or `null` to clear any previous link
     * @param autoRefresh whether this layout should refresh its contents if the given ViewPager's
     * content changes
     */
    fun setupWithViewPager(viewPager: ViewPager2?, autoRefresh: Boolean) {
        setupWithViewPager(viewPager, autoRefresh, false)
    }

    private fun setupWithViewPager(
        viewPager: ViewPager2?, autoRefresh: Boolean,
        implicitSetup: Boolean
    ) {
        if (mViewPager != null) {
            // If we've already been setup with a ViewPager, remove us from it
            if (mPageChangeListener != null) {
                mViewPager?.unregisterOnPageChangeCallback(mPageChangeListener!!)
            }
        }
        if (mCurrentVpSelectedListener != null) {
            // If we already have a tab selected listener for the ViewPager, remove it
            removeOnTabSelectedListener(mCurrentVpSelectedListener!!)
            mCurrentVpSelectedListener = null
        }
        if (viewPager != null) {
            mViewPager = viewPager

            // Add our custom OnPageChangeListener to the ViewPager
            if (mPageChangeListener == null) {
                mPageChangeListener = TabLayoutOnPageChangeListener(this)
            }
            mPageChangeListener?.reset()
            viewPager.registerOnPageChangeCallback(mPageChangeListener!!)

            // Now we'll add a tab selected listener to set ViewPager's current item
            mCurrentVpSelectedListener = ViewPagerOnTabSelectedListener(viewPager)
            addOnTabSelectedListener(mCurrentVpSelectedListener!!)
            val adapter = viewPager.adapter
            adapter?.let { setPagerAdapter(it as TitlePagerAdapter, autoRefresh) }

            // Now update the scroll position to match the ViewPager's current item
            setScrollPosition(viewPager.currentItem, 0f, true)
        } else {
            // We've been given a null ViewPager so we need to clear out the internal state,
            // listeners and observers
            mViewPager = null
            setPagerAdapter(null, false)
        }
        mSetupViewPagerImplicitly = implicitSetup
    }


    @Deprecated(
        """Use {@link #setupWithViewPager(ViewPager)} to link a TabLayout with a ViewPager
      together. When that method is used, the TabLayout will be automatically updated
      when the {@link PagerAdapter} is changed."""
    )
    fun setTabsFromPagerAdapter(adapter: TitlePagerAdapter?) {
        setPagerAdapter(adapter, false)
    }

    override fun shouldDelayChildPressedState(): Boolean {
        // Only delay the pressed state if the tabs can scroll
        return getTabScrollRange() > 0
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        if (mViewPager == null) {
            // If we don't have a ViewPager already, check if our parent is a ViewPager to
            // setup with it automatically
            val vp = parent
            if (vp is ViewPager2) {
                // If we have a ViewPager parent and we've been added as part of its decor, let's
                // assume that we should automatically setup to display any titles
                setupWithViewPager(vp, true, true)
            }
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        if (mSetupViewPagerImplicitly) {
            // If we've been setup with a ViewPager implicitly, let's clear out any listeners, etc
            setupWithViewPager(null)
            mSetupViewPagerImplicitly = false
        }
    }

    private fun getTabScrollRange(): Int {
        return Math.max(
            0, mTabStrip.width - width - paddingLeft
                    - paddingRight
        )
    }

    fun setPagerAdapter(adapter: TitlePagerAdapter?, addObserver: Boolean) {
        mPagerAdapter = adapter
        /*if (mPagerAdapter != null && mPagerAdapterObserver != null) {
            // If we already have a PagerAdapter, unregister our observer
            mPagerAdapter?.unregisterAdapterDataObserver(mPagerAdapterObserver!!)
        }

        if (addObserver && adapter != null) {
            // Register our observer on the new adapter
            if (mPagerAdapterObserver == null) {
                mPagerAdapterObserver = PagerAdapterObserver()
            }
            adapter.registerAdapterDataObserver(mPagerAdapterObserver!!)
        }*/

        // Finally make sure we reflect the new adapter
        populateFromPagerAdapter()
    }

    fun populateFromPagerAdapter() {
        if (useCustomTab) {
            for (i in mTabs.indices) {
                val tab = mTabs[i]
                tab.setText(mPagerAdapter?.getPagerTitle(i))
            }
        } else {
            removeAllTabs()
            if (mPagerAdapter != null) {
                val adapterCount = mPagerAdapter?.itemCount ?: 0
                for (i in 0 until adapterCount) {
                    addTab(newTab().setText(mPagerAdapter?.getPagerTitle(i)))
                }

                // Make sure we reflect the currently set ViewPager item
                if (mViewPager != null && adapterCount > 0) {
                    val curItem = mViewPager!!.currentItem
                    if (curItem != getSelectedTabPosition() && curItem < getTabCount()) {
                        selectTab(getTabAt(curItem))
                    }
                }
            }
        }
    }

    private fun updateAllTabs() {
        var i = 0
        val z = mTabs.size
        while (i < z) {
            mTabs[i].updateView()
            i++
        }
    }

    private fun createTabView(tab: Tab): TabView? {
        var tabView: TabView? = mTabViewPool.acquire()
        if (tabView == null) {
            tabView = TabView(context)
        }
        tabView.setTab(tab)
        tabView.setFocusable(true)
        tabView.setMinimumWidth(getTabMinWidth())
        return tabView
    }

    private fun configureTab(tab: Tab, position: Int) {
        tab.setPosition(position)
        mTabs.add(position, tab)
        val count = mTabs.size
        for (i in position + 1 until count) {
            mTabs[i].setPosition(i)
        }
    }

    private fun addTabView(tab: Tab) {
        val tabView: TabView = tab.mView ?: return
        mTabStrip.addView(tabView, tab.getPosition(), createLayoutParamsForTabs())
    }

    override fun addView(child: View) {
        addViewInternal(child)
    }

    override fun addView(child: View, index: Int) {
        addViewInternal(child)
    }

    fun addView(child: View, params: LayoutParams?) {
        addViewInternal(child)
    }

     fun addView(child: View, index: Int, params: LayoutParams?) {
        addViewInternal(child)
    }

    private fun addViewInternal(child: View) {
        if (child is TabItem) {
            addTabFromItemView(child as TabItem)
        } else {
            throw IllegalArgumentException("Only TabItem instances can be added to TabLayout")
        }
    }

    private fun createLayoutParamsForTabs(): LayoutParams? {
        val lp = LayoutParams(
            LayoutParams.WRAP_CONTENT, LayoutParams.MATCH_PARENT
        )
        updateTabViewLayoutParams(lp)
        return lp
    }

    private fun updateTabViewLayoutParams(lp: LayoutParams) {
        if (mMode == MODE_FIXED && mTabGravity == GRAVITY_FILL) {
            lp.width = LayoutParams.MATCH_PARENT
        } else {
            lp.width = LayoutParams.WRAP_CONTENT
        }
    }

    fun dpToPx(dps: Int): Int {
        return Math.round(resources.displayMetrics.density * dps)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        // If we have a MeasureSpec which allows us to decide our height, try and use the default
        // height
        var heightMeasureSpec = heightMeasureSpec
        val idealHeight = dpToPx(getDefaultHeight()) + paddingTop + paddingBottom
        when (MeasureSpec.getMode(heightMeasureSpec)) {
            MeasureSpec.AT_MOST -> heightMeasureSpec = MeasureSpec.makeMeasureSpec(
                Math.min(idealHeight, MeasureSpec.getSize(heightMeasureSpec)),
                MeasureSpec.EXACTLY
            )

            MeasureSpec.UNSPECIFIED -> heightMeasureSpec =
                MeasureSpec.makeMeasureSpec(idealHeight, MeasureSpec.EXACTLY)
        }
        val specWidth = MeasureSpec.getSize(widthMeasureSpec)
        if (MeasureSpec.getMode(widthMeasureSpec) != MeasureSpec.UNSPECIFIED) {
            // If we don't have an unspecified width spec, use the given size to calculate
            // the max tab width
            mTabMaxWidth =
                if (mRequestedTabMaxWidth > 0) mRequestedTabMaxWidth else specWidth - dpToPx(
                    TAB_MIN_WIDTH_MARGIN
                )
        }

        // Now super measure itself using the (possibly) modified height spec
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        if (childCount == 1) {
            // If we're in fixed mode then we need to make the tab strip is the same width as us
            // so we don't scroll
            val child = getChildAt(0)
            var remeasure = false
            when (mMode) {
                MODE_SCROLLABLE ->                     // We only need to resize the child if it's smaller than us. This is similar
                    // to fillViewport
                    remeasure = child.measuredWidth < measuredWidth

                MODE_FIXED ->                     // Resize the child so that it doesn't scroll
                    remeasure = child.measuredWidth != measuredWidth
            }
            if (remeasure) {
                // Re-measure the child with a widthSpec set to be exactly our measure width
                val childHeightMeasureSpec = getChildMeasureSpec(
                    heightMeasureSpec, paddingTop
                            + paddingBottom, child.layoutParams.height
                )
                val childWidthMeasureSpec = MeasureSpec.makeMeasureSpec(
                    measuredWidth, MeasureSpec.EXACTLY
                )
                child.measure(childWidthMeasureSpec, childHeightMeasureSpec)
            }
        }
    }

    private fun removeTabViewAt(position: Int) {
        val view: TabView = mTabStrip.getChildAt(position) as TabView
        mTabStrip.removeViewAt(position)
        if (view != null) {
            view.reset()
            mTabViewPool.release(view)
        }
        requestLayout()
    }

    private fun animateToTab(newPosition: Int) {
        if (newPosition == INVALID_POSITION) {
            return
        }
        if (windowToken == null || !ViewCompat.isLaidOut(this)
            || mTabStrip.childrenNeedLayout()
        ) {
            // If we don't have a window token, or we haven't been laid out yet just draw the new
            // position now
            setScrollPosition(newPosition, 0f, true)
            return
        }
        val startScrollX = scrollX
        val targetScrollX = calculateScrollXForTab(newPosition, 0f)
        if (startScrollX != targetScrollX) {
            if (mScrollAnimator == null) {
                mScrollAnimator = ValueAnimator()
                mScrollAnimator?.interpolator = FastOutSlowInInterpolator()
                mScrollAnimator?.duration = ANIMATION_DURATION.toLong()
                mScrollAnimator?.addUpdateListener(AnimatorUpdateListener { animator ->
                    scrollTo(
                        animator.animatedValue as Int,
                        0
                    )
                })
            }
            mScrollAnimator?.setIntValues(startScrollX, targetScrollX)
            mScrollAnimator?.start()
        }

        // Now animate the indicator
        mTabStrip.animateIndicatorToPosition(newPosition, ANIMATION_DURATION)
    }

    private fun setSelectedTabView(position: Int) {
        val tabCount = mTabStrip.childCount
        if (position < tabCount) {
            for (i in 0 until tabCount) {
                val child = mTabStrip.getChildAt(i)
                child.isSelected = i == position
            }
        }
    }

    fun selectTab(tab: Tab?) {
        selectTab(tab, true)
    }

    fun selectTab(tab: Tab?, updateIndicator: Boolean) {
        val currentTab: Tab? = mSelectedTab
        if (currentTab == tab) {
            if (currentTab != null) {
                dispatchTabReselected(tab)
                animateToTab(currentTab.getPosition())
            }
        } else {
            val newPosition: Int = tab?.getPosition() ?: INVALID_POSITION
            if (updateIndicator) {
                if ((currentTab == null || currentTab.getPosition() == INVALID_POSITION)
                    && newPosition != INVALID_POSITION
                ) {
                    // If we don't currently have a tab, just draw the indicator
                    setScrollPosition(newPosition, 0f, true)
                } else {
                    animateToTab(newPosition)
                }
                if (newPosition != INVALID_POSITION) {
                    setSelectedTabView(newPosition)
                }
            }
            if (currentTab != null) {
                dispatchTabUnselected(currentTab)
            }
            mSelectedTab = tab
            if (tab != null) {
                dispatchTabSelected(tab)
            }
        }
    }

    private fun dispatchTabSelected(tab: Tab?) {
        for (i in mSelectedListeners.indices.reversed()) {
            mSelectedListeners[i].onTabSelected(tab)
        }
    }

    private fun dispatchTabUnselected(tab: Tab?) {
        for (i in mSelectedListeners.indices.reversed()) {
            mSelectedListeners[i].onTabUnselected(tab)
        }
    }

    private fun dispatchTabReselected(tab: Tab?) {
        for (i in mSelectedListeners.indices.reversed()) {
            mSelectedListeners[i].onTabReselected(tab)
        }
    }

    private fun calculateScrollXForTab(position: Int, positionOffset: Float): Int {
        if (mMode == MODE_SCROLLABLE) {
            val selectedChild = mTabStrip.getChildAt(position)
            val nextChild =
                if (position + 1 < mTabStrip.childCount) mTabStrip.getChildAt(position + 1) else null
            val selectedWidth = selectedChild?.width ?: 0
            val nextWidth = nextChild?.width ?: 0
            val scrollBase = selectedChild!!.left + selectedChild.width / 2 - width / 2
            val scrollOffset = ((selectedWidth + nextWidth) * positionOffset * 0.5f).toInt()
            return when (ViewCompat.getLayoutDirection(this)) {
                ViewCompat.LAYOUT_DIRECTION_LTR -> {
                    scrollBase + scrollOffset
                }

                else -> {
                    scrollBase - scrollOffset
                }
            }

        }
        return 0
    }

    private fun applyModeAndGravity() {
        var paddingStart = 0
        if (mMode == MODE_SCROLLABLE) {
            // If we're scrollable, or fixed at start, inset using padding
            paddingStart = Math.max(0, mContentInsetStart - mTabPaddingStart)
        }
        ViewCompat.setPaddingRelative(mTabStrip, paddingStart, 0, 0, 0)
        updateTabViews(true)
    }

    fun updateTabViews(requestLayout: Boolean) {
        for (i in 0 until mTabStrip.childCount) {
            val child = mTabStrip.getChildAt(i)
            child.minimumWidth = getTabMinWidth()
            updateTabViewLayoutParams(child.layoutParams as LayoutParams)
            if (requestLayout) {
                child.requestLayout()
            }
        }
    }

    private fun createColorStateList(defaultColor: Int, selectedColor: Int): ColorStateList? {
        val states = arrayOfNulls<IntArray>(2)
        val colors = IntArray(2)
        var i = 0
        states[i] = SELECTED_STATE_SET
        colors[i] = selectedColor
        i++

        // Default enabled state
        states[i] = EMPTY_STATE_SET
        colors[i] = defaultColor
        i++
        return ColorStateList(states, colors)
    }

    private fun getDefaultHeight(): Int {
        val hasIconAndText = false
        return if (hasIconAndText) DEFAULT_HEIGHT_WITH_TEXT_ICON else DEFAULT_HEIGHT
    }

    private fun getTabMinWidth(): Int {
        if (mRequestedTabMinWidth != INVALID_WIDTH) {
            // If we have been given a min width, use it
            return mRequestedTabMinWidth
        }
        // Else, we'll use the default value
        return if (mMode == MODE_SCROLLABLE) mScrollableTabMinWidth else 0
    }

    override fun generateLayoutParams(attrs: AttributeSet?): LayoutParams? {
        // We don't care about the layout params of any views added to us, since we don't actually
        // add them. The only view we add is the SlidingTabStrip, which is done manually.
        // We return the default layout params so that we don't blow up if we're given a TabItem
        // without android:layout_* values.
        return generateDefaultLayoutParams()
    }

    fun getTabMaxWidth(): Int {
        return mTabMaxWidth
    }

    private inner class PagerAdapterObserver internal constructor() : DataSetObserver() {
        override fun onChanged() {
            populateFromPagerAdapter()
        }

        override fun onInvalidated() {
            populateFromPagerAdapter()
        }
    }

    public inner class TabView @JvmOverloads constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
    ) : FrameLayout(context, attrs, defStyleAttr), OnLongClickListener {
        private var mTab: Tab? = null
        private var mCustomView: View? = null

        init {
            if (mTabBackgroundResId != 0) {
                setBackgroundDrawable(
                    AppCompatResources.getDrawable(
                        context!!,
                        mTabBackgroundResId
                    )
                )
            }
            ViewCompat.setPaddingRelative(
                this, mTabPaddingStart, mTabPaddingTop,
                mTabPaddingEnd, mTabPaddingBottom
            )
            isClickable = true
        }

        override fun performClick(): Boolean {
            val value = super.performClick()
            return if (mTab != null) {
                mTab?.select()
                true
            } else {
                value
            }
        }

        override fun setSelected(selected: Boolean) {
            val changed = isSelected != selected
            super.setSelected(selected)
            if (changed && selected && Build.VERSION.SDK_INT < 16) {
                // Pre-JB we need to manually send the TYPE_VIEW_SELECTED event
                sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_SELECTED)
            }

            // Always dispatch this to the child views, regardless of whether the value has
            // changed
            if (mCustomView != null) {
                mCustomView!!.isSelected = selected
            }
        }

        @TargetApi(Build.VERSION_CODES.ICE_CREAM_SANDWICH)
        override fun onInitializeAccessibilityEvent(event: AccessibilityEvent) {
            super.onInitializeAccessibilityEvent(event)
            // This view masquerades as an action bar tab.
            event.className = ActionBar.Tab::class.java.name
        }

        @TargetApi(Build.VERSION_CODES.ICE_CREAM_SANDWICH)
        override fun onInitializeAccessibilityNodeInfo(info: AccessibilityNodeInfo) {
            super.onInitializeAccessibilityNodeInfo(info)
            // This view masquerades as an action bar tab.
            info.className = ActionBar.Tab::class.java.name
        }

        override fun onMeasure(origWidthMeasureSpec: Int, origHeightMeasureSpec: Int) {
            val specWidthSize = MeasureSpec.getSize(origWidthMeasureSpec)
            val specWidthMode = MeasureSpec.getMode(origWidthMeasureSpec)
            val maxWidth: Int = getTabMaxWidth()
            val widthMeasureSpec: Int
            widthMeasureSpec = if (maxWidth > 0 && (specWidthMode == MeasureSpec.UNSPECIFIED
                        || specWidthSize > maxWidth)
            ) {
                // If we have a max width and a given spec which is either unspecified or
                // larger than the max width, update the width spec using the same mode
                MeasureSpec.makeMeasureSpec(mTabMaxWidth, MeasureSpec.AT_MOST)
            } else {
                // Else, use the original width spec
                origWidthMeasureSpec
            }

            // Now lets measure
            super.onMeasure(widthMeasureSpec, origHeightMeasureSpec)
        }

        fun setTab(tab: Tab?) {
            if (tab != mTab) {
                mTab = tab
                update()
            }
        }

        fun reset() {
            setTab(null)
            isSelected = false
        }

        fun update() {
            val tab: Tab? = mTab
            val custom: View? = if (tab != null) tab.getCustomView() else null
            if (custom != null) {
                val customParent = custom.parent
                if (customParent !== this) {
                    if (customParent != null) {
                        (customParent as ViewGroup).removeView(custom)
                    }
                    val lp = LayoutParams(
                        LayoutParams.WRAP_CONTENT,
                        LayoutParams.WRAP_CONTENT
                    )
                    addView(custom, lp)
                }
                val lp = custom.layoutParams as LayoutParams
                if (isEnableScale()) {
                    lp.bottomMargin = dpToPx(8)
                    lp.gravity = Gravity.LEFT or Gravity.BOTTOM
                } else {
                    lp.bottomMargin = 0
                    lp.gravity = Gravity.CENTER
                }
                mCustomView = custom
            } else {
                // We do not have a custom view. Remove one if it already exists
                if (mCustomView != null) {
                    removeView(mCustomView)
                    mCustomView = null
                }
            }

            // Finally update our selected state
            isSelected = tab != null && tab.isSelected()
        }

        override fun onLongClick(v: View?): Boolean {
            val screenPos = IntArray(2)
            val displayFrame = Rect()
            getLocationOnScreen(screenPos)
            getWindowVisibleDisplayFrame(displayFrame)
            val context = context
            val width = width
            val height = height
            val midy = screenPos[1] + height / 2
            var referenceX = screenPos[0] + width / 2
            if (ViewCompat.getLayoutDirection(v!!) == ViewCompat.LAYOUT_DIRECTION_LTR) {
                val screenWidth = context.resources.displayMetrics.widthPixels
                referenceX = screenWidth - referenceX // mirror
            }
            val cheatSheet: Toast = Toast.makeText(
                context, mTab?.getContentDescription(),
                Toast.LENGTH_SHORT
            )
            if (midy < displayFrame.height()) {
                // Show below the tab view
                cheatSheet.setGravity(
                    Gravity.TOP or GravityCompat.END, referenceX,
                    screenPos[1] + height - displayFrame.top
                )
            } else {
                // Show along the bottom center
                cheatSheet.setGravity(Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL, 0, height)
            }
            cheatSheet.show()
            return true
        }

        fun getTab(): Tab? {
            return mTab
        }

        /**
         * Approximates a given lines width with the new provided text size.
         */
        private fun approximateLineWidth(layout: Layout, line: Int, textSize: Float): Float {
            return layout.getLineWidth(line) * (textSize / layout.paint.textSize)
        }
    }

    @IntDef(flag = true, value = [GRAVITY_FILL, GRAVITY_CENTER])
    @Retention(RetentionPolicy.SOURCE)
    internal annotation class TabGravity


    inner class SlidingTabStrip @JvmOverloads constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
    ) : LinearLayout(context, attrs, defStyleAttr) {
        private var mSelectedIndicatorHeight = 0
        private var mSelectedIndicatorPaint: Paint? = null
        private var mSlidingIndicator: ISlidingIndicator? = null

        var mPreviousSelectedPosition = -1
        var mSelectedPosition = -1
        var mSelectionOffset = 0f

        private var mIndicatorLeft = -1
        private var mIndicatorRight = -1

        private var mIndicatorAnimator: ValueAnimator? = null

        init {
            setWillNotDraw(false)
            mSelectedIndicatorPaint = Paint()
        }

        fun setSelectedIndicatorColor(color: Int) {
            if (mSelectedIndicatorPaint!!.color != color) {
                mSelectedIndicatorPaint!!.color = color
                ViewCompat.postInvalidateOnAnimation(this)
            }
        }

        fun setSelectedIndicatorHeight(height: Int) {
            if (mSelectedIndicatorHeight != height) {
                mSelectedIndicatorHeight = height
                ViewCompat.postInvalidateOnAnimation(this)
            }
        }

        fun setSlidingIndicator(slidingIndicator: ISlidingIndicator?) {
            if (mSlidingIndicator !== slidingIndicator) {
                mSlidingIndicator = slidingIndicator
                ViewCompat.postInvalidateOnAnimation(this)
            }
        }

        fun childrenNeedLayout(): Boolean {
            var i = 0
            val z = childCount
            while (i < z) {
                val child = getChildAt(i)
                if (child.width <= 0) {
                    return true
                }
                i++
            }
            return false
        }

        fun setIndicatorPositionFromTabPosition(position: Int, positionOffset: Float) {
            if (mIndicatorAnimator != null && mIndicatorAnimator!!.isRunning) {
                mIndicatorAnimator!!.cancel()
            }
            mPreviousSelectedPosition = mSelectedPosition
            mSelectedPosition = position
            mSelectionOffset = positionOffset
            updateIndicatorPosition(true)
        }

        fun getIndicatorPosition(): Float {
            return mSelectedPosition + mSelectionOffset
        }

        override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
            if (MeasureSpec.getMode(widthMeasureSpec) != MeasureSpec.EXACTLY) {
                super.onMeasure(widthMeasureSpec, heightMeasureSpec)
                // HorizontalScrollView will first measure use with UNSPECIFIED, and then with
                // EXACTLY. Ignore the first call since anything we do will be overwritten anyway
                return
            }
            if (mMode == MODE_FIXED) {
                val count = childCount
                val width =
                    if (count > 0) MeasureSpec.getSize(widthMeasureSpec) / count else MeasureSpec.getSize(
                        widthMeasureSpec
                    )
                for (childIndex in 0 until count) {
                    val child = getChildAt(childIndex)
                    measureChild(
                        child, MeasureSpec.makeMeasureSpec(width, MeasureSpec.EXACTLY),
                        heightMeasureSpec
                    )
                }
                super.onMeasure(widthMeasureSpec, heightMeasureSpec)
                return
            }
            var measuredWidth = 0
            var measuredHeight = 0
            val count = childCount
            for (childIndex in 0 until count) {
                val child = getChildAt(childIndex)
                measureChild(child, widthMeasureSpec, heightMeasureSpec)
                val childLayoutParams = child.layoutParams as MarginLayoutParams
                measuredWidth += (child.measuredWidth
                        + childLayoutParams.leftMargin + childLayoutParams.rightMargin)
                measuredHeight = Math.max(
                    measuredHeight, child.measuredHeight
                            + childLayoutParams.topMargin + childLayoutParams.bottomMargin
                )
            }
            setMeasuredDimension(measuredWidth, measuredHeight)
        }

        override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
            val rtl = ViewCompat.getLayoutDirection(this) != ViewCompat.LAYOUT_DIRECTION_LTR
            var childLeft = paddingLeft
            val childSpace = bottom - top - paddingTop - paddingBottom
            val count = childCount
            var start = 0
            var dir = 1
            if (rtl) {
                start = count - 1
                dir = -1
            }
            for (i in 0 until count) {
                val childIndex = start + i * dir
                val child = getChildAt(childIndex)
                if (child.visibility != GONE) {
                    val childWidth = child.measuredWidth
                    val childHeight = child.measuredHeight
                    val lp = child.layoutParams as MarginLayoutParams
                    childLeft += lp.leftMargin
                    val childTop = paddingTop +
                            (childSpace - childHeight - lp.topMargin - lp.bottomMargin) / 2
                    child.layout(
                        childLeft, childTop,
                        childLeft + childWidth, childTop + childHeight
                    )
                    childLeft += childWidth + lp.rightMargin
                }
            }
            if (mIndicatorAnimator == null || !mIndicatorAnimator!!.isRunning) {
                updateIndicatorPosition(false)
            }
        }

        private fun updateTab(position: Int, percent: Float) {
            val tab: Tab = getTabAt(position) ?: return
            if (tab?.mTabInfo != null) {
                tab?.mTabInfo?.onAnimatorUpdate(tab?.mParent, position, percent)
            }
        }

        private fun updateIndicatorPosition(includeTab: Boolean) {
            val selectedTitle = getChildAt(mSelectedPosition)
            var left: Int
            var right: Int
            if (selectedTitle != null && selectedTitle.width > 0) {
                left = selectedTitle.left
                right = selectedTitle.right
                if (mSelectionOffset > 0f && mSelectedPosition < childCount - 1) {
                    // Draw the selection partway between the tabs
                    val nextTitle = getChildAt(mSelectedPosition + 1)
                    left = (mSelectionOffset * nextTitle.left +
                            (1.0f - mSelectionOffset) * left).toInt()
                    right = (mSelectionOffset * nextTitle.right +
                            (1.0f - mSelectionOffset) * right).toInt()
                }
            } else {
                right = -1
                left = right
            }
            if (includeTab) {
                if (mPreviousSelectedPosition != mSelectedPosition
                    && mPreviousSelectedPosition != mSelectedPosition + 1
                ) {
                    updateTab(mPreviousSelectedPosition, 0f)
                }
                updateTab(mSelectedPosition, Math.abs(1 - mSelectionOffset))
                updateTab(mSelectedPosition + 1, Math.abs(mSelectionOffset))
                requestLayout()
            }
            setIndicatorPosition(left, right)
        }

        fun setIndicatorPosition(left: Int, right: Int) {
            if (left != mIndicatorLeft || right != mIndicatorRight) {
                // If the indicator's left/right has changed, invalidate
                mIndicatorLeft = left
                mIndicatorRight = right
                ViewCompat.postInvalidateOnAnimation(this)
            }
        }

        @SuppressLint("RestrictedApi")
        fun animateIndicatorToPosition(position: Int, duration: Int) {
            if (mIndicatorAnimator != null && mIndicatorAnimator!!.isRunning) {
                mIndicatorAnimator!!.cancel()
            }
            val isRtl = (ViewCompat.getLayoutDirection(this)
                    == ViewCompat.LAYOUT_DIRECTION_RTL)
            val targetView = getChildAt(position)
            if (targetView == null) {
                // If we don't have a view, just update the position now and return
                updateIndicatorPosition(true)
                return
            }
            val targetLeft = targetView.left
            val targetRight = targetView.right
            val startLeft: Int
            val startRight: Int
            if (Math.abs(position - mSelectedPosition) <= 1) {
                // If the views are adjacent, we'll animate from edge-to-edge
                startLeft = mIndicatorLeft
                startRight = mIndicatorRight
            } else {
                // Else, we'll just grow from the nearest edge
                val offset: Int = dpToPx(MOTION_NON_ADJACENT_OFFSET)
                if (position < mSelectedPosition) {
                    // We're going end-to-start
                    if (isRtl) {
                        startRight = targetLeft - offset
                        startLeft = startRight
                    } else {
                        startRight = targetRight + offset
                        startLeft = startRight
                    }
                } else {
                    // We're going start-to-end
                    if (isRtl) {
                        startRight = targetRight + offset
                        startLeft = startRight
                    } else {
                        startRight = targetLeft - offset
                        startLeft = startRight
                    }
                }
            }
            if (startLeft != targetLeft || startRight != targetRight) {
                val isSameTab = mSelectedPosition == position
                mIndicatorAnimator = ValueAnimator()
                val animator = mIndicatorAnimator!!
                animator.interpolator = AnimationUtils.FAST_OUT_SLOW_IN_INTERPOLATOR
                animator.duration = duration.toLong()
                animator.setFloatValues(0f, 1f)
                animator.addUpdateListener { animator ->
                    val fraction = animator.animatedFraction
                    setIndicatorPosition(
                        AnimationUtils.lerp(startLeft, targetView.left, fraction),
                        AnimationUtils.lerp(startRight, targetView.right, fraction)
                    )
                    if (!isSameTab) {
                        updateTab(mSelectedPosition, 1 - fraction)
                    }
                    updateTab(position, fraction)
                    requestLayout()
                }
                animator.addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animator: Animator) {
                        if (!isSameTab) {
                            updateTab(mSelectedPosition, 0f)
                        }
                        updateTab(position, 1f)
                        requestLayout()
                        mPreviousSelectedPosition = mSelectedPosition
                        mSelectedPosition = position
                        mSelectionOffset = 0f
                    }
                })
                animator.start()
            }
        }

        override fun draw(canvas: Canvas) {
            super.draw(canvas)

            // Thick colored underline below the current selection
            if (mIndicatorLeft >= 0 && mIndicatorRight > mIndicatorLeft) {
                if (mSlidingIndicator != null) {
                    mSlidingIndicator?.onDraw(
                        canvas,
                        mIndicatorLeft, 0, mIndicatorRight, height, mSelectionOffset
                    )
                } else {
                    canvas.drawRect(
                        mIndicatorLeft.toFloat(), height - mSelectedIndicatorHeight.toFloat(),
                        mIndicatorRight.toFloat(), height.toFloat(), mSelectedIndicatorPaint!!
                    )
                }
            }
        }
    }

    public inner class Tab {

        private var mTag: Any? = null
        private var mText: CharSequence? = null
        private var mContentDesc: CharSequence? = null
        private var mPosition = INVALID_POSITION
        private var mCustomView: View? = null
        public var mTabInfo: TabInfo? = null

        var mParent: ViewPageTabLayout? = null
        var mView: TabView? = null

        constructor() {
            // Private constructor
        }

        /**
         * @return This Tab's tag object.
         */
        fun getTag(): Any? {
            return mTag
        }

        /**
         * Give this Tab an arbitrary object to hold for later use.
         *
         * @param tag Object to store
         * @return The current instance for call chaining
         */
        fun setTag(tag: Any?): Tab {
            mTag = tag
            return this
        }

        /**
         * Returns the custom view used for this tab.
         */
        internal fun getCustomView(): View? {
            return mCustomView
        }

        fun setTabInfo(tabInfo: TabInfo?): Tab? {
            if (tabInfo == null) return this
            mTabInfo = tabInfo
            mCustomView = mTabInfo?.getCustomView(mParent!!)
            updateView()
            return this
        }

        fun <T : TabInfo?> getTabInfo(): TabInfo? {
            return mTabInfo
        }

        /**
         * Return the current position of this tab in the action bar.
         *
         * @return Current position, or [.INVALID_POSITION] if this tab is not currently in
         * the action bar.
         */
        fun getPosition(): Int {
            return mPosition
        }

        fun setPosition(position: Int) {
            mPosition = position
        }

        /**
         * Return the text of this tab.
         *
         * @return The tab's text
         */
        fun getText(): CharSequence? {
            return mText
        }

        /**
         * Set the text displayed on this tab. Text may be truncated if there is not room to display
         * the entire string.
         *
         * @param text The text to display
         * @return The current instance for call chaining
         */
        fun setText(text: CharSequence?): Tab {
            mText = text
            if (SimpleTextTabInfo::class.java.isInstance(mTabInfo)) {
                (mTabInfo as SimpleTextTabInfo).title = mText
            } else if (mTabInfo == null) {
                setTabInfo(SimpleTextTabInfo(mText))
            } else {
                throw IllegalStateException(
                    "Can not setText with TabInfo=" +
                            mTabInfo?.javaClass?.name
                )
            }
            return this
        }

        /**
         * Set the text displayed on this tab. Text may be truncated if there is not room to display
         * the entire string.
         *
         * @param resId A resource ID referring to the text that should be displayed
         * @return The current instance for call chaining
         */
        fun setText(@StringRes resId: Int): Tab {
            requireNotNull(mParent) { "Tab not attached to a TabLayout" }
            return setText(mParent?.resources?.getText(resId))
        }

        /**
         * Select this tab. Only valid if the tab has been added to the action bar.
         */
        fun select() {
            requireNotNull(mParent) { "Tab not attached to a TabLayout" }
            mParent?.selectTab(this)
        }

        /**
         * Returns true if this tab is currently selected.
         */
        fun isSelected(): Boolean {
            requireNotNull(mParent) { "Tab not attached to a TabLayout" }
            return mParent?.getSelectedTabPosition() == mPosition
        }

        /**
         * Set a description of this tab's content for use in accessibility support. If no content
         * description is provided the title will be used.
         *
         * @param resId A resource ID referring to the description text
         * @return The current instance for call chaining
         * @see .setContentDescription
         * @see .getContentDescription
         */
        fun setContentDescription(@StringRes resId: Int): Tab {
            requireNotNull(mParent) { "Tab not attached to a TabLayout" }
            return setContentDescription(mParent?.getResources()?.getText(resId))
        }

        /**
         * Set a description of this tab's content for use in accessibility support. If no content
         * description is provided the title will be used.
         *
         * @param contentDesc Description of this tab's content
         * @return The current instance for call chaining
         * @see .setContentDescription
         * @see .getContentDescription
         */
        fun setContentDescription(contentDesc: CharSequence?): Tab {
            mContentDesc = contentDesc
            updateView()
            return this
        }

        /**
         * Gets a brief description of this tab's content for use in accessibility support.
         *
         * @return Description of this tab's content
         * @see .setContentDescription
         * @see .setContentDescription
         */
        fun getContentDescription(): CharSequence? {
            return mContentDesc
        }

        fun updateView() {
            mView?.update()
        }

        fun reset() {
            mParent = null
            mView = null
            mTag = null
            mText = null
            mContentDesc = null
            mPosition = INVALID_POSITION
            mCustomView = null
            mTabInfo = null
        }
    }
}