package com.google.android.material.tabs

import androidx.viewpager.widget.ViewPager
import androidx.viewpager2.widget.ViewPager2
import java.lang.ref.WeakReference

/**
 *<AUTHOR>
 *@date 2020/9/21
 *@des
 **/
class TabLayoutOnPageChangeListener : ViewPager2.OnPageChangeCallback {

    private var mTabLayoutRef: WeakReference<ViewPageTabLayout>? = null
    private var mPreviousScrollState = 0
    private var mScrollState = 0

    constructor(tabLayout: ViewPageTabLayout) {
        mTabLayoutRef = WeakReference<ViewPageTabLayout>(tabLayout)
    }

    override fun onPageScrollStateChanged(state: Int) {
        mPreviousScrollState = mScrollState
        mScrollState = state
    }

    override fun onPageScrolled(position: Int, positionOffset: Float,
                                positionOffsetPixels: Int) {
        val tabLayout: ViewPageTabLayout? = mTabLayoutRef!!.get()
        if (tabLayout != null) {
            // Only update the text selection if we're not settling, or we are settling after
            // being dragged
            val updateText = mScrollState != ViewPager.SCROLL_STATE_SETTLING ||
                    mPreviousScrollState == ViewPager.SCROLL_STATE_DRAGGING
            // Update the indicator if we're not settling after being idle. This is caused
            // from a setCurrentItem() call and will be handled by an animation from
            // onPageSelected() instead.
            val updateIndicator = !(mScrollState == ViewPager.SCROLL_STATE_SETTLING
                    && mPreviousScrollState == ViewPager.SCROLL_STATE_IDLE)
            tabLayout.setScrollPosition(position, positionOffset, updateText, updateIndicator)
        }
    }

    override fun onPageSelected(position: Int) {
        val tabLayout: ViewPageTabLayout? = mTabLayoutRef!!.get()
        if (tabLayout != null && tabLayout.getSelectedTabPosition() != position && position < tabLayout.getTabCount()) {
            // Select the tab, only updating the indicator if we're not being dragged/settled
            // (since onPageScrolled will handle that).
            val updateIndicator = (mScrollState == ViewPager.SCROLL_STATE_IDLE
                    || (mScrollState == ViewPager.SCROLL_STATE_SETTLING
                    && mPreviousScrollState == ViewPager.SCROLL_STATE_IDLE))
            tabLayout.selectTab(tabLayout.getTabAt(position), updateIndicator)
        }
    }

    fun reset() {
        mScrollState = ViewPager.SCROLL_STATE_IDLE
        mPreviousScrollState = mScrollState
    }
}