package com.google.android.material.tabs

import androidx.viewpager2.widget.ViewPager2

/**
 *<AUTHOR>
 *@date 2020/9/21
 *@des
 **/
class ViewPagerOnTabSelectedListener : OnTabSelectedListener {
    private var mViewPager: ViewPager2? = null

    constructor(viewPager: ViewPager2?) {
        mViewPager = viewPager
    }

    override fun onTabSelected(tab: ViewPageTabLayout.Tab?) {
        mViewPager!!.currentItem = tab?.getPosition() ?: 0
    }

    override fun onTabUnselected(tab: ViewPageTabLayout.Tab?) {
        // No-op
    }

    override fun onTabReselected(tab: ViewPageTabLayout.Tab?) {
        // No-op
    }
}