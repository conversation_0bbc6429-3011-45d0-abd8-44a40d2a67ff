package com.google.android.material.tabs;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Lifecycle;
import androidx.viewpager2.adapter.FragmentStateAdapter;

/**
 * <AUTHOR>
 * @date 2020/11/6
 * @des
 **/
public abstract class TitlePagerAdapter extends FragmentStateAdapter {
    public TitlePagerAdapter(@NonNull FragmentActivity fragmentActivity) {
        super(fragmentActivity);
    }

    public TitlePagerAdapter(@NonNull Fragment fragment) {
        super(fragment);
    }

    public TitlePagerAdapter(@NonNull FragmentManager fragmentManager, @NonNull Lifecycle lifecycle) {
        super(fragmentManager, lifecycle);
    }

    public abstract CharSequence getPagerTitle(int position);
}
