package com.google.android.material.tabs

import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.widget.TextView

/**
 *<AUTHOR>
 *@date 2020/9/21
 *@des
 **/
abstract class TabInfo {

    companion object {
        const val MAX_SCALE_OFFSET = 0.1F
    }

    private var customView: View? = null

    protected open fun inheritTabLayoutStyle(textView: TextView,
                                             tabLayout: ViewPageTabLayout) {
        textView.gravity = Gravity.CENTER


        //不使用系统style
//        textView.setTextAppearance(tabLayout.context, tabLayout.mTabTextAppearance)
        textView.setTextColor(tabLayout.mTabTextColors)
        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, tabLayout.mTabTextSize)
    }

    protected abstract fun inflateCustomView(tabLayout: ViewPageTabLayout): View

    open fun getCustomView(tabLayout: ViewPageTabLayout): View {
        if (customView == null) {
            customView = inflateCustomView(tabLayout)
        }
        return customView!!
    }

    protected abstract fun onAnimatorUpdate(tabLayout: ViewPageTabLayout?,
                                            customView: View, percent: Float)

    open fun onAnimatorUpdate(tabLayout: ViewPageTabLayout?, position: Int, percent: Float) {
        if (customView == null) return
        onAnimatorUpdate(tabLayout, customView!!, percent)
    }
}