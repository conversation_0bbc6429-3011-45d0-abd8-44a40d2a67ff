package com.google.android.material.tabs

import android.content.Context
import android.os.Build
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup

/**
 *<AUTHOR>
 *@date 2020/9/21
 *@des
 **/
class ScaleLayout constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ViewGroup(context, attrs, defStyleAttr) {

    private var childScaleX = 1f
    private var childScaleY = 1f

    constructor(view: View) : this(view.context) {
        addView(view)
    }

    fun setChildScale(scaleX: Float, scaleY: Float) {
        childScaleX = scaleX
        childScaleY = scaleY
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            if (!isInLayout) {
                requestLayout()
            }
        } else {
            requestLayout()
        }
    }

    private fun truncateScale(measure: Int, scale: Float): Float {
        if (measure == 0) return scale
        val after = (measure * scale + 0.5f).toInt()
        return after * 1f / measure
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        var measuredWidth = 0
        var measuredHeight = 0
        val count = childCount
        for (childIndex in 0 until count) {
            val child = getChildAt(childIndex)
            measureChild(child, widthMeasureSpec, heightMeasureSpec)
            measuredWidth = Math.max(measuredWidth, (child.measuredWidth *
                    truncateScale(child.measuredWidth, childScaleX)).toInt())
            measuredHeight = Math.max(measuredHeight, (child.measuredHeight *
                    truncateScale(child.measuredHeight, childScaleY)).toInt())
        }
        setMeasuredDimension(measuredWidth, measuredHeight)
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        val count = childCount
        for (childIndex in 0 until count) {
            val child = getChildAt(childIndex)
            val scaleX = truncateScale(child.measuredWidth, childScaleX)
            val scaleY = truncateScale(child.measuredHeight, childScaleY)
            child.pivotX = 0f
            child.pivotY = 0f
            child.scaleX = scaleX
            child.scaleY = scaleY
            val childWidth = (child.measuredWidth * scaleX).toInt()
            val childHeight = (child.measuredHeight * scaleY).toInt()
            child.layout(0, 0, childWidth, childHeight)
        }
    }
}