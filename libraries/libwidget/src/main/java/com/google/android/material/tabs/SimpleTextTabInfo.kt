package com.google.android.material.tabs

import android.view.View
import android.widget.TextView

/**
 *<AUTHOR>
 *@date 2020/9/21
 *@des
 **/
open class SimpleTextTabInfo(title: CharSequence? = null) : TabInfo() {

    var titleView: TextView? = null

    var scaleLayout: ScaleLayout? = null

    var title: CharSequence? = title
        set(value) {
            field = value
            titleView?.text = value
        }

    override fun onAnimatorUpdate(
        tabLayout: ViewPageTabLayout?,
        customView: View, percent: Float
    ) {
        if (tabLayout?.isEnableScale() != true) {
            return
        }
        scaleLayout?.setChildScale(
            1 + MAX_SCALE_OFFSET * percent,
            1 + MAX_SCALE_OFFSET * percent
        )
    }

    override fun inflateCustomView(tabLayout: ViewPageTabLayout): View {
        val textView = TextView(tabLayout.context)
        titleView = textView
        inheritTabLayoutStyle(textView, tabLayout)
        textView.text = title
        return ScaleLayout(textView).also {
            scaleLayout = it
        }
    }
}