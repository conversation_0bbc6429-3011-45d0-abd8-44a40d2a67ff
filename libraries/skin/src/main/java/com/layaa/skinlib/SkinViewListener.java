package com.layaa.skinlib;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;

/**
 * <AUTHOR>
 * @date 2021/3/2
 * @des
 **/
public interface SkinViewListener{

    /**
     * 根据name 获取包装后的view
     * @param name view name 例如 androidx.constraintlayout.widget.ConstraintLayout
     * @return 包装后的view
     */
    View getView(String name, Context context, AttributeSet attrs);
}
