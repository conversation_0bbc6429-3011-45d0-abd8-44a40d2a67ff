package com.layaa.skinlib;

import android.app.Application;
import android.content.Context;
import android.util.AttributeSet;
import android.view.View;

import com.layaa.skinlib.observer.SkinObserver;
import com.layaa.skinlib.observer.SkinSubject;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.MainThread;

/**
 * <AUTHOR>
 * @date 2021/3/2
 * @des
 **/
public class SkinKit {

    private static SkinKit skinKit;
    private List<SkinViewListener> listeners;
    private LanguageListener languageListener;
    private SkinSubject skinSubject;
    private boolean skinAllActivity = false;

    private SkinConfig mConfig;

    public SkinKit() {
        skinSubject = new SkinSubject();
    }

    public static SkinKit getInstance() {
        if (skinKit == null) {
            synchronized (SkinKit.class) {
                if (skinKit == null) {
                    skinKit = new SkinKit();
                }
            }
        }
        return skinKit;
    }

    public void setConfig(SkinConfig config) {
        mConfig = config;
    }

    public SkinConfig.Builder getConfigBuilder() {
        if (mConfig == null) {
            return new SkinConfig.Builder();
        }
        return new SkinConfig.Builder(mConfig);
    }

    public boolean isRtl() {
        return mConfig != null && mConfig.isRtl();
    }

    public View getView(String name, Context context, AttributeSet attrs) {
        if (listeners == null) {
            return null;
        }
        for (SkinViewListener listener : listeners) {
            View view = listener.getView(name, context, attrs);
            if (view != null) {
                return view;
            }
        }
        return null;
    }

    @MainThread
    public void addListener(SkinViewListener listener) {
        if (listeners == null) {
            listeners = new ArrayList<>();
        }
        listeners.add(listener);
    }

    @MainThread
    public void removeListener(SkinViewListener listener) {
        if (listeners != null) {
            listeners.remove(listener);
        }
    }

    public String getString(Context context, String resName, int resId) {
        if (languageListener != null) {
            return languageListener.getText(resName, resId);
        }
        return context.getResources().getString(resId);
    }

    public void setLanguageListener(LanguageListener languageListener) {
        this.languageListener = languageListener;
    }

    public void addObserver(SkinObserver observer) {
        skinSubject.addObserver(observer);
    }

    public void notifiChange() {
        skinSubject.notifiChange();
    }

    public void removeObserver(SkinObserver observer) {
        skinSubject.removeObserver(observer);
    }

    public void clear() {
        skinSubject.clear();
    }

    public void register(Application application) {
        SkinActivityLifecycle.create(application);
    }

    public boolean isSkinAllActivity() {
        return skinAllActivity;
    }
}
