package com.layaa.skinlib;

import androidx.annotation.NonNull;

/**
 * <AUTHOR>
 * @date 2021/3/2
 * @des
 **/
public class SkinConfig {

    private SkinConfig(Builder builder) {
        rtl = builder.rtl;
        lang = builder.lang;
    }

    private boolean rtl;
    private String lang;

    public boolean isRtl() {
        return rtl;
    }

    public static class Builder {

        private boolean rtl;
        private String lang;

        public Builder() {

        }

        public Builder(SkinConfig config) {
            rtl = config.rtl;
            lang = config.lang;
        }

        public Builder setRtl(boolean rtl) {
            this.rtl = rtl;
            return this;
        }

        public Builder setLang(String lang) {
            this.lang = lang;
            return this;
        }

        public SkinConfig build() {
            return new SkinConfig(this);
        }

        public Builder newBuilder(@NonNull SkinConfig config) {
            return new Builder(config);
        }
    }
}
