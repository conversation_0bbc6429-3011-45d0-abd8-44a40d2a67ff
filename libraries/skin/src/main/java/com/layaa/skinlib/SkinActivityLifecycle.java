package com.layaa.skinlib;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;

import com.layaa.skinlib.observer.SkinObserver;
import com.layaa.libutils.module_log.LogUtils;

import java.lang.ref.WeakReference;
import java.util.WeakHashMap;

import androidx.core.view.LayoutInflaterCompat;

/**
 * <AUTHOR>
 * @date 2020-01-06
 * @des
 **/
final class SkinActivityLifecycle implements Application.ActivityLifecycleCallbacks {

    private WeakHashMap<Context, SkinCompatDelegate> mSkinDelegateMap;
    private WeakHashMap<Context, LazySkinObserver> mSkinObserverMap;
    /**
     * 用于记录当前Activity，在换肤后，立即刷新当前Activity
     */
    private WeakReference<Activity> mCurActivityRef;


    public static void create(Application application) {
        new SkinActivityLifecycle(application);
    }

    private SkinActivityLifecycle(Application application) {
        application.registerActivityLifecycleCallbacks(this);
    }

    @Override
    public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
        if (isContextSkinEnable(activity)) {
            installLayoutFactory(activity);
            LazySkinObserver observer = getObserver(activity);
            SkinKit.getInstance().addObserver(observer);
        }
    }

    @Override
    public void onActivityStarted(Activity activity) {

    }

    @Override
    public void onActivityResumed(Activity activity) {
        mCurActivityRef = new WeakReference<>(activity);
        if (isContextSkinEnable(activity)) {
            LazySkinObserver observer = getObserver(activity);
            if (observer != null) {
                observer.updateSkinIfNeeded();
            }
        }
    }

    @Override
    public void onActivityPaused(Activity activity) {
    }

    @Override
    public void onActivityStopped(Activity activity) {

    }

    @Override
    public void onActivitySaveInstanceState(Activity activity, Bundle outState) {

    }

    @Override
    public void onActivityDestroyed(Activity activity) {
        if (isContextSkinEnable(activity)) {
            SkinKit.getInstance().removeObserver(getObserver(activity));
            mSkinObserverMap.remove(activity);
            mSkinDelegateMap.remove(activity);
        }
    }

    private void installLayoutFactory(Context context) {
        try {
            LayoutInflater layoutInflater = LayoutInflater.from(context);
            LayoutInflaterCompat.setFactory2(layoutInflater, getSkinDelegate(context));
            LogUtils.i("setFactory2", "installLayoutFactory");
        } catch (Throwable e) {
            LogUtils.i("SkinActivity", "A factory has already been set on this LayoutInflater");
        }
    }

    private SkinCompatDelegate getSkinDelegate(Context context) {
        if (mSkinDelegateMap == null) {
            mSkinDelegateMap = new WeakHashMap<>();
        }

        SkinCompatDelegate mSkinDelegate = mSkinDelegateMap.get(context);
        if (mSkinDelegate == null) {
            mSkinDelegate = SkinCompatDelegate.create(context, null);
            mSkinDelegateMap.put(context, mSkinDelegate);
        }
        return mSkinDelegate;
    }

    private LazySkinObserver getObserver(final Context context) {
        if (mSkinObserverMap == null) {
            mSkinObserverMap = new WeakHashMap<>();
        }
        LazySkinObserver observer = mSkinObserverMap.get(context);
        if (observer == null) {
            observer = new LazySkinObserver(context);
            mSkinObserverMap.put(context, observer);
        }
        return observer;
    }

    private boolean isContextSkinEnable(Context context) {
        return SkinKit.getInstance().isSkinAllActivity()
                || context.getClass().getAnnotation(Skinable.class) != null
                || context instanceof SkinCompatSupportable;
    }

    private class LazySkinObserver implements SkinObserver {
        private final WeakReference<Context> mContext;
        private boolean mMarkNeedUpdate = false;

        LazySkinObserver(Context context) {
            mContext = new WeakReference<>(context);
        }

        @Override
        public void updateSkin() {
            // 当前Activity，或者非Activity，立即刷新，否则延迟到下次onResume方法中刷新。
            if (mCurActivityRef == null
                    || mContext.get() == mCurActivityRef.get()
                    || !(mContext.get() instanceof Activity)) {
                updateSkinForce();
            } else {
                mMarkNeedUpdate = true;
            }
        }

        void updateSkinIfNeeded() {
            if (mMarkNeedUpdate) {
                updateSkinForce();
            }
        }

        void updateSkinForce() {
            Context context = mContext.get();
            if (context == null) {
                return;
            }
            getSkinDelegate(context).applySkin();
            if (context instanceof SkinCompatSupportable) {
                ((SkinCompatSupportable) context).applySkin();
            }
            mMarkNeedUpdate = false;
        }
    }

}
