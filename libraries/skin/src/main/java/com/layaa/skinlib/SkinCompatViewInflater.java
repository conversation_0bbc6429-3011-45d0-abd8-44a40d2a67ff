package com.layaa.skinlib;

import android.content.Context;
import android.content.ContextWrapper;
import android.content.res.TypedArray;
import android.os.Build;
import android.util.AttributeSet;
import android.view.InflateException;
import android.view.View;

import com.layaa.skinlib.widget.SkinCompatButtonView;
import com.layaa.skinlib.widget.SkinCompatEditTextView;
import com.layaa.skinlib.widget.SkinCompatFrameLayout;
import com.layaa.skinlib.widget.SkinCompatImageView;
import com.layaa.skinlib.widget.SkinCompatLinearLayout;
import com.layaa.skinlib.widget.SkinCompatRecyclerView;
import com.layaa.skinlib.widget.SkinCompatTextView;
import com.layaa.skinlib.widget.SkinConstraintLayout;
import com.layaa.libutils.ClickValidUtil;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Map;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.core.view.ViewCompat;

/**
 * <AUTHOR>
 * @date 2020-01-07
 * @des
 **/
class SkinCompatViewInflater {
    private static final Class<?>[] sConstructorSignature = new Class[]{
            Context.class, AttributeSet.class};
    private static final int[] sOnClickAttrs = new int[]{android.R.attr.onClick};

    private static final String[] sClassPrefixList = {
            "android.widget.",
            "android.view.",
            "android.webkit."
    };

    private static final Map<String, Constructor<? extends View>> sConstructorMap
            = new ArrayMap<>();

    private final Object[] mConstructorArgs = new Object[2];

    public final View createView(View parent, final String name, @NonNull Context context, @NonNull AttributeSet attrs) {
        View view = createViewFromInflater(context, name, attrs);

        if (view == null) {
            view = createViewFromTag(context, name, attrs);
        }

        if (view != null) {
            // If we have created a view, check it's android:onClick
            checkOnClickListener(view, attrs);
        }

        return view;
    }


    private View createViewFromInflater(Context context, String name, AttributeSet attrs) {
        View view = SkinKit.getInstance().getView(name,context,attrs);
        if (view != null) {
            return view;
        }
        switch (name) {
            case "TextView":
                view = createTextView(context, attrs);
                verifyNotNull(view, name);
                break;
            case "ImageView":
                view = createImageView(context, attrs);
                verifyNotNull(view, name);
                break;
            case "Button":
                view = createButton(context, attrs);
                verifyNotNull(view, name);
                break;
            case "EditText":
                view = createEditText(context, attrs);
                verifyNotNull(view, name);
                break;
            case "LinearLayout":
                view = createLinearLayout(context, attrs);
                verifyNotNull(view, name);
                break;
            case "FrameLayout":
                view = createFrameLayout(context, attrs);
                verifyNotNull(view, name);
                break;
            case "androidx.constraintlayout.widget.ConstraintLayout":
                view = createConstraintLayout(context, attrs);
                verifyNotNull(view, name);
                break;
            case "androidx.recyclerview.widget.RecyclerView":
                view = createRecyclerView(context, attrs);
                verifyNotNull(view, name);
                break;
            default:
                break;
        }
        return view;
    }

    private void verifyNotNull(View view, String name) {
        if (view == null) {
            throw new IllegalStateException(this.getClass().getName()
                    + " asked to inflate view for <" + name + ">, but returned null");
        }
    }

    public View createViewFromTag(Context context, String name, AttributeSet attrs) {
        if ("view".equals(name)) {
            name = attrs.getAttributeValue(null, "class");
        }

        try {
            mConstructorArgs[0] = context;
            mConstructorArgs[1] = attrs;

            if (-1 == name.indexOf('.')) {
                for (int i = 0; i < sClassPrefixList.length; i++) {
                    final View view = createView(context, name, sClassPrefixList[i]);
                    if (view != null) {
                        return view;
                    }
                }
                return null;
            } else {
                return createView(context, name, null);
            }
        } catch (Exception e) {
            // We do not want to catch these, lets return null and let the actual LayoutInflater
            // try
            return null;
        } finally {
            // Don't retain references on context.
            mConstructorArgs[0] = null;
            mConstructorArgs[1] = null;
        }
    }

    private SkinCompatTextView createTextView(Context context, AttributeSet attrs) {
        return new SkinCompatTextView(context, attrs);
    }

    private SkinCompatImageView createImageView(Context context, AttributeSet attrs) {
        return new SkinCompatImageView(context, attrs);
    }

    private SkinCompatButtonView createButton(Context context, AttributeSet attrs) {
        return new SkinCompatButtonView(context, attrs);
    }

    private SkinCompatEditTextView createEditText(Context context, AttributeSet attrs) {
        return new SkinCompatEditTextView(context, attrs);
    }

    private SkinCompatRecyclerView createRecyclerView(Context context, AttributeSet attrs) {
        return new SkinCompatRecyclerView(context, attrs);
    }

    private SkinCompatLinearLayout createLinearLayout(Context context, AttributeSet attrs) {
        return new SkinCompatLinearLayout(context, attrs);
    }

    private SkinCompatFrameLayout createFrameLayout(Context context, AttributeSet attrs) {
        return new SkinCompatFrameLayout(context, attrs);
    }

    private SkinConstraintLayout createConstraintLayout(Context context, AttributeSet attrs) {
        return new SkinConstraintLayout(context, attrs);
    }

    /**
     * android:onClick doesn't handle views with a ContextWrapper context. This method
     * backports new framework functionality to traverse the Context wrappers to find a
     * suitable target.
     */
    private void checkOnClickListener(View view, AttributeSet attrs) {
        final Context context = view.getContext();

        if (!(context instanceof ContextWrapper) ||
                (Build.VERSION.SDK_INT >= 15 && !ViewCompat.hasOnClickListeners(view))) {
            // Skip our compat functionality if: the Context isn't a ContextWrapper, or
            // the view doesn't have an OnClickListener (we can only rely on this on API 15+ so
            // always use our compat code on older devices)
            return;
        }

        final TypedArray a = context.obtainStyledAttributes(attrs, sOnClickAttrs);
        final String handlerName = a.getString(0);
        if (handlerName != null) {
            view.setOnClickListener(new DeclaredOnClickListener(view, handlerName));
        }
        a.recycle();
    }

    private View createView(Context context, String name, String prefix)
            throws ClassNotFoundException, InflateException {
        Constructor<? extends View> constructor = sConstructorMap.get(name);

        try {
            if (constructor == null) {
                // Class not found in the cache, see if it's real, and try to add it
                Class<? extends View> clazz = context.getClassLoader().loadClass(
                        prefix != null ? (prefix + name) : name).asSubclass(View.class);

                constructor = clazz.getConstructor(sConstructorSignature);
                sConstructorMap.put(name, constructor);
            }
            constructor.setAccessible(true);
            return constructor.newInstance(mConstructorArgs);
        } catch (Exception e) {
            // We do not want to catch these, lets return null and let the actual LayoutInflater
            // try
            return null;
        }
    }

    /**
     * An implementation of OnClickListener that attempts to lazily load a
     * named click handling method from a parent or ancestor context.
     */
    private static class DeclaredOnClickListener implements View.OnClickListener {
        private final View mHostView;
        private final String mMethodName;

        private Method mResolvedMethod;
        private Context mResolvedContext;

        public DeclaredOnClickListener(@NonNull View hostView, @NonNull String methodName) {
            mHostView = hostView;
            mMethodName = methodName;
        }

        @Override
        public void onClick(@NonNull View v) {
            if (!ClickValidUtil.clickValidNormal()) {
                return;
            }
            if (mResolvedMethod == null) {
                resolveMethod(mHostView.getContext(), mMethodName);
            }

            try {
                mResolvedMethod.invoke(mResolvedContext, v);
            } catch (IllegalAccessException e) {
                throw new IllegalStateException(
                        "Could not execute non-public method for android:onClick", e);
            } catch (InvocationTargetException e) {
                throw new IllegalStateException(
                        "Could not execute method for android:onClick", e);
            }
        }

        @NonNull
        private void resolveMethod(@Nullable Context context, @NonNull String name) {
            while (context != null) {
                try {
                    if (!context.isRestricted()) {
                        final Method method = context.getClass().getMethod(mMethodName, View.class);
                        if (method != null) {
                            mResolvedMethod = method;
                            mResolvedContext = context;
                            return;
                        }
                    }
                } catch (NoSuchMethodException e) {
                    // Failed to find method, keep searching up the hierarchy.
                }

                if (context instanceof ContextWrapper) {
                    context = ((ContextWrapper) context).getBaseContext();
                } else {
                    // Can't search up the hierarchy, null out and fail.
                    context = null;
                }
            }

            final int id = mHostView.getId();
            final String idText = id == View.NO_ID ? "" : " with id '"
                    + mHostView.getContext().getResources().getResourceEntryName(id) + "'";
            throw new IllegalStateException("Could not find method " + mMethodName
                    + "(View) in a parent or ancestor Context for android:onClick "
                    + "attribute defined on view " + mHostView.getClass() + idText);
        }
    }
}
