package com.layaa.skinlib;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.Window;

import com.layaa.skinlib.SkinCompatSupportable;
import com.layaa.skinlib.SkinCompatViewInflater;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatCallback;

/**
 * <AUTHOR>
 * @date 2020-01-07
 * @des
 **/
class SkinCompatDelegateImpl extends SkinCompatDelegate {

    final Context mContext;
    final Window mWindow;
    private SkinCompatViewInflater skinCompatViewInflater;
    private List<WeakReference<SkinCompatSupportable>> mSkinHelpers = new ArrayList<>();

    SkinCompatDelegateImpl(Context context, Window window, AppCompatCallback callback) {
        mContext = context;
        mWindow = window;
    }

    @Override
    public View createView(@Nullable View parent, String name, @NonNull Context context, @NonNull AttributeSet attrs) {
        if (skinCompatViewInflater == null) {
            skinCompatViewInflater = new SkinCompatViewInflater();
        }
        View view = skinCompatViewInflater.createView(parent, name, context, attrs);
        if (view instanceof SkinCompatSupportable) {
            mSkinHelpers.add(new WeakReference<>((SkinCompatSupportable) view));
        }
        return view;
    }

    @Override
    public void applySkin() {
        if (mSkinHelpers != null && !mSkinHelpers.isEmpty()) {
            for (WeakReference ref : mSkinHelpers) {
                if (ref != null && ref.get() != null) {
                    ((SkinCompatSupportable) ref.get()).applySkin();
                }
            }
        }
    }

    @Nullable
    @Override
    public View onCreateView(@Nullable View parent, @NonNull String name, @NonNull Context context, @NonNull AttributeSet attrs) {
        return createView(parent, name, context, attrs);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull String name, @NonNull Context context, @NonNull AttributeSet attrs) {
        return createView(null, name, context, attrs);
    }
}
