package com.layaa.skinlib;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;

import com.layaa.skinlib.SkinCompatDelegateImpl;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatCallback;

/**
 * <AUTHOR>
 * @date 2020-01-06
 * @des
 **/
public abstract class SkinCompatDelegate implements LayoutInflater.Factory2 {

    public SkinCompatDelegate() {

    }

    public static SkinCompatDelegate create(Activity activity, AppCompatCallback callback) {
        return new SkinCompatDelegateImpl(activity, activity.getWindow(), callback);
    }


    public static SkinCompatDelegate create(Dialog dialog, AppCompatCallback callback) {
        return new SkinCompatDelegateImpl(dialog.getContext(), dialog.getWindow(), callback);
    }


    public static SkinCompatDelegate create(Context context, Window window,
                                            AppCompatCallback callback) {
        return new SkinCompatDelegateImpl(context, window, callback);
    }

    public static SkinCompatDelegate create(Context context,
                                            AppCompatCallback callback) {
        return new SkinCompatDelegateImpl(context, null, callback);
    }

    public abstract View createView(@Nullable View parent, String name, @NonNull Context context,
                                    @NonNull AttributeSet attrs);

    public abstract void applySkin();
}
