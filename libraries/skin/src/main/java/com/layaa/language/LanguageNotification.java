package com.layaa.language;

import java.util.Map;
import java.util.WeakHashMap;

/**
 * <AUTHOR>
 * @date 2021/3/25
 * @des
 **/
public class LanguageNotification {

    public static int MSG_CHANGE_LANGUAGE = 1;
    private static WeakHashMap<String, LanguageObserve> map = new WeakHashMap<>();

    public static synchronized void addObserve(String tag, LanguageObserve observe) {
        map.put(tag, observe);
    }

    public synchronized static void removeObserve(String tag) {
        map.remove(tag);
    }

    public synchronized static void notify(int id, Object... objects) {
        for (Map.Entry<String, LanguageObserve> stringLanguageObserveEntry : map.entrySet()) {
            if (stringLanguageObserveEntry.getValue() != null) {
                stringLanguageObserveEntry.getValue().languageObserver(id, objects);
            }
        }
    }
}
