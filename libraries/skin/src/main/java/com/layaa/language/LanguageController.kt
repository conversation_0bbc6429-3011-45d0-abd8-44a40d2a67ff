package com.layaa.language

import android.app.Activity
import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import android.os.Build
import android.text.TextUtils
import android.view.View
import androidx.annotation.StringRes
import com.layaa.libutils.UtilKit
import com.layaa.libutils.kv.KVDelegate
import java.util.Locale

/**
 * Created by <PERSON> on 25/02/21_Fri
 */
object LanguageController {

    private const val LANG_DEFAULT = "default"

    private const val KV_CURRENT_LANGUAGE = "current_language"

    @JvmField
    var fallbackLanguage = "en"

    @JvmField
    val supportedLanguages = linkedMapOf<String, String>()

    @JvmStatic
    fun saveLanguage(language: String) {
        KVDelegate.getInstance().save(KV_CURRENT_LANGUAGE, language)
    }

    @JvmStatic
    fun getLanguage(): String {
        var language = KVDelegate.getInstance().getString(KV_CURRENT_LANGUAGE, LANG_DEFAULT)
        if (language == LANG_DEFAULT) {
            language = getLocale(language).language
            saveLanguage(language)
        }
        return language
    }

    @JvmStatic
    fun getLanguageName(): String {
        return supportedLanguages[getLanguage()] ?: ""
    }

    /**
     * 根据语言代码创建 Locale 对象.
     * 如果 language 为 LANG_DEFAULT，则返回系统默认 Locale 或者 fallbackLanguage 的 Locale.
     */
    private fun getLocale(language: String): Locale {
        return if (language == LANG_DEFAULT) {
            val systemLocale = getSystemLocale()
            val systemLanguageCode = systemLocale.language
            return if (supportedLanguages.contains(systemLanguageCode)) {
                systemLocale
            } else {
                Locale(fallbackLanguage)
            }
        } else {
            Locale.forLanguageTag(language) // 使用 Language Tag 创建 Locale
        }
    }

    private fun getSystemLocale(): Locale = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
        Resources.getSystem().configuration.locales[0] // 获取系统首选 Locale
    } else {
        Resources.getSystem().configuration.locale // 兼容旧版本
    }

    /**
     * 设置 App 语言并保存.
     * @param context Context
     * @param language  语言代码，例如 "en", "zh-CN", 或者 "system_default".
     * @param refreshImmediately  context 为 Activity 的时候，是否立即重启当前 Activity
     */
    @JvmStatic
    @JvmOverloads
    fun changeLanguage(context: Context, language: String, refreshImmediately: Boolean = true) {
        saveLanguage(language)
        val locale = getLocale(language)
        updateLocale(context, locale, refreshImmediately)
        LanguageNotification.notify(LanguageNotification.MSG_CHANGE_LANGUAGE)
    }

    /**
     * 在 Application 初始化时或者在 BaseActivity 的 attachBaseContext 中调用，
     * 应用用户上次选择的语言设置.
     * @param context Context
     * @return  应用语言设置后的 ContextWrapper
     */
    @JvmStatic
    fun attachBaseContext(context: Context): Context {
        val language = getLanguage()
        val locale = getLocale(language)
        return updateResources(context, locale)
    }

    private fun updateResources(context: Context, locale: Locale): Context {
        val configuration = Configuration(context.resources.configuration)
        configuration.setLocale(locale)
        return context.createConfigurationContext(configuration) // 创建新的 Context
    }

    /**
     * 更新 App 的语言配置.
     * @param context  Context
     * @param newLocale  新的 Locale 对象
     * @param refreshImmediately  是否立即刷新当前 Activity (如果适用)
     */
    private fun updateLocale(
        context: Context,
        newLocale: Locale,
        refreshImmediately: Boolean = true
    ) {
        Locale.setDefault(newLocale)

        updateResources(context, newLocale)

        // 对于 Activity 类型的 Context，建议 recreate() 以确保 UI 立即更新
        if (context is Activity && refreshImmediately) {
            context.recreate() // 重建 Activity 以应用新的配置
        }
    }

    @JvmStatic
    fun isRtl(): Boolean {
        val locale = Locale(getLanguage())
        val layoutDirection = TextUtils.getLayoutDirectionFromLocale(locale)
        return layoutDirection == View.LAYOUT_DIRECTION_RTL
    }

    @JvmStatic
    @Deprecated("Use self instance", ReplaceWith("LanguageController"))
    fun getInstance() = LanguageController

    @JvmStatic
    @Deprecated("Use Context.getString", ReplaceWith("context.getString(resId)"))
    fun getString(resId: Int) = UtilKit.context.getString(resId)

    @JvmStatic
    @Deprecated("Use Context.getString", ReplaceWith("context.getString(resId)"))
    fun getString(name: String, resId: Int) = getString(resId)

    @JvmStatic
    @Deprecated("Use Context.getString", ReplaceWith("context.getString(resId, args)"))
    fun getString(@StringRes resId: Int, vararg args: Any?) = String.format(getString(resId), *args)
}