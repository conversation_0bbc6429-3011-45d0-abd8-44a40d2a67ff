package com.layaa.olweb.listener;

import android.graphics.Bitmap;
import android.net.http.SslError;
import android.webkit.ConsoleMessage;
import android.webkit.JsPromptResult;
import android.webkit.JsResult;
import android.webkit.SslErrorHandler;
import android.webkit.WebResourceResponse;
import android.webkit.WebView;

/**
 * <AUTHOR>
 * @date 2020-01-10
 * @des * OLWebView页面加载的监听器，
 * * 1.监听页面开始加载，页面加载完毕，以及页面加载失败
 * * 2.页面获得标题，获得图标，页面加载进度发生变化
 * * 3.Js confirm，alert，prompt事件接收
 **/
public abstract class OLWebLoadListener {

    private OLPageLifeCycleCallback olHelper;

    public OLWebLoadListener(OLPageLifeCycleCallback olHelper) {
        this.olHelper = olHelper;
    }

    public WebResourceResponse shouldInterceptRequest(WebView view, String url) {
        return null;
    }

    public void onPageStarted(WebView webview, String url, Bitmap favicon) {
        if (olHelper != null) {
            olHelper.onPageStarted(webview, url, favicon);
        }
    }

    public void onPageFinished(WebView webview, String url) {
    }

    public void onPageError(WebView view, int errorCode, String description, String failingUrl) {
    }

    public void onReceivedSslError(WebView view, final SslErrorHandler handler, SslError error) {

    }

    public void onReceivedIcon(WebView view, Bitmap icon) {
    }

    public void onReceivedTitle(WebView view, String title) {
    }

    public void onProgressChanged(WebView view, int newProgress) {
    }

    public boolean onJsPrompt(WebView view, String url, String message, String defaultValue, JsPromptResult result) {
        return false;
    }

    public boolean onJsConfirm(WebView view, String url, String message, JsResult result) {
        return false;
    }

    public boolean onJsAlert(WebView view, String url, String message, JsResult result) {
        return false;
    }

    public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
        return false;
    }
}
