package com.layaa.olweb.jsbridge;

import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Configuration;
import android.net.Uri;
import android.text.TextUtils;

import com.layaa.libutils.ClickValidUtil;
import com.layaa.libutils.NetUtils;
import com.layaa.libutils.PackageUtils;
import com.layaa.libutils.StatusBarUtil;
import com.layaa.libutils.UIUtils;
import com.layaa.libutils.module_log.LogUtils;
import com.layaa.olweb.OLKit;
import com.layaa.olweb.annotation.OLJsBridge;
import com.layaa.olweb.annotation.OLSubscribe;
import com.layaa.olweb.annotation.ThreadMode;
import com.layaa.olweb.config.OLConfig;
import com.layaa.olweb.constant.OLConstants;
import com.layaa.olweb.utils.OLHelper;
import com.layaa.olweb.widget.OLWebView;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2021/3/10
 * @des
 **/
@OLSubscribe(nameSpace = "device")
public class DeviceBridge {

    private OLWebView OLWebView;

    public DeviceBridge(OLWebView pOLWebview) {
        this.OLWebView = pOLWebview;
    }

    public void callPhone(JSONObject params) {
        try {
            final String phoneNumber = params.optString("phoneNumber");
            int confirm = params.optInt("confirm");
            if (0 == confirm) {
                Uri uri = Uri.parse("tel:" + phoneNumber);
                Intent it = new Intent(Intent.ACTION_DIAL, uri);
                it.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                OLKit.getContext().startActivity(it);
            } else {
                //对alert的简单封装
                new AlertDialog.Builder(OLKit.getContext()).
                        setTitle("Alert").setMessage("是否拨打电话").setPositiveButton("OK",
                                new DialogInterface.OnClickListener() {
                                    @Override
                                    public void onClick(DialogInterface arg0, int arg1) {
                                        if (!ClickValidUtil.clickValidNormal()) {
                                            return;
                                        }
                                        Uri uri = Uri.parse("tel:" + phoneNumber);
                                        Intent it = new Intent(Intent.ACTION_DIAL, uri);
                                        it.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                        OLKit.getContext().startActivity(it);
                                    }
                                }).setNegativeButton("NO", new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialogInterface, int i) {
                            }
                        }).create().show();
            }
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }

    public void getNetworkType(JSONObject params) {
        String netCallback = params.optString(OLConstants.callBack);
        String net = NetUtils.getNetWorkClass();
        net = TextUtils.isEmpty(net) ? "none" : net;
        JSONObject networkData = OLHelper.assembleJsonObject(new String[]{"network_type"}, new String[]{net});
        OLHelper.insertCallback(OLWebView, netCallback, networkData.toString());
    }

    public void getScreenInfo(JSONObject params) {
        JSONObject screenData = getScreenInfo();
        OLHelper.insertCallback(OLWebView, params.optString(OLConstants.callBack), screenData == null ? "" : screenData.toString());
    }

    private JSONObject getScreenInfo() {
        try {
            JSONObject jsonResult = new JSONObject();
            int contentWidth;
            int contentHeight;
            if (OLWebView == null) {
                contentWidth = 0;
                contentHeight = 0;
            } else {
                contentWidth = OLWebView.getWidth();
                contentHeight = OLWebView.getHeight();
            }
            jsonResult.put("deviceWidth", UIUtils.getScreenWidth());
            jsonResult.put("deviceHeight", UIUtils.getScreenHeight());
            jsonResult.put("width", contentWidth);
            jsonResult.put("height", contentHeight);
            jsonResult.put("statusBarHeight", StatusBarUtil.getStatusBarHeight(OLKit.getContext()));
            jsonResult.put("navigationHeight", StatusBarUtil.getNavigationBarHeight(OLKit.getContext()));
            String oritaton;
            if (OLKit.getContext().getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
                oritaton = "portrait";
            } else {
                oritaton = "landscape";
            }
            jsonResult.put("orientation", oritaton);
            jsonResult.put("density", UIUtils.getScreenDensity());
            return jsonResult;
        } catch (Exception e) {
        }
        return null;
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void getClientInfo(JSONObject params) {
        String versionCode = PackageUtils.getVersionCode() + "";
        JSONObject verObj = new JSONObject();
        try {
            verObj.put("build_version", versionCode);
            OLHelper.insertCallback(OLWebView, params.optString(OLConstants.callBack), verObj.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void getUserInfo(JSONObject params) {
        OLConfig config = OLKit.getConfig();
        String userInfo = "";
        if (config != null && config.getUserInfo() != null) {
            userInfo = config.getUserInfo().invoke();
        }
        OLHelper.insertCallback(OLWebView, params.optString(OLConstants.callBack), userInfo);
    }

}
