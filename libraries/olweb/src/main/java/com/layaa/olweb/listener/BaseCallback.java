package com.layaa.olweb.listener;

import org.json.JSONObject;

import java.lang.ref.WeakReference;

/**
 * <AUTHOR>
 * @date 2021/3/10
 * @des 回调父类，用于实现弱引用得回调，防止内存泄漏
 **/
public abstract class BaseCallback<T> {
    WeakReference<T> ref;

    public T getRef() {
        return ref != null ? ref.get() : null;
    }

    public BaseCallback(T t) {
        this.ref = new WeakReference<>(t);
    }

    public abstract void onSuccess(JSONObject result);

    public abstract void onError(String msg);
}
