package com.layaa.olweb.statistics;

/**
 * <AUTHOR>
 * @date 2021/3/9
 * @des
 **/
public class WebMonitorInfo {
    private long onPageStarted;
    private String offlineVersion;

    public long getOnPageStarted() {
        return onPageStarted;
    }

    public void setOnPageStarted(long onPageStarted) {
        this.onPageStarted = onPageStarted;
    }

    public String getOfflineVersion() {
        return offlineVersion;
    }

    public void setOfflineVersion(String offlineVersion) {
        this.offlineVersion = offlineVersion;
    }
}
