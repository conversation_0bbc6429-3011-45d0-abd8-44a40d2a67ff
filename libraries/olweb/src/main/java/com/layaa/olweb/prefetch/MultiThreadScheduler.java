package com.layaa.olweb.prefetch;

import android.os.SystemClock;

import com.layaa.libutils.module_log.LogUtils;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2021/3/10
 * @des
 **/
class MultiThreadScheduler implements IScheduler {

    private static final String TAG = "SYNC-MultiThreadScheduler";
    private static final int MIN_POOL_SIZE = 5;
    private static final int MAX_POOL_SIZE = 10;
    private static final long DEFAULT_KEEP_LIVE_TIME = 20L;

    private ISyncObjectPool syncObjectPool;
    private IThread thread;

    public MultiThreadScheduler(ISyncObjectPool syncObjectPool) {
        if (syncObjectPool == null) {
            throw new NullPointerException("syncObjectPool must not be null!");
        }
        this.syncObjectPool = syncObjectPool;
    }

    /**
     * 将任务放入线程中调度
     *
     * @param key    同步对象的key,外部可通过{@link ISyncObjectPool#waitForKey(String)}等待任务执行完成
     * @param action 任务
     */
    @Override
    public void schedule(String key, Runnable action) {
        Action innerAction = new Action(key, action);
        LogUtils.d(TAG, "schedule action: %s", innerAction);
        syncObjectPool.putKey(key);
        if (thread == null) {
            thread = new ThreadPool();
            thread.start();
        }
        thread.post(innerAction);
    }

    /**
     * 停止调度线程,若有正在执行的任务,可能会打断
     */
    @Override
    public void stop() {
        if (thread != null) {
            thread.release();
        }
    }

    /**
     * 若有正在执行的任务,等待任务执行完成,再停止调度线程
     */
    @Override
    public void stopSafely() {
        if (thread != null) {
            thread.releaseSafely();
        }
    }

    /**
     * 若有正在执行的任务,等待任务执行完成,再停止调度线程
     * 清除剩余任务,notify所有等待obj
     */
    @Override
    public void release() {
        stopSafely();
        syncObjectPool.clear();
    }

    /**
     * 放入线程中执行的Runnable
     * 执行完任务后，将相应key删除并notify
     */
    private class Action implements Runnable {
        private Runnable innerRunnable;
        private String key;

        Action(String key, Runnable runnable) {
            this.key = key;
            this.innerRunnable = runnable;
        }

        @Override
        public void run() {
            long start = SystemClock.uptimeMillis();
            LogUtils.d(TAG, "thread: %s---run action, : %s", Thread.currentThread().getName(), innerRunnable);
            if (innerRunnable != null)
                innerRunnable.run();
            LogUtils.d(TAG, "thread: %s---action done! action: %s, cast: %d", Thread.currentThread().getName(), innerRunnable, (SystemClock.uptimeMillis() - start));
            syncObjectPool.removeKeyAndNotify(key);
        }

        @Override
        public String toString() {
            return "action key: " + key + " inner action: " + innerRunnable;
        }
    }

    /**
     * 使用线程池实现
     */
    private class ThreadPool implements IThread {
        private ThreadPoolExecutor threadPool;

        @Override
        public void start() {
            if (threadPool == null) {
                threadPool = new ThreadPoolExecutor(MIN_POOL_SIZE, MAX_POOL_SIZE, DEFAULT_KEEP_LIVE_TIME,
                        TimeUnit.SECONDS, new SynchronousQueue<Runnable>(),
                        new OLThreadFactory(), new RejectedHandler());
                threadPool.allowCoreThreadTimeOut(true);
            }
        }

        @Override
        public void post(Runnable action) {
            threadPool.execute(action);
        }

        @Override
        public void release() {
            threadPool.shutdownNow();
            threadPool = null;
        }

        @Override
        public void releaseSafely() {
            threadPool.shutdown();
            threadPool = null;
        }
    }

    private static class RejectedHandler implements RejectedExecutionHandler {
        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            LogUtils.e(TAG, "Task " + r.toString() + " rejected from " + executor.toString());
        }
    }
}

/**
 * 线程工厂，标记线程名称，并将线程优先级设置到最高
 */
class OLThreadFactory implements ThreadFactory {
    private static final String TAG = "OLThreadFactory";
    private static final AtomicInteger mCount = new AtomicInteger(1);

    @Override
    public Thread newThread(Runnable r) {
        String threadName = "OL-thread #" + mCount.getAndIncrement();
        LogUtils.i(TAG, "OLThreadFactory -> newThread : " + threadName);
        Thread thread = new Thread(r, threadName);
        thread.setPriority(Thread.MAX_PRIORITY);
        return thread;
    }
}
