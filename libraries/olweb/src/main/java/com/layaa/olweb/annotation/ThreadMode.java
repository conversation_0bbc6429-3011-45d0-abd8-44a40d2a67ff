package com.layaa.olweb.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import androidx.annotation.IntDef;

/**
 * <AUTHOR>
 * @date 2021/1/8
 * @des
 **/
@IntDef({ThreadMode.THREAD_CURRENT, ThreadMode.THREAD_MAIN, ThreadMode.THREAD_SYNC})
@Target(value = ElementType.METHOD)
@Retention(RetentionPolicy.SOURCE)
public @interface ThreadMode {
    int THREAD_CURRENT = 0;
    int THREAD_MAIN = 1;
    int THREAD_SYNC = 2;
}
