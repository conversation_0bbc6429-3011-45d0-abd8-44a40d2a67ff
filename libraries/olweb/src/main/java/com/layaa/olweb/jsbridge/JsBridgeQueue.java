package com.layaa.olweb.jsbridge;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.util.Log;

/**
 * <AUTHOR>
 * @date 2021/1/12
 * @des
 **/
public class JsBridgeQueue {

    private String TAG = "JsBridgeQueue";

    private volatile MsgHandlerThread mHandlerThread;
    private volatile Handler mHandler;
    private String mName;

    public JsBridgeQueue(String name) {
        TAG += "_" + name;
        mName = name;
    }

    public MsgHandlerThread getHandlerThread() {
        return mHandlerThread;
    }

    public Handler getHandler() {
        return mHandler;
    }

    public void Run() {
        Quit();
        synchronized (TAG) {
            mHandlerThread = new MsgHandlerThread(mName);
            mHandlerThread.start();
            mHandler = new Handler(mHandlerThread.getLooper(), mHandlerThread);
        }
    }

    public void Quit() {
        synchronized (TAG) {
            if (mHandlerThread != null) {
                mHandlerThread.quit();
            }

            if (mHandler != null) {
                mHandler.removeCallbacks(mHandlerThread);
            }

            mHandlerThread = null;
            mHandler = null;
        }
    }

    public void sendMessage(int what, int arg1, int arg2) {
        synchronized (TAG) {
            if (mHandler != null) {
                mHandler.sendMessage(mHandler.obtainMessage(what, arg1, arg2));
            } else {
                Log.v(TAG, "mHandler == null");
            }
        }
    }

    public void sendMessage(int what, Object obj) {
        synchronized (TAG) {
            if (mHandler != null) {
                mHandler.sendMessage(mHandler.obtainMessage(what, obj));
            } else {
                Log.v(TAG, "mHandler == null");
            }
        }
    }

    public void sendMessage(int what, int arg1, int arg2, Object obj) {
        synchronized (TAG) {
            if (mHandler != null) {
                mHandler.sendMessage(mHandler.obtainMessage(what, arg1, arg2, obj));
            } else {
                Log.v(TAG, "mHandler == null");
            }
        }
    }

    public void sendMessageDelayed(int what, int arg1, int arg2, Object obj, long delayMillis) {
        synchronized (TAG) {
            if (mHandler != null) {
                mHandler.sendMessageDelayed(mHandler.obtainMessage(what, arg1, arg2, obj), delayMillis);
            } else {
                Log.v(TAG, "mHandler == null");
            }
        }
    }


    public void sendEmptyMessage(int what) {
        synchronized (TAG) {
            if (mHandler != null) {
                mHandler.sendEmptyMessage(what);
            } else {
                Log.v(TAG, "mHandler == null");
            }
        }
    }

    public void sendEmptyMessageDelayed(int what, long delayMillis) {
        synchronized (TAG) {
            if (mHandler != null) {
                mHandler.sendEmptyMessageDelayed(what, delayMillis);
            } else {
                Log.v(TAG, "mHandler == null");
            }
        }
    }

    public boolean sendEmptyMessageAtTime(int what, long uptimeMillis) {
        synchronized (TAG) {
            if (mHandler != null) {
                return mHandler.sendEmptyMessageAtTime(what, uptimeMillis);
            } else {
                Log.v(TAG, "mHandler == null");
            }
        }
        return false;
    }

    public void removeMessages(int what) {
        synchronized (TAG) {
            if (mHandler != null) {
                mHandler.removeMessages(what);
            } else {
                Log.v(TAG, "mHandler == null");
            }
        }
    }

    public void removeMessages(int what, Object object) {
        synchronized (TAG) {
            if (mHandler != null) {
                mHandler.removeMessages(what, object);
            } else {
                Log.v(TAG, "mHandler == null");
            }
        }
    }

    public void post(Runnable runnable) {
        postDelayed(runnable, 0);
    }

    public void postDelayed(Runnable runnable, int delay) {
        synchronized (TAG) {
            if (mHandler != null) {
                mHandler.postDelayed(runnable, delay);
            } else {
                Log.v(TAG, "mHandler == null");
            }
        }
    }


    private class MsgHandlerThread extends HandlerThread implements Handler.Callback {

        public MsgHandlerThread(String name) {
            super(name);
        }

        public MsgHandlerThread(String name, int priority) {
            super(name, priority);
        }

        @Override
        public boolean handleMessage(Message msg) {
            if (mCallback != null) {
                return mCallback.handleMessage(msg);
            }
            return false;
        }
    }


    private Callback mCallback;

    public void setCallback(Callback callback) {
        this.mCallback = callback;
    }

    interface Callback {
        boolean handleMessage(Message msg);
    }
}
