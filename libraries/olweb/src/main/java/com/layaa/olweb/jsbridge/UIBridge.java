package com.layaa.olweb.jsbridge;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import android.view.inputmethod.InputMethodManager;

import com.layaa.gotorouter.GotoApp;
import com.layaa.libutils.StatusBarUtil;
import com.layaa.libutils.module_log.LogUtils;
import com.layaa.libutils.toast.ToastUtils;
import com.layaa.olweb.OLKit;
import com.layaa.olweb.annotation.OLJsBridge;
import com.layaa.olweb.annotation.OLSubscribe;
import com.layaa.olweb.annotation.ThreadMode;
import com.layaa.olweb.constant.OLBroadCastActions;
import com.layaa.olweb.constant.OLConstants;
import com.layaa.olweb.entity.OLMenu;
import com.layaa.olweb.entity.SetUIBtnParams;
import com.layaa.olweb.entity.SetUIParams;
import com.layaa.olweb.event.GlobalEventAdapter;
import com.layaa.olweb.listener.OLUICallback;
import com.layaa.olweb.utils.BroadcastHelper;
import com.layaa.olweb.utils.OLHelper;
import com.layaa.olweb.widget.OLWebView;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2021/3/18
 * @des
 **/
@OLSubscribe(nameSpace = "ui")
public class UIBridge {

    private OLUICallback uiCallback;
    private OLWebView OLWebView;

    public UIBridge(OLWebView OLWebView) {
        this.OLWebView = OLWebView;
    }

    public void setUiCallback(OLUICallback uiCallback) {
        this.uiCallback = uiCallback;
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void setUI(JSONObject params) {

        SetUIParams uiParams = new SetUIParams();
        if (params.has("nav")) {
            JSONObject navJson = params.optJSONObject("nav");
            uiParams.setModel(navJson.optInt("mode", -1));
            uiParams.setNavTitleColor(navJson.optString("color"));
            uiParams.setNavBackgroundColor(navJson.optString("background"));
        }
        if (params.has("uiBtn")) {
            JSONObject menuJson = params.optJSONObject("uiBtn");
            uiParams.setRightBtnTextColor(menuJson.optString("color"));
        }
        //backBtn 设置返回按钮的颜色 只有 android的支持
        if (params.has("backBtn")) {
            JSONObject backJson = params.optJSONObject("backBtn");
            uiParams.setBackBtnIconColor(backJson.optString("color"));
        }
        if (params.has("_ui_bg")) {
            uiParams.setNavBackgroundColor(params.optString("_ui_bg"));
        }
        if (params.has("_ui_mode")) {
            uiParams.setModel(params.optInt("_ui_mode"));
        }
        if (uiCallback != null) {
            uiCallback.uiSetUI(uiParams);
        }

    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void setUIBtn(JSONObject params) {
        SetUIBtnParams setUIBtn = new SetUIBtnParams();
        //当设置了 title 和callback后，等于设置按钮
        String uiBtnCallback = OLHelper.getJsCallback(params);
        if (params != null && params.has("title") && !TextUtils.isEmpty(uiBtnCallback)) {
            setUIBtn.setTitle(params.optString("title"), uiBtnCallback);
        } else {
            setUIBtn.setClear(true);
        }
        if (uiCallback != null) {
            uiCallback.uiSetUIButton(setUIBtn);
        }
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void setUIMenu(JSONObject params) {
        JSONArray menuArray = params.optJSONArray("buttons");
        if (menuArray == null || menuArray.length() == 0) {
            return;
        }
        SetUIBtnParams btnParams = new SetUIBtnParams();
        // 设置右上角按钮的标题
        btnParams.setTitle(params.optString("title", "..."), "");
        for (int i = 0; i < menuArray.length(); i++) {
            btnParams.addMenu(OLMenu.parseJson(menuArray.optJSONObject(i)));
        }
        if (uiCallback != null) {
            uiCallback.uiSetUIButton(btnParams);
        }
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void setBackBtn(JSONObject params) {
        if (OLWebView != null) {
            OLWebView.putCallbackString(OLHelper.getJsCallback(params));
        }
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void postMessage(JSONObject params) {
        String dst = params.optString("dst", null);
        if (!TextUtils.isEmpty(dst)) {
            postGlobalMessage(params);
            return;
        }
        Intent intent = new Intent();

        String name = params.optString("name");
        //type暂时没有用到
        intent.putExtra("type", "bridgeMessage");
        intent.putExtra("target", params.optString("target"));
        intent.putExtra("name", name);

        JSONObject dataJson = params.optJSONObject("data");
        if (dataJson != null) {
            intent.putExtra("data", dataJson.toString());
        }
        if (OLWebView != null) {
            intent.putExtra("origin", OLWebView.getOriginURL());
        }

        /**
         * 6.7 扩展 如果name中带有  bn:xxx 字段，则直接发对应的广播给客户端，不用统一发 {@link OLBroadCastActions.OL_ACTION_POSTMESSAGE}
         */
        boolean sendToNative = !TextUtils.isEmpty(name) && (name.indexOf("bn:") >= 0);
        if (sendToNative) {
            intent.setAction(name);//发送一条广播出去
        } else {
            intent.setAction(OLBroadCastActions.OL_ACTION_POSTMESSAGE);//发送一条广播出去
        }
        BroadcastHelper.sendBroadcast(OLKit.getContext(), intent);
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void postGlobalMessage(JSONObject params) {
        GlobalEventAdapter adapter = OLKit.getGlobalEventAdapter();
        if (adapter != null) {
            adapter.sendEvent(params.toString());
        }
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void showMessage(JSONObject params) {
        String msg = params.optString("message");
        if (!TextUtils.isEmpty(msg)) {
            ToastUtils.show(msg);
        }
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void openLinkInExternalBrowser(JSONObject params) {
        String url = params.optString("url");
        Intent it = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
        it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        OLKit.getContext().startActivity(it);
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void reload(JSONObject params) {
        if (OLWebView != null) {
            OLWebView.reload();
        }
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public String getVisibility(JSONObject params) {
        if (OLWebView == null) {
            return null;
        }
        int visible = OLWebView.getVisibility() == View.VISIBLE ? 1 : 0;
        JSONObject insertCallback = OLHelper.assembleJsonObject(new String[]{OLConstants.status, OLConstants.message}, new Object[]{visible + "", visible == 1 ? "前台" : "后台"});
        return insertCallback.toString();
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void showKeyboard(JSONObject params) {
        InputMethodManager imm = (InputMethodManager) OLKit.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null) {
            imm.toggleSoftInput(0, InputMethodManager.HIDE_NOT_ALWAYS);
        }
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void openUrl(JSONObject params) {

        if (OLWebView != null) {
            OLWebView.openUrl(params);
        }
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void close(JSONObject params) {

        if (OLWebView == null) {
            return;
        }
        Activity activity = OLWebView.getActivity();
        if (activity == null) {
            return;
        }

        int type = 1;
        if (params != null) {
            type = params.optInt("type", 1);
        }
        if (type == 2) {
            Intent intent = new Intent(OLBroadCastActions.OL_ACTION_CLOSE_ALL_OF_PAGE);
            intent.putExtra(OLBroadCastActions.KEY_CLOSE_SAVE_URL,
                    OLHelper.getStringArrayFromJsonArray(params.optJSONArray(OLBroadCastActions.KEY_CLOSE_SAVE_URL)));
            BroadcastHelper.sendBroadcast(activity, intent);
            activity.finish();
        } else if (type == 3) {
            Intent intent = new Intent(OLBroadCastActions.OL_ACTION_CLOSE_OTHER_PAGE);
            intent.putExtra(OLBroadCastActions.PARAM_WEBVIEW_ID, OLWebView.getWebViewId());
            BroadcastHelper.sendBroadcast(activity, intent);
        } else {
            activity.finish();
        }
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void goBack(JSONObject params) {
        if (OLWebView != null) {
            if (OLWebView.onBack()) {
                return;
            }
            if (OLWebView.canGoBack()) {
                OLWebView.goBack();
            }
        }
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void openGoto(JSONObject params) {
        String url = params.optString("param");
        GotoApp.navigate(url);
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void getStatusBarHeight(JSONObject params) {
        JSONObject verObj = new JSONObject();
        try {
            verObj.put("height", StatusBarUtil.getStatusBarHeight(OLKit.getContext()));
            OLHelper.insertCallback(OLWebView, params.optString(OLConstants.callBack), verObj.toString());
        } catch (JSONException e) {
            LogUtils.e(e);
        }
    }
    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void setUIHeight(JSONObject params) {
        int uiHeight = params.optInt("height");
        if (uiCallback != null) {
            uiCallback.setUiHeight(uiHeight);
        }
    }
}
