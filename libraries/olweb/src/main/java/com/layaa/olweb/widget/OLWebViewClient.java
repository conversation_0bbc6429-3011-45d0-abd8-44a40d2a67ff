package com.layaa.olweb.widget;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.Bitmap;
import android.net.Uri;
import android.net.http.SslError;
import android.os.Build;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.SslErrorHandler;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.layaa.olweb.OLKit;
import com.layaa.olweb.constant.OLConstants;
import com.layaa.olweb.entity.StatusParams;
import com.layaa.olweb.entity.UIParams;
import com.layaa.olweb.listener.OLPageLifeCycleCallback;
import com.layaa.olweb.listener.OLUICallback;
import com.layaa.olweb.listener.PreLoadingUrlListener;
import com.layaa.olweb.offline.OLPackageManager;
import com.layaa.olweb.router.OLPackageRouter;
import com.layaa.olweb.statistics.WebMonitorInfo;
import com.layaa.olweb.statistics.time.TimeMonitorConfig;
import com.layaa.olweb.statistics.time.TimeMonitorManager;
import com.layaa.olweb.utils.InterceptUtils;
import com.layaa.olweb.utils.OLHelper;
import com.layaa.olweb.utils.WebRefereeHandler;
import com.layaa.libutils.module_log.LogUtils;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.URLDecoder;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

/**
 * <AUTHOR>
 * @date 2021/4/10
 * @des
 **/
class OLWebViewClient extends WebViewClient {

    private static final String TAG = "OLWebViewClient";
    private OLWebView OLWebView;
    private long startOpenPage = -1L;
    protected boolean isFirstLoad = true;

    private PreLoadingUrlListener preLoadingUrlListener;
    private OLUICallback OLUICallback;
    private String originURL;
    private String firstEnterUrl;

    private OLPageLifeCycleCallback lifeCycleCallback;

    public AtomicBoolean isFirstUrl = new AtomicBoolean(true);

    public OLWebViewClient(OLWebView OLWebView) {
        this.OLWebView = OLWebView;
        TimeMonitorManager.getInstance().resetTimeMonitor(TimeMonitorConfig.TIME_MONITOR_ID_WEBVIEW_LOAD);
    }

    private WebMonitorInfo webMonitorInfo = new WebMonitorInfo();

    @Override
    public final void onPageStarted(WebView view, String url, Bitmap favicon) {
        super.onPageStarted(view, url, favicon);
        String bid = OLPackageRouter.getBidFromUrl(url);
        // 在这里记录启动时间
        if (!TextUtils.isEmpty(bid)) {
            webMonitorInfo.setOfflineVersion(String.valueOf(OLPackageManager.getInstance().getVersion(bid)));
        } else {
            webMonitorInfo.setOfflineVersion("none");
        }
        webMonitorInfo.setOnPageStarted(System.currentTimeMillis());

        initUIByUrlParams(url);
        //如果是离线包或者网页则统计白屏时间
        if (OLHelper.isHttpUrl(url) || OLHelper.isLocalFile(url)) {
            TimeMonitorManager.getInstance().getTimeMonitor(TimeMonitorConfig.TIME_MONITOR_ID_WEBVIEW_LOAD)
                    .startMonitor();
                /*String logKey = getCurrentLogKey();
                IMKLog tempLog = MKLogManager.getInstance().getTempLog(logKey);
                if (tempLog != null) {
                    MKOpenUrlLog open_url_log = (MKOpenUrlLog) tempLog;
                    open_url_log.recordPageStartTime();
                    MKLogManager.getInstance().recordTempLog(logKey, open_url_log);
                }*/
        }

        if (lifeCycleCallback != null) {
            lifeCycleCallback.onPageStarted(view, url, favicon);
        }
    }

    @Override
    public final void onPageFinished(WebView view, String url) {
        if (view == null || view.getContext() == null || OLHelper.isReleased(OLWebView)) {
            return;
        }
        try {
            String defaultH5Info = OLHelper.generateDefaultInfo(webMonitorInfo, startOpenPage);
            view.loadUrl(defaultH5Info);
        } catch (Exception e) {
            LogUtils.e(e);
        }

        if (!"about:blank".equals(url) && isFirstUrl.get()) {
            isFirstUrl.set(false);
            view.clearHistory();
        }

        super.onPageFinished(view, url);
        if (isFirstLoad) {
            isFirstLoad = false;
        }

        if (lifeCycleCallback != null) {
            lifeCycleCallback.onPageFinished(view, url);
        }
        if (OLHelper.isHttpUrl(url) || OLHelper.isLocalFile(url)) {
                /*String logKey = getCurrentLogKey();
                IMKLog tempLog = MKLogManager.getInstance().getTempLog(logKey);
                if (tempLog != null) {
//                    LogUtil.d(TAG, "tang------页面加载完毕，纪录log " + logKey);
                    MKOpenUrlLog open_url_log = (MKOpenUrlLog) tempLog;
                    open_url_log.recordPageFinishedTime();

                    open_url_log.onOpenSuccess();//页面打开成功
                    open_url_log.onEnd();
                    MKLogManager.getInstance().saveLog(open_url_log);
                    MKLogManager.getInstance().removeTempLog(logKey);
                }*/
            TimeMonitorManager.getInstance().getTimeMonitor(TimeMonitorConfig.TIME_MONITOR_ID_WEBVIEW_LOAD)
                    .end("webview-Over," + url, false);
        }


    }

    /**
     * 返回true代表拦截请求成功
     *
     * @param view
     * @param url
     * @return
     */
    @Override
    public final boolean shouldOverrideUrlLoading(WebView view, String url) {
        boolean hasProcessed = preLoadingUrlListener != null && preLoadingUrlListener.beforeShouldOverrideUrlLoading(url);
        LogUtils.d(TAG, "tang------shouldOverrideUrlLoading " + url + "   hasProcessed " + hasProcessed);
        if (hasProcessed) {
            LogUtils.d(TAG, "tang------shouldOverrideUrlLoading 已经被处理，不需要额外处理");
            return true;
        }
        Uri uri = Uri.parse(url);
        if (uri == null) {
            return true;
        }
        String scheme = uri.getScheme();
        if (TextUtils.isEmpty(scheme) || scheme.equals("http") || scheme.equals("https") || scheme.equals("ftp") || scheme.equals("file")) {
            loadUrl(url);
            return false;
        }
        //todo :oljsbridge 注释 使用js注入和JsPrompt
       /* if (TextUtils.equals("oljsbridge", uri.getScheme())) {
            try {
                OLWebView.processMKBridge(uri.getHost(), uri.getLastPathSegment(), uri.getQueryParameter("param"));
            } catch (Exception e) {
                e.printStackTrace();
            }
            return true;
        }*/
            /*else if (TextUtils.equals("appkcbridge", uri.getScheme())) {
                try {

                    mMKWebView.getBridgeProcessor().processBridge(uri.getHost(), uri.getLastPathSegment(), new JSONObject(uri.getQueryParameter("param")));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }*/
        //处理非 http ftp file 的请求链接
        LogUtils.i("asdf intercept -> " + url);

        Activity activity = getActivity();
        if (activity == null) {
            return false;
        }
        if ("biz.com".equals(uri.getHost())) {
            String appid = uri.getQueryParameter("appid");
            final Intent intent = new Intent(Intent.ACTION_VIEW, uri);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

            List<ResolveInfo> list = activity.getPackageManager().queryIntentActivities(intent, PackageManager.GET_INTENT_FILTERS);
            if (list == null || list.isEmpty()) {
                String gotoTag = uri.getQueryParameter("goto");
                if (!TextUtils.isEmpty(gotoTag)) {//执行goto
                    try {
                        gotoTag = URLDecoder.decode(gotoTag, "UTF-8");
                        gotoTag = URLDecoder.decode(gotoTag, "UTF-8");
                    } catch (UnsupportedEncodingException e) {
                        LogUtils.e(e);
                    }
//                                ActivityHandler.executeAction(gotoTag, context);
                } else {
                    // 打开应用详情，不在这里下载
                    if (!TextUtils.isEmpty(appid)) {
                                   /* Intent appProfileIntent = new Intent(WebKit.getContext(), GameProfileTabsActivity.class);
                                    appProfileIntent.putExtra(GameProfileTabsActivity.KEY_APPID, appid);
                                    context.startActivity(appProfileIntent);*/
                    }
                }
            } else {
                if (getActivity() != null) {
                    getActivity().startActivity(Intent.createChooser(intent, "打开应用"));
                }
            }
        } else {
            try {
                LogUtils.i("view uri = " + uri);
                final Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                activity.startActivity(intent);
            } catch (Throwable throwable) {
                LogUtils.e(throwable);
            }
        }
        return true;

    }

    @Nullable
    @Override
    public WebResourceResponse shouldInterceptRequest(WebView view, String url) {
        WebResourceResponse response = handleAllRequest(view, url, null, null);
        if (response != null) {
            return response;
        }
        LogUtils.e("DNSTest", "olweb handleAllRequest failed: " + url);
        //交给webview自己处理时，需要替换
        String interceptUrl = getInterceptUrl(url);
        LogUtils.d(TAG, "shouldInterceptRequest=====" + interceptUrl);
        return super.shouldInterceptRequest(view, interceptUrl);
    }


    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    @Nullable
    @Override
    public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
        WebResourceResponse response = handleAllRequest(
                view,
                request.getUrl().toString(),
                request.getMethod(),
                request
        );
        if (response != null) {
            return response;
        }
        try {
            LogUtils.e("DNSTest", "olweb handleAllRequest failed: " + request.getUrl().toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        //交给webview自己处理时，需要替换
        String interceptUrl = getInterceptUrl(request.getUrl().toString());
//            LogUtil.d(TAG, "shouldInterceptRequest=====" + interceptUrl);
        return super.shouldInterceptRequest(view, interceptUrl);
    }

    @Override
    public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
        super.onReceivedError(view, errorCode, description, failingUrl);
        handleReceivedErrorInner(view, failingUrl, errorCode, description);
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    @Override
    public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
        super.onReceivedError(view, request, error);
        handleReceivedErrorInner(
                view,
                request.getUrl().toString(),
                error.getErrorCode(),
                error.getDescription() != null ? error.getDescription().toString() : ""
        );
    }

    @Override
    public void onReceivedSslError(WebView view, final SslErrorHandler handler, SslError error) {
        LogUtils.e(TAG, "tang------onReceivedSslError " + error);
        LogUtils.e("DNSTest", "olweb dns error: " + error);
        try {
            WebRefereeHandler.verifyCertificate(view, error.getUrl(), handler);
        } catch (Exception e) {
            e.printStackTrace();
            if (handler != null) {
                handler.cancel();
            }
        }
//            recordOpenErrorLog(MKOpenUrlLog.RECEIVED_ERROR_SSL, error != null ? error.toString() : null);
        if (error != null) {
            WebRefereeHandler.onReceivedError(error.getUrl());
        }
    }

    /**
     * 通过url中的 _ui参数控制页面UI信息
     *
     * @param url url
     */
    private void initUIByUrlParams(String url) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        int _ui = -1;
        try {
            Uri uri = Uri.parse(url);
            String value = uri.getQueryParameter("_ui");
            if (!TextUtils.isEmpty(value)) {
                //_ui 站内方式
                _ui = Integer.valueOf(value);
                if (_ui > 0) {
                    UIParams params = new UIParams(_ui);
                    if (OLUICallback != null) {
                        OLUICallback.changeTitleBtn(params);
                    }
                   /* if (params.hideRightButton()) {
                        clearRightButton();
                    } else {
//                setupDefaultShareButton();
                    }*/

                    try {
                        if (Build.VERSION.SDK_INT != Build.VERSION_CODES.O
                                && Build.VERSION.SDK_INT != Build.VERSION_CODES.O_MR1) {
                            //强制切换为横屏
                            if (params.forceSwitchScreen()) {
                                if (getActivity() != null) {
                                    getActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
                                }
                            } else {
                                //如果允许横屏，则由系统控制
                                if (getActivity() != null) {
                                    if (params.enableSwitchScreen()) {
                                        getActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED);
                                    } else {
                                        getActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
                                    }
                                }
                            }
                        }
                    } catch (Throwable ignore) {

                    }
                    boolean isFullScreen = params.isFullScreen();
                    //控制是否全屏
                    if (isFullScreen) {
                        switchFullscreen(true);
                    } else {
                        switchFullscreen(false);
                    }

                    boolean isShowHeaderBar = !isFullScreen && !params.hideNavigationBar();
                    if (OLUICallback != null) {
                        OLUICallback.uiShowHeaderBar(isShowHeaderBar);
                    }
                } else {
                    //重置
                    if (OLUICallback != null) {
                        OLUICallback.uiShowHeaderBar(true);
                    }
//            setupDefaultShareButton();//显示默认的分享
                    switchFullscreen(false);//退出全屏
                }
            } else {
                //_status js
                value = uri.getQueryParameter("_status");
                if (TextUtils.isEmpty(value)) {
                    return;
                }
                BigInteger bigInteger = new BigInteger(value, 16);
                _ui = bigInteger.intValue();
                if (_ui > 0) {
                    StatusParams params = new StatusParams(_ui);
                    if (OLUICallback != null) {
                        OLUICallback.changeTitleBtn(params);
                    }
                  /*  if (params.hideRightButton()) {
                        clearRightButton();
                    } else {
//                setupDefaultShareButton();
                    }*/

                    try {
                        if (Build.VERSION.SDK_INT != Build.VERSION_CODES.O
                                && Build.VERSION.SDK_INT != Build.VERSION_CODES.O_MR1) {
                            //强制切换为横屏
                            if (params.forceSwitchScreen()) {
                                if (getActivity() != null) {
                                    getActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
                                }
                            } else {
                                //如果允许横屏，则由系统控制
                                if (getActivity() != null) {
                                    if (params.enableSwitchScreen()) {
                                        getActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED);
                                    } else {
                                        getActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
                                    }
                                }
                            }
                        }
                    } catch (Throwable ignore) {

                    }

                    boolean isFullScreen = params.isFullScreen();
                    //控制是否全屏
                    if (isFullScreen) {
                        switchFullscreen(true);
                    } else {
                        switchFullscreen(false);
                    }

                    boolean isShowHeaderBar = !isFullScreen && !params.hideNavigationBar();
                    if (OLUICallback != null) {
                        OLUICallback.uiShowHeaderBar(isShowHeaderBar);
                    }
                } else {
                    //重置
                    if (OLUICallback != null) {
                        OLUICallback.uiShowHeaderBar(true);
                    }
//            setupDefaultShareButton();//显示默认的分享
                    switchFullscreen(false);//退出全屏
                }
            }

        } catch (Exception ex) {
        }
        LogUtils.d(TAG, "tang------ _ui参数是 " + _ui);
    }


    public final void loadUrl(String url) {
        if (TextUtils.isEmpty(url) || OLHelper.isReleased(OLWebView)) {
            return;
        }
        //如果链接已经被额外处理，则不需要执行下面的操作
        if (preLoadingUrlListener != null && preLoadingUrlListener.beforeLoadUrl(url)) {
            return;
        }
        try {
            String interceptUrl = getInterceptUrlAndCopyCookie(url);
            LogUtils.i(TAG, interceptUrl);
            OLWebView.startLoadUrl(interceptUrl);
        } catch (Exception ex) {
            ex.printStackTrace();
            OLWebView.loadUrl(url);
        }
    }

    public Activity getActivity() {
        if (OLWebView != null && OLWebView.getContext() instanceof Activity) {
            return (Activity) OLWebView.getContext();
        }
        return null;
    }

    private WebResourceResponse handleAllRequest(WebView webView, String url, String method, WebResourceRequest request) {
        if (preLoadingUrlListener != null) {
            WebResourceResponse response = preLoadingUrlListener.shouldInterceptRequest(webView, url);
            if (response != null) {
                return response;
            }
        }
        WebResourceResponse response = InterceptUtils.interceptRequest(url, originURL);
        if (response != null) {
            LogUtils.d("DNSTest", "加载本地资源 " + url);
            return response;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            LogUtils.d("DNSTest", "olweb method " + request.getMethod() + " ---> " + url);
            boolean enableWebDns = OLKit.getInterceptConfig() != null && OLKit.getInterceptConfig().isEnableWebDns();
            LogUtils.d("DNSTest", "olweb enableWebDns --> " + enableWebDns);
            //todo 暂时不需要dns
            /*if (enableWebDns && MDDNSEntrance.getInstance().useDNS(Uri.parse(url).getHost())) {

                LogUtils.d("DNSTest", "mkweb use dns --> " + url);
                byte[] postData = postMap.remove(url);
                response = WebDNSHandler.handleInterceptRequest(postData, webView, request);
                if (response == null) {
                    LogUtils.e("DNSTest", "mkweb use dns failed, url: " + url);
                }
            } else {*/
            response = WebRefereeHandler.interceptRequestInner(url, method, request);
            if (response == null) {
                LogUtils.e("DNSTest", "olweb use referee failed, url: " + url);
            }
//            }
            return response;
        }
        return null;
    }

    private void handleReceivedErrorInner(WebView view, String failingUrl, int errorCode, String description) {
        LogUtils.e(TAG, "tang------handleReceivedErrorInner " + errorCode + ":" + description + "  " + failingUrl);
        if (lifeCycleCallback != null) {
            lifeCycleCallback.onPageError(view, errorCode, description, failingUrl);
        }

        WebRefereeHandler.onReceivedError(failingUrl);
    }

    public String getInterceptUrl(String originUrl) {
        if (TextUtils.isEmpty(originUrl)) {
            return originUrl;
        }
        String targetUrl = originUrl;
        if (originUrl.startsWith("http://") || originUrl.startsWith("https://")) {
            File localFile = OLPackageRouter.getOfflineFileFromUrl(originUrl);
            if (localFile != null) {
                LogUtils.d(TAG, "exists local file " + localFile.getAbsolutePath());
                targetUrl = originUrl;
            } else {
                String interceptUrl = WebRefereeHandler.getInterceptUrl(originUrl);
                if (!TextUtils.isEmpty(interceptUrl)) {
                    targetUrl = interceptUrl;
                    // 记录下已经 referee 过的 host
                    String originalHost = OLHelper.getHost(originUrl);
                    if (OLWebView != null) {
                        OLWebView.addRefereeHost(originalHost);
                    }

                    String interceptHost = OLHelper.getHost(targetUrl);
                    if (OLWebView != null) {
                        OLWebView.addRefereeHost(interceptHost);
                    }
                }
            }
        }
        return targetUrl;
    }

    /**
     * 切换全屏状态
     *
     * @param isFullscreen 是否全屏
     */
    public void switchFullscreen(boolean isFullscreen) {
        try {
            Activity activity = getActivity();
            if (activity == null) {
                return;
            }
            Window window = activity.getWindow();

            int systemUiVisibility = window.getDecorView().getSystemUiVisibility();
            WindowManager.LayoutParams attrs = window.getAttributes();
            // go full screen
            if (isFullscreen) {
                attrs.flags |= WindowManager.LayoutParams.FLAG_FULLSCREEN;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    attrs.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
                    systemUiVisibility |= View.SYSTEM_UI_FLAG_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN;
                }
            } else {
                // go non-full screen
                attrs.flags &= (~WindowManager.LayoutParams.FLAG_FULLSCREEN);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    attrs.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_DEFAULT;
                    systemUiVisibility &= ~(View.SYSTEM_UI_FLAG_FULLSCREEN);
                }
            }
            window.setAttributes(attrs);
            window.getDecorView().setSystemUiVisibility(systemUiVisibility);

            if (OLUICallback != null) {
                OLUICallback.uiShowHeaderBar(!isFullscreen);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static final String URL_REDIRECT_PREFIX = "https://www.biz.com/checkurl/?url=";
    public static final String URL_PASSPORT_PREFIX = "https://passport.biz.com/authorize?redirect_uri=";

    /**
     * 复制Cookie
     *
     * @param url
     * @return
     */
    public String getInterceptUrlAndCopyCookie(String url) {
        String beforeUr = beforeLoadUrl(url);

        if (beforeUr.startsWith(URL_REDIRECT_PREFIX) || beforeUr.startsWith(URL_PASSPORT_PREFIX)) {
            // 只有checkurl和passport才走这里
            return getInterceptUrl(beforeUr);
        }
//        if (!beforeUr.equals(newUr)) {
//            CookieSyncManager.createInstance(getContext());
//            CookieManager.getInstance().setAcceptCookie(true);
//            // 被referee了, cookie复制
//            CookieManager.getInstance().setCookie(newUr, CookieManager.getInstance().getCookie(beforeUr));
//            CookieSyncManager.getInstance().sync();
//        }

        return beforeUr;
    }

    /**
     * 1.重写所有的url:此方法是OL页面的最终入口
     * 2.用于统计页面打开
     *
     * @param url
     * @return
     */
    private String beforeLoadUrl(String url) {
        //webview 可能已经被销毁了，但是刚好执行了loadurl，就会报错了
        if (OLHelper.isReleased(OLWebView) || TextUtils.isEmpty(url)) {
            return url;
        }
        if (url.startsWith(OLConstants.jsPrefix)) {
            return url;
        }
        if (!OLHelper.isHttpUrl(url) && !url.startsWith("file://")) {
//            LogUtil.d(TAG, "tang-----不是url:" + url);
            return url;
        }
        String path = null;
        if (url.contains("#")) {
            String[] split = url.split("#");
            url = split[0];
            path = split[1];
        }
//        LogUtil.d(TAG, "tang------是url:" + url);
        String bid = OLPackageRouter.getBidFromUrl(url);//一律不处理不带 _bid参数的url
        if (TextUtils.isEmpty(bid)) {
            if (!TextUtils.isEmpty(path)) {
                return url + "#" + path;
            }
            return url;
        }
        //test
//        LocalServerHandler.startServer(bid);
        /**
         * 从 {@link #loadUrl(String)} 入口进入此方法时，需要纪录下初始的url，便于统计打点
         */
        originURL = url;
        if (TextUtils.isEmpty(firstEnterUrl)) {
            firstEnterUrl = url;
        }
        /**
         * 如果url是本地文件
         */
        boolean isLocalFileUrl = false;
        //如果是加载本地的文件，自动增加 _offline参数
        if (url.startsWith(OLConstants.LOCAL_FILE_SIGN)) {
            //有可能是相对路径，则直接添加 _offline=1标记即可
            if (url.indexOf("?") > 0) {
                url = url + OLConstants.OFFLINE_SIGN;
            } else {
                url = url + "?" + OLConstants.OFFLINE_SIGN;
            }
            isLocalFileUrl = true;
        }
        String target_url = url;

        //所有页面中的url最终都会进入此方法，所以为了避免一些图片，js文件被统计，根据是否有 _bid来进行过滤，只统计带_bid的url
        /*String logKey = getCurrentLogKey();
        IMKLog tempLog = MKLogManager.getInstance().getTempLog(logKey);
        MKOpenUrlLog open_url_log = null;
        if (tempLog != null) {
            open_url_log = (MKOpenUrlLog) tempLog;
        } else {
            //没有日志，则建立一条新日志
            open_url_log = new MKOpenUrlLog(url);
            open_url_log.onStart();
            open_url_log.setIsNewWebView(false);
            open_url_log.setInitWebViewTime(0);
//            LogUtil.d(TAG, "tang------创建新日志 " + url);
        }
        open_url_log.setBid(bid);*/

        if (isLocalFileUrl) {
            /*open_url_log.setVerifyTime(0);
            open_url_log.setRouterTime(0);
            open_url_log.setIsOfflinePackage(true);*/
        } else {
            //离线包打开之前 预处理离线包
            long start_process = System.currentTimeMillis();
            int result = OLPackageManager.getInstance().preProcessPackage(bid, originURL);
            boolean isExistValidatePackage = result == 1;
            LogUtils.i(TAG, "tang------预处理离线包，是否存在合法的离线包：" + isExistValidatePackage + "   预处理耗时 " + (System.currentTimeMillis() - start_process));

            long startRouterTime = System.currentTimeMillis();
            String offline_url = OLPackageRouter.getOfflineUrlFromUrl(url);
            /*open_url_log.setRouterTime(System.currentTimeMillis() - startRouterTime);//统计路由时间*/

            //如果不存在合法的离线包或者不存在离线包文件
            if (isExistValidatePackage && !TextUtils.isEmpty(offline_url)) {
                target_url = offline_url;
                /*open_url_log.setIsOfflinePackage(true);*/
            } else {
                /*open_url_log.setIsOfflinePackage(false);*/
                LogUtils.d(TAG, "tang------没有离线包,去检查更新 " + bid);
                //如果链接里面带了 _bid ，但是没有离线包，则加载在线包，同时去下载离线包
                OLPackageManager.getInstance().checkUpdate(bid, originURL, null);
            }
        }
//        MKLogManager.getInstance().recordTempLog(logKey, open_url_log);
        LogUtils.d(TAG, "tang-----最终访问的url " + target_url);
        if (!TextUtils.isEmpty(path)) {
            target_url = target_url + "#" + path;
        }
        return target_url;
    }

    public void onDestroy() {

    }

    public String getFirstEnterUrl() {
        return firstEnterUrl;
    }

    public String getOriginURL() {
        return originURL;
    }

    public void setPreLoadingUrlListener(PreLoadingUrlListener preLoadingUrlListener) {
        this.preLoadingUrlListener = preLoadingUrlListener;
    }

    public void setLifeCycleCallback(OLPageLifeCycleCallback lifeCycleCallback) {
        this.lifeCycleCallback = lifeCycleCallback;
    }

    public void setOLuiCallback(OLUICallback OLUICallback) {
        this.OLUICallback = OLUICallback;
    }

    public void resetFirstUrl() {
        isFirstUrl.set(true);
    }

};
