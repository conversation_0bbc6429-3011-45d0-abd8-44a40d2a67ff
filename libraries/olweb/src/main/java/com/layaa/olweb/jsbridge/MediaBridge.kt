package com.layaa.olweb.jsbridge

import com.layaa.olweb.annotation.OLJsBridge
import com.layaa.olweb.annotation.OLSubscribe
import com.layaa.olweb.annotation.ThreadMode
import com.layaa.olweb.constant.OLConstants
import com.layaa.olweb.utils.OLHelper
import com.layaa.olweb.widget.OLWebView
import org.json.JSONObject

/**
 * <AUTHOR>
 * @date 2021/3/10
 * @des
 */
@OLSubscribe(nameSpace = "media")
class MediaBridge(private val webview: OLWebView) {

    private var selectImageCallback: String? = null

    var imageSelector: (() -> Unit)? = null

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    fun selectImage(params: JSONObject) {
        selectImageCallback = params.optString(OLConstants.callBack)
        imageSelector?.invoke()
    }


    fun onImageSelect(url: String) {
        val result = JSONObject()
        result.put("image", url)
        OLHelper.insertCallback(webview, selectImageCallback, result.toString())
    }

}
