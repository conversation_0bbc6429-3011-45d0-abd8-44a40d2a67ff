package com.layaa.olweb.jsbridge;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;

import com.layaa.olweb.OLKit;
import com.layaa.olweb.annotation.OLJsBridge;
import com.layaa.olweb.annotation.OLSubscribe;
import com.layaa.olweb.annotation.ThreadMode;
import com.layaa.olweb.constant.OLConstants;
import com.layaa.olweb.utils.OLFileHelper;
import com.layaa.libutils.EncryptUtils;
import com.layaa.libutils.FileUtils;
import com.layaa.libutils.SecurityUtils;
import com.layaa.libutils.module_log.LogUtils;

import org.json.JSONObject;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2021/3/10
 * @des
 **/
@OLSubscribe(nameSpace = "storage")
public class StorageBridge {

    public static final String TAG = "StorageBridge";
    private String path;

    public StorageBridge(String url) {
        if (!TextUtils.isEmpty(url)) {
            Uri uri = Uri.parse(url);
            path = SecurityUtils.getMD5(uri.getHost());
        }
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_SYNC)
    public void clearItem(JSONObject params) {
        //如果path为空，则删除所有的localstorage
        if (TextUtils.isEmpty(path)) {
            FileUtils.deleteAllFilesSafely(OLFileHelper.getLocalStorageDir(OLKit.getContext()));
        } else {
            File dir = getStoreDirByPath(OLKit.getContext(), path);
            FileUtils.deleteAllFilesSafely(dir);
        }
    }

    private File getStoreDirByPath(Context pContext, String path) {
        String dir = EncryptUtils.getMD5(TextUtils.isEmpty(path) ? "" : path);
        File root = OLFileHelper.getLocalStorageDir(pContext);
        return new File(root, dir);
    }

    public String getItem(JSONObject params) {
        String storageCallback = params.optString(OLConstants.callBack);
        String value = getItem(params.optString("path"), params.optString("key"));
        return value;
//        MKHelper.insertCallback(mkWebView, storageCallback, value);
    }

    public void removeItem(JSONObject params) {
        try {
            File file = getStoreFile(params.optString("key"), params.optString("path"));
            if (file != null && file.exists()) {
                file.delete();
            }
        } catch (Exception ignore) {

        }

    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_SYNC)
    public void setItem(JSONObject params) {
        final String value = params.optString("value", "");
        long start = System.currentTimeMillis();
        try {
            File file = getStoreFile(params.optString("key"), params.optString("path"));
            if (file == null) {
                return;
            }
            if (file.exists()) {
                file.delete();
            } else {
                file.createNewFile();
            }
            FileUtils.writeToFile(value.getBytes(), file.getAbsolutePath(), false);
            LogUtils.d(TAG, "tang------写入文件内容耗时 " + (System.currentTimeMillis() - start) + "   file " + file.getAbsolutePath());//+ "   " + finalData);
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }

    private File getStoreFile(String key, String path) {
        if (TextUtils.isEmpty(key)) {
            return null;
        }
        String dir = EncryptUtils.getMD5(TextUtils.isEmpty(path) ? "" : path);
        String filename = EncryptUtils.getMD5(TextUtils.isEmpty(key) ? "" : key);
        return OLFileHelper.getLocalStorageFile(dir, filename);
    }

    private String getItem(String path, String key) {
        long start = System.currentTimeMillis();
        try {
            File file = getStoreFile(key, path);
            byte[] source = FileUtils.readFileToBytes(file);
            if (source == null) {
//                LogUtil.e(TAG, "tang-------getItem内容为空 " + key + "  path:" + path + "  file:" + file.getAbsolutePath());
                return "";
            }
            String result = new String(source);
            LogUtils.d(TAG, "tang------读取文件内容耗时 " + (System.currentTimeMillis() - start) + "   file " + file.getAbsolutePath());//+ "   " + decryptData);
            return result;
        } catch (Exception e) {
            LogUtils.e(e);
        }
        return "";
    }
}
