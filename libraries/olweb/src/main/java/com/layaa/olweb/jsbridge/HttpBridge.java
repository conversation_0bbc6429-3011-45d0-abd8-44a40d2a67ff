package com.layaa.olweb.jsbridge;

import android.text.TextUtils;
import android.util.Base64;

import com.layaa.olweb.OLKit;
import com.layaa.olweb.annotation.OLJsBridge;
import com.layaa.olweb.annotation.OLSubscribe;
import com.layaa.olweb.annotation.ThreadMode;
import com.layaa.olweb.config.IHttpClient;
import com.layaa.olweb.constant.OLConstants;
import com.layaa.olweb.prefetch.PreFetchManager;
import com.layaa.olweb.prefetch.PrefetchData;
import com.layaa.olweb.server.LocalServerHandler;
import com.layaa.olweb.session.OLWebSessionHandler;
import com.layaa.olweb.utils.OLHelper;
import com.layaa.olweb.widget.OLWebView;
import com.layaa.libutils.FileUtils;
import com.layaa.libutils.module_log.LogUtils;
import com.layaa.libutils.module_thread.task.AbsJob;
import com.layaa.libutils.module_thread.task.ThreadPool;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import androidx.annotation.Nullable;

/**
 * <AUTHOR>
 * @date 2020-01-10
 * @des
 **/
@OLSubscribe(nameSpace = "http")
public class HttpBridge {
    private static final String TAG = "HttpBridge";
    private static final String UTF_8 = "UTF_8";
    private OLWebView OLWebView;

    public HttpBridge(OLWebView OLWebView) {
        this.OLWebView = OLWebView;
    }

    private boolean checkStatus() {
        return OLWebView != null && !OLWebView.isReleased();
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_CURRENT)
    public void request(JSONObject params) {
        String httpMethod = params.optString("type", "post");
        String httpUrl = params.optString("url");

        try {
            // 解析 oldUrl
            URI oldUri = new URI(httpUrl);
            // 解析 newUrl
            URI newUri = new URI(OLKit.getBaseUrl());

            // 获取 newUrl 的协议和主机部分
            String newScheme = newUri.getScheme(); // http 或 https
            String newHost = newUri.getHost(); // wohoh.live
            int newPort = newUri.getPort(); // 如果没有端口号，返回 -1

            // 构建新的 URI，保留 oldUrl 的路径、查询参数和片段
            URI finalUri = new URI(
                    newScheme, // 使用 newUrl 的协议
                    oldUri.getUserInfo(), // 保留 oldUrl 的用户信息（如果有）
                    newHost, // 使用 newUrl 的主机
                    newPort, // 如果 newUrl 没有端口号，使用 oldUrl 的端口号
                    oldUri.getPath(), // 保留 oldUrl 的路径
                    oldUri.getQuery(), // 保留 oldUrl 的查询参数
                    oldUri.getFragment() // 保留 oldUrl 的片段
            );
            httpUrl = finalUri.toString();
        }catch (Exception e){
            LogUtils.e(e);
            httpUrl = params.optString("url");
        }
        JSONObject requestParams = params.optJSONObject("data");
        //是否需要使用 base64编码
        boolean encodeBase64 = false;
        if (params.has("encode")) {
            encodeBase64 = params.optInt("encode", 0) == 1;
        }
        //                        LogUtil.d(TAG, "tang-------执行request请求 " + httpUrl + "  " + httpMethod + "  " + requestParams);
        String requestCallback = params.optString(OLConstants.callBack);
        runHttpRequest(httpUrl, httpMethod, requestParams, requestCallback, encodeBase64);
    }

    public void createServer(JSONObject params) {
        createLocalServer(params);
    }

    public void resetSession(JSONObject params) {
        //清空web session
        OLWebSessionHandler.clearWebSession(OLKit.getContext());
        OLWebSessionHandler.updateWebSession(new OLWebSessionHandler.WebSessionUpdateListener() {
            @Override
            public void onFinished() {
                OLHelper.insertCallback(OLWebView, OLWebView.getJSCallback(params), "", "成功", "0");
            }

            @Override
            public void onError() {
                OLHelper.insertCallback(OLWebView, OLWebView.getJSCallback(params), "", "失败", "1");
            }
        });
    }

    /**
     * 执行http请求
     *
     * @param url
     * @param method
     * @param jsonParams
     * @param callback
     */

    private void runHttpRequest(final String url, final String method, @Nullable final JSONObject jsonParams, final String callback, final boolean encodeBase64) {
        IHttpClient iRequester = OLKit.getHttpClient();
        if (iRequester == null) {
            LogUtils.e("OL", "ol-http-requester is NULL");
            return;
        }
        AbsJob runnable = new AbsJob() {
            @Override
            public void run() {
                long start = System.currentTimeMillis();
//                String requestData = checkPreFetch(url, jsonParams);
                //不使用预加载
                String requestData = null;
                LogUtils.d(TAG, "check pre fetch cast: %d, data: %s", (System.currentTimeMillis() - start), requestData);
                if (TextUtils.isEmpty(requestData)) {
                    try {
                        requestData = doRequest(url, method, jsonParams);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        try {
                            JSONObject json = new JSONObject();
                            json.put("ec", -1);
                            json.put("em", "网络请求失败");
                            json.put("data", requestData);
                            json.put("exmsg", ex.getMessage());
                            LogUtils.d(TAG, "tang-------执行request请求失败 " + json.toString());
                            OLHelper.insertCallback(OLWebView, callback, json.toString());
                        } catch (JSONException e) {
                            LogUtils.e(e);
                        }
                    }
                }
                if (TextUtils.isEmpty(requestData))
                    return;
                try {
                    JSONObject json = new JSONObject(requestData);
                    requestData = json.toString();
                } catch (Exception e) {
                }
                try {
                    if (encodeBase64) {
                        requestData = Base64.encodeToString(requestData.getBytes(), Base64.NO_WRAP);
                    } else {
                        //去掉
                        /*final String code = OLHelper.getChangeCode(requestData);
                        requestData = OLHelper.preChangeSpecialCode(requestData, code);
                        requestData = Uri.encode(requestData, UTF_8);
                        requestData = OLHelper.afterEncode(requestData, code);*/
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                OLHelper.insertCallback(OLWebView, callback, requestData);
            }
        };
        ThreadPool.submit(runnable, "runHttpRequest");
    }

    /**
     * 真正执行http request
     *
     * @param url
     * @param method
     * @param jsonParams
     * @return
     * @throws Exception
     */

    private String doRequest(String url, final String method, @Nullable final JSONObject jsonParams) throws Exception {
        String requestData = "";
        IHttpClient requester = OLKit.getHttpClient();
        long start = System.currentTimeMillis();
        LogUtils.d(TAG, "tang-------执行request请求，开始 " + url + "  " + method);
        if (canBeUsedAsForm(jsonParams)) {
            HashMap<String, String> params = null;
            if (jsonParams != null) {
                params = new HashMap<>();
                boolean needDeviceId = jsonParams.optInt("needDeviceId", 0) == 1;
           /* if (needDeviceId) {
                params.put(requester.getDeviceParamsKey(IHttpClient.DNA), requester.getDeviceId(IHttpClient.DNA));
            }
//            addOfflineVersion(jsonParams);
            String idKey = requester.getDeviceParamsKey(IHttpClient.NORMAL);
            String idValue = requester.getDeviceId(IHttpClient.NORMAL);
            if (!TextUtils.isEmpty(idKey) && !TextUtils.isEmpty(idValue)) {
                params.put(idKey, idValue);
            }*/
                getDataFromJson(params, jsonParams, null);
            }
            if ("get".equalsIgnoreCase(method)) {
                requestData = requester.doGet(url, params, null);
            } else {
                requestData = requester.doPost(url, params, null);
            }
        } else {
            requestData = requester.doPost(url, jsonParams.toString(), null);
        }
        LogUtils.d(TAG, "tang-------执行request请求，结束 " + url + "  " + method + "    耗时 " + (System.currentTimeMillis() - start));
        return requestData;
    }

    public static boolean canBeUsedAsForm(@Nullable JSONObject json) {
        if (json == null) {
            return true;
        }
        JsonObject jsonObject = JsonParser.parseString(json.toString()).getAsJsonObject();
        for (String key : jsonObject.keySet()) {
            JsonElement value = jsonObject.get(key);
            if (!(value.isJsonPrimitive() || value.isJsonNull())) {
                return false;
            }
        }
        return true;
    }

    public static void getDataFromJson(Map<String, String> outMap, JSONObject jo, String parent) {
        Iterator<String> keys = jo.keys();
        String key = null;
        while (keys.hasNext()) {
            key = keys.next();
            Object obj = jo.opt(key);
            if (obj == null)
                continue;
            if (obj instanceof JSONObject) {
                getDataFromJson(outMap, (JSONObject) obj, getNextParent(parent, key, -1));
            } else if (obj instanceof JSONArray) {
                JSONArray ja = (JSONArray) obj;
                int len = ja.length();
                for (int i = 0; i < len; i++) {
                    Object child = ja.opt(i);
                    if (child == null)
                        continue;
                    if (child instanceof JSONObject) {
                        getDataFromJson(outMap, (JSONObject) child, getNextParent(parent, key, i));
                    } else {
                        outMap.put(getKey(parent, key) + "[" + i + "]", child.toString());
                    }
                }
            } else {
                outMap.put(getKey(parent, key), obj.toString());
            }
        }
    }

    private static final String JSON_FORMAT_STR = "[%s]";

    private static String getNextParent(String parent, String key, int index) {
        String result = null;
        String format = index >= 0 ? "[" + index + "]" + JSON_FORMAT_STR : JSON_FORMAT_STR;
        if (isEmtpy(parent)) {
            result = key + format;
        } else {
            result = String.format(parent, key) + format;
        }
        return result;
    }

    private static String getKey(String parent, String key) {
        if (isEmtpy(parent)) {
            return key;
        } else {
            return String.format(parent, key);
        }
    }

    private static boolean isEmtpy(CharSequence text) {
        return text == null || text.length() == 0;
    }

    /**
     * 检查预请求，若正在执行预请求，等待
     *
     * @param url        api
     * @param jsonParams params
     * @return 请求数据，可能为空
     */

    private String checkPreFetch(final String url, final JSONObject jsonParams) {
        if (!checkStatus()) {
            return null;
        }
        String ourl = OLWebView.getOriginURL();
        if (TextUtils.isEmpty(ourl))
            return null;
        Object uniqueKey = OLWebView.hashCode();
        final String prefetchKey = PreFetchManager.getPrefetchKey(uniqueKey, url, jsonParams);
        long time = System.currentTimeMillis();
        try {
            PreFetchManager.getInstance().waitForKey(prefetchKey);
        } catch (InterruptedException e) {
            LogUtils.e(TAG, e);
        }
        time = System.currentTimeMillis() - time;
        LogUtils.d(TAG, "wait time: %d, prefetch key: %s", time, prefetchKey);
        File cache = PreFetchManager.getInstance().getCacheFile(uniqueKey, ourl, prefetchKey);
        if (cache != null && cache.exists()) {
            try {
                String result = FileUtils.readStr(cache);
                PreFetchManager.getInstance().deleteCacheFile(prefetchKey);
                PrefetchData prefetchData = new PrefetchData(result);
                prefetchData.setWaitTime(time);
                return prefetchData.toString();
            } catch (IOException e) {
                LogUtils.e(TAG, e);
            }
        }
        return null;
    }

    public void createLocalServer(final JSONObject param) {
        if (!checkStatus()) {
            return;
        }
        AbsJob job = new AbsJob() {
            @Override
            protected void run() {
                if (!checkStatus()) {
                    return;
                }
                String bid = param.optString("bid");
                String env = param.optString("env");
                String callback = param.optString(OLConstants.callBack);
                if (TextUtils.isEmpty(bid)) {
                    OLHelper.insertCallback(OLWebView, callback,
                            OLHelper.assembleJsonObject(new String[]{"error"}, new Object[]{"no bid!"}).toString());
                    return;
                }
                LocalServerHandler.setEnvType(env);
                LocalServerHandler.startServer(bid);
                String value = null;
                if (LocalServerHandler.isServerRunning()) {
                    value = OLHelper.assembleJsonObject(new String[]{"host", "port", "sign"},
                                    new Object[]{LocalServerHandler.getHost(), LocalServerHandler.getPort(), LocalServerHandler.getSign()})
                            .toString();
                } else {
                    value = OLHelper.assembleJsonObject(new String[]{"error"}, new Object[]{"打开失败"}).toString();
                }
                OLHelper.insertCallback(OLWebView, callback, value);
            }
        };
        ThreadPool.submit(job, "createLocalServer");
    }

    /**
     *直播需求
     *
     ol 的网络代发需要在请求参数中添加离线包的版本号
     *
     *
     @param
     params
     */

   /* private void addOfflineVersion(final JSONObject params) {
        if (params == null || mkWebview == null || TextUtils.isEmpty(mkWebview.getBid()))
            return;

        try {
            String bid = mkWebview.getBid();
            OfflinePackage offlinePackage = MKPackageManager.getInstance().getPackageInfo(bid);
            if (offlinePackage != null) {
                String versionStr = offlinePackage.getVersionStr();
                if (!TextUtils.isEmpty(versionStr)) {
                    params.put("mkVersion", versionStr);
                } else {
                    params.put("mkVersion", offlinePackage.getVersion());
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }*/
}
