package com.layaa.olweb.jsbridge;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;

import com.layaa.olweb.annotation.OLJsBridge;
import com.layaa.olweb.annotation.OLSubscribe;
import com.layaa.olweb.annotation.ThreadMode;
import com.layaa.olweb.utils.OLHelper;
import com.layaa.olweb.widget.OLWebView;
import com.layaa.libutils.module_log.LogUtils;

import org.json.JSONObject;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

import androidx.annotation.NonNull;

/**
 * <AUTHOR>
 * @date 2021/1/12
 * @des
 **/
public class JsBridgeExecutor {

    private static final String TAG = "JsBridgeExecutor";
    /*
     * In newer class files, compilers may add methods. Those are called bridge or synthetic methods.
     * EventBus must ignore both. There modifiers are not public but defined in the Java class file format:
     * http://docs.oracle.com/javase/specs/jvms/se7/html/jvms-4.html#jvms-4.6-200-A.1
     */
    private static final int BRIDGE = 0x40;
    private static final int SYNTHETIC = 0x1000;
    private static final int INVOKE_METHOD = 1;

    private static final int MODIFIERS_IGNORE = Modifier.ABSTRACT | Modifier.STATIC | BRIDGE | SYNTHETIC;
    /**
     * 使用两个map  存储 防止各个模块继承同一个bridge 理论上不同模块会使用不同bridge，但是现有业务部分融合在一起，兼容当前业务
     * CLASS_CATCH 存储 类名称
     * OBJECT_CATCH 存储实例
     */
    private OLWebView OLWebView;
    private ConcurrentHashMap<String, CopyOnWriteArrayList<String>> CLASS_CATCH = new ConcurrentHashMap<>();
    private ConcurrentHashMap<String, Object> OBJECT_CATCH = new ConcurrentHashMap<>();

    private Handler handler = new Handler(Looper.getMainLooper(), new Handler.Callback() {
        @Override
        public boolean handleMessage(@NonNull Message msg) {
            switch (msg.what) {
                case INVOKE_METHOD:
                    invokeMethod(msg.obj);
                    break;
                default:
                    break;
            }
            return false;
        }
    });

    private JsBridgeQueue queue = new JsBridgeQueue(TAG);

    private JsBridgeQueue.Callback callback = new JsBridgeQueue.Callback() {
        @Override
        public boolean handleMessage(Message msg) {
            switch (msg.what) {
                case INVOKE_METHOD:
                    invokeMethod(msg.obj);
                    break;
                default:
                    break;
            }
            return false;
        }
    };

    private void invokeMethod(Object obj) {
        if (obj == null) {
            return;
        }
        if (obj instanceof MethodInfo) {
            MethodInfo info = (MethodInfo) obj;
            if (OBJECT_CATCH.containsKey(info.object.getClass().getName())) {
                try {
                    Object o = info.method.invoke(info.object, info.params);
                    LogUtils.i(TAG, "obj =" + info.toString());
                    String jsCallback = OLHelper.getJsCallback(info.params);
                    boolean callback = o instanceof String && !TextUtils.isEmpty(jsCallback);
                    if (callback) {
                        OLHelper.insertCallback(OLWebView, jsCallback, (String) o);
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                }
            }
        }
    }


    public JsBridgeExecutor(OLWebView OLWebView) {
        this.OLWebView = OLWebView;
    }

    public void executor(String nameSpace, String method, JSONObject params) {
        CopyOnWriteArrayList<String> list = CLASS_CATCH.get(nameSpace);
        if (list != null && list.size() > 0) {
            for (String s : list) {
                Object obj = OBJECT_CATCH.get(s);
                if (obj == null) {
                    continue;
                }
                try {
                    Method obgMethod = obj.getClass().getDeclaredMethod(method, JSONObject.class);
                    int modifiers = obgMethod.getModifiers();
                    if ((modifiers & Modifier.PUBLIC) != 0 && (modifiers & MODIFIERS_IGNORE) == 0) {
                        MethodInfo info = new MethodInfo();
                        info.method = obgMethod;
                        info.object = obj;
                        info.params = params;
                        info.nameSpace = nameSpace;
                        Class<?>[] parameterTypes = obgMethod.getParameterTypes();
                        if (parameterTypes.length > 0) {
                            //有注解 需要切换线程
                            OLJsBridge jsBridgeAnnotation = obgMethod.getAnnotation(OLJsBridge.class);
                            if (jsBridgeAnnotation == null || jsBridgeAnnotation.threadMode() == ThreadMode.THREAD_CURRENT) {
                                obgMethod.invoke(obj, params);
                                return;
                            }
                            if (jsBridgeAnnotation.threadMode() == ThreadMode.THREAD_MAIN && isMainThread()
                                    || jsBridgeAnnotation.threadMode() == ThreadMode.THREAD_SYNC && !isMainThread()) {
                                obgMethod.invoke(obj, params);
                                return;
                            }

                            switch (jsBridgeAnnotation.threadMode()) {
                                case ThreadMode.THREAD_MAIN:
                                    handler.sendMessage(Message.obtain(handler, INVOKE_METHOD, info));
                                    break;
                                case ThreadMode.THREAD_SYNC:
                                    queue.sendMessage(INVOKE_METHOD, info);
                                    break;
                                default:
                                    invokeMethod(info);
                                    break;
                            }
                        } else {
                            invokeMethod(info);
                        }
                    } else {
                        throw new IllegalArgumentException("class:" + obj.getClass().getName() + " method:" + method + " must be public");
                    }
                    break;
                } catch (NoSuchMethodException | IllegalAccessException |
                         InvocationTargetException e) {
                    e.printStackTrace();
                }
            }

        }
    }

    public void start() {
        queue.Run();
        queue.setCallback(callback);
    }

    public void stop() {
        queue.Quit();
        queue.setCallback(null);
    }

    public void addObserver(Object observer) {
        if (observer == null) {
            throw new IllegalArgumentException("obj is null");
        }
        Class<?> clz = observer.getClass();
        OLSubscribe subscribe = clz.getAnnotation(OLSubscribe.class);
        if (subscribe == null) {
            throw new IllegalArgumentException("class " + clz.getName() + "没有添加OLSubscribe桥接");
        }
        final String nameSpace = subscribe.nameSpace();
        if (TextUtils.isEmpty(nameSpace)) {
            throw new IllegalArgumentException("class " + clz.getName() + "OLSubscribe nameSpace不能为空");
        }
        String clsName = observer.getClass().getName();
        CopyOnWriteArrayList<String> list = CLASS_CATCH.get(nameSpace);
        if (list == null) {
            list = new CopyOnWriteArrayList<>();
            CLASS_CATCH.put(nameSpace, list);
        }
        if (!list.contains(clsName)) {
            list.add(clsName);
        }

        if (!OBJECT_CATCH.containsKey(observer.getClass().getName())) {
            OBJECT_CATCH.put(clsName, observer);
        }
    }

    public void removeObserver(Object observer) {
        if (observer == null) {
            throw new IllegalArgumentException("obj is null");
        }
        Class<?> clz = observer.getClass();
        OLSubscribe subscribe = clz.getAnnotation(OLSubscribe.class);
        if (subscribe == null) {
            throw new IllegalArgumentException("class " + clz.getName() + "没有添加OLSubscribe桥接");
        }
        final String nameSpace = subscribe.nameSpace();
        if (TextUtils.isEmpty(nameSpace)) {
            throw new IllegalArgumentException("class " + clz.getName() + "OLSubscribe nameSpace不能为空");
        }
        if (!OBJECT_CATCH.containsKey(observer.getClass().getName())) {
            return;
        }
        OBJECT_CATCH.remove(observer.getClass().getName());
    }

    /**
     * app退出 清除下缓存
     */
    public void clear() {
        CLASS_CATCH.clear();
        OBJECT_CATCH.clear();
    }

    public void onResume() {
    }

    public void onPause() {

    }

    public void onDestroy() {
        OLWebView = null;
    }

    private class MethodInfo {
        private Method method;
        private Object object;
        private JSONObject params;
        private String nameSpace;

        @Override
        public String toString() {
            return "MethodInfo{" +
                    "method=" + method +
                    ", object=" + object +
                    '}';
        }
    }

    public static boolean isMainThread() {
        return Looper.myLooper() == Looper.getMainLooper();
    }
}
