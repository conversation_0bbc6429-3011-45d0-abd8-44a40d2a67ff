package com.layaa.olweb.prefetch;

import com.layaa.libutils.module_log.LogUtils;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/3/10
 * @des 简单实现, 只适合所有key都不同的情况
 **/
class SyncObjectPoolImpl implements ISyncObjectPool {

    private static final String TAG = "SYNC-ObjectPool";

    private final HashMap<String, Object> SyncPools = new HashMap<>();

    /**
     * 清除同步池中数据并notify
     */
    @Override
    public void clear() {
        LogUtils.d(TAG, "clear keys!");
        Set<String> set = SyncPools.keySet();
        if (set != null && !set.isEmpty()) {
            Iterator<String> iterator = set.iterator();
            while (iterator.hasNext()) {
                String key = iterator.next();
                notifyFor<PERSON>ey(key);
            }
        }
        SyncPools.clear();
    }

    /**
     * 添加一个和key对应的同步对象
     *
     * @param key 同步对象的key
     */
    @Override
    public void putKey(String key) {
        SyncPools.put(key, new Object());
    }

    /**
     * 删除和key对应的同步对象并notify
     *
     * @param key 同步对象的key
     * @return true: notify ; false: no obj
     */
    @Override
    public boolean removeKeyAndNotify(String key) {
        if (key == null)
            return false;
        Object syncObj = SyncPools.remove(key);
        LogUtils.d(TAG, "removeKeyAndNotify: " + key + " sync obj: " + syncObj);
        if (syncObj != null) {
            synchronized (syncObj) {
                syncObj.notifyAll();
            }
            return true;
        }
        return false;
    }

    /**
     * 等待和key对应的同步对象notify
     *
     * @param key 同步对象的key
     * @return true: key有对应的同步对象
     * @throws InterruptedException wait被打断将抛出异常
     */
    @Override
    public boolean waitForKey(String key) throws InterruptedException {
        if (key == null) {
            return false;
        }
        Object syncObj = SyncPools.get(key);
        LogUtils.d(TAG, "waitForKey: " + key + " sync obj: " + syncObj);
        if (syncObj == null) {
            return false;
        }
        synchronized (syncObj) {
            syncObj.wait();
        }
        return true;
    }

    /**
     * notify 和key对应的同步对象
     *
     * @param key 同步对象的key
     */
    @Override
    public void notifyForKey(String key) {
        if (key == null) {
            return;
        }
        Object syncObj = SyncPools.get(key);
        LogUtils.d(TAG, "notifyForKey: " + key + " sync obj: " + syncObj);
        if (syncObj == null) {
            return;
        }
        synchronized (syncObj) {
            syncObj.notifyAll();
        }
    }
}
