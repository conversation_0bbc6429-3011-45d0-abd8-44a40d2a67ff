package com.layaa.olweb.listener;

import android.webkit.WebView;

import com.layaa.olweb.entity.SetUIBtnParams;
import com.layaa.olweb.entity.SetUIParams;
import com.layaa.olweb.entity.StatusParams;
import com.layaa.olweb.entity.UIParams;

/**
 * <AUTHOR>
 * @date 2021/4/10
 * @des
 **/
public interface OLUICallback {
    /**
     * 回调 webview title
     *
     * @param view  webview
     * @param title title
     */
    void onReceivedTitle(WebView view, String title);

    void uiShowHeaderBar(boolean show);

    void changeTitleBtn(StatusParams params);

    void changeTitleBtn(UIParams params);

    void uiSetUI(SetUIParams uiParams);

    void uiSetUIButton(SetUIBtnParams params);
    void setUiHeight(int height);
}
