package com.layaa.olweb.router;

import android.net.Uri;
import android.text.TextUtils;

import com.layaa.olweb.OLKit;
import com.layaa.olweb.constant.OLConstants;
import com.layaa.olweb.offline.OfflineUtils;
import com.layaa.olweb.utils.OLFileHelper;

import java.io.File;
import java.util.List;
import java.util.Set;

import androidx.annotation.Nullable;

/**
 * <AUTHOR>
 * @date 2021/3/9
 * @des
 **/
public class OLPackageRouter {
    private static final String TAG = OLPackageRouter.class.getSimpleName();

    private static final String LOCAL_SCHEME = "local://";
    private static final String TEST_URL = OfflineUtils.ALPHA_FILE;

    /**
     * 是否是测试环境的url
     *
     * @param url
     * @return
     */
    public static boolean isTestUrl(String url) {
        Uri uri = Uri.parse(url);
        String host = uri.getHost();
        return host != null && host.startsWith(TEST_URL);
    }

    public static boolean isLocalUrl(String url) {
        return url.startsWith(LOCAL_SCHEME);
    }

    public static String getAbsoluteUrl(String url) {
        File root = OLFileHelper.getOLHomeDir();
        if (root == null) {
            return null;
        }
        File parentFile = root.getParentFile();
        if (parentFile == null) {
            return null;
        }
        String bizPath = parentFile.getParent();
        if (TextUtils.isEmpty(bizPath)) {
            return null;
        }
        url = getUrlPath(url);
        File file = new File(bizPath, url);
        return file.getAbsolutePath();
    }

    public static String getLocalUrl(String absoluteUrl) {
        if (absoluteUrl == null || !absoluteUrl.startsWith("/")) {
            return absoluteUrl;
        }
        File root = OLFileHelper.getOLHomeDir();
        if (root == null) {
            return absoluteUrl;
        }
        File parentFile = root.getParentFile();
        if (parentFile == null) {
            return absoluteUrl;
        }
        String bizPath = parentFile.getParent();
        if (TextUtils.isEmpty(bizPath)) {
            return absoluteUrl;
        }
        if (!absoluteUrl.startsWith(bizPath)) {
            return absoluteUrl;
        }
        String url = absoluteUrl.replace(bizPath, "").substring(1);
        return LOCAL_SCHEME + url;
    }

    /**
     * check whether the url is a offline url
     *
     * @param url
     * @return
     */
    public static String getBidFromUrl(String url) {
//        long start = System.currentTimeMillis();
        if (TextUtils.isEmpty(url)) {
            return "";
        }
        try {
            Uri uri = Uri.parse(url);
            String bid = uri.getQueryParameter("_bid");
//            LogUtil.d(TAG, "tang-----isOfflinePage " + url + "   time " + (System.currentTimeMillis() - start) + "  scheme " + uri.getScheme());
            return bid;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return "";
    }

    public static boolean isOLUrl(String url) {
        String bid = getBidFromUrl(url);
        return !TextUtils.isEmpty(bid);
    }

    /**
     * 根据url 得到对应的带有 offline 参数的 url
     * eg url: http://web.biz.com/decoration/profile?product_id=10080&_bid=78
     * 对应得离线包目录则是 /Offline/78/web.biz.com/decoration/profile.html
     *
     * @param url_str 如果包含  _bid 参数则代表离线包
     * @return
     */
    public static String getOfflineUrlFromUrl(String url_str) {
        if (TextUtils.isEmpty(url_str)) {
            return "";
        }
        Uri uri = Uri.parse(url_str);
        File offline_file = getOfflineFileFromUrl(url_str);
//        LogUtils.d(TAG, "tang-------生成的离线文件路径是 " + offline_file.getAbsolutePath() + "   exists? " + offline_file.exists());
        if (offline_file == null) {
            return "";
        }
        String port = uri.getPort() == -1 ? "" : ":" + String.valueOf(uri.getPort());
        Uri.Builder builder = new Uri.Builder();
        builder.scheme(uri.getScheme())
                .authority(uri.getHost() + port)
                .path(uri.getPath());
        //增加参数
        Set<String> params = uri.getQueryParameterNames();
        boolean has_offline = false;//是否已经带有 _offline参数
        //拼接参数
        for (String key : params) {
            if (TextUtils.equals(key, "_offline")) {
                has_offline = true;
            }
            builder.appendQueryParameter(key, uri.getQueryParameter(key));
        }
        //增加 offline参数
        if (!has_offline) {
            builder.appendQueryParameter("_offline", "1");
        }
        //6.8 改进 将链接中 ＃号后面的参数也应该添加进去
        String fragment = uri.getEncodedFragment();
        if (!TextUtils.isEmpty(fragment)) {
            builder.encodedFragment(fragment);
        }
        return builder.build().toString();
    }

    /**
     * 从图片缓存路径寻找图片
     *
     * @param imageUrl
     * @return 返回缓存图片应该缓存的路径，图片不一定存在
     */
    public static File getWebImageFromCache(String imageUrl) {
        return OLKit.getImageLoader().getCacheImage(imageUrl);
    }

    /**
     * 根据图片url寻找图片缓存路径
     * 1. 先从离线包寻找；
     * 2. 如果离线包没有，则从缓存路径寻找
     * 3. 如果缓存文件没有，则返回缓存文件的路径
     *
     * @param imageUrl
     * @return
     */
    public static File getWebImageCachePath(String imageUrl) {
        File file = getOfflineFileFromUrl(imageUrl);
        if (file != null) {
            return file;
        }
        file = getWebImageFromCache(imageUrl);
        return file;
    }

    /**
     * 根据url从离线包中找到文件
     *
     * @param url_str
     * @return
     */
    @Nullable
    public static File getOfflineFileFromUrl(String url_str) {
        return getOfflineFileFromUrl(url_str, 0);
    }

    /**
     * @param url_str
     * @param modifyOffset
     * @return
     */
    @Nullable
    public static File getOfflineFileFromUrl(String url_str, long modifyOffset) {
        if (TextUtils.isEmpty(url_str)) {
            return null;
        }
        Uri uri = Uri.parse(url_str);
        String _bid = getBidFromUrl(url_str);
        if (TextUtils.isEmpty(_bid)) {
            return null;
        }
        List<String> downloadingBids = OfflineUtils.getDownloadingBids();
        if (downloadingBids != null && downloadingBids.contains(_bid)) {
            return null;
        }
        downloadingBids = OfflineUtils.getUnzipingBids();
        if (downloadingBids != null && downloadingBids.contains(_bid)) {
            return null;
        }
        File dir = OfflineUtils.getPackageDir(_bid);
        if (dir == null) {
            return null;
        }
        if (!dir.exists()) {
            dir.mkdirs();
        }
        if (modifyOffset > 0) {
            if (System.currentTimeMillis() - dir.lastModified() < modifyOffset) {
                return null;
            }
        }
        String host = uri.getHost();
        String path = uri.getPath();
        if (!path.startsWith("/")) {
            path = "/" + path;
        }
        File offline_file = new File(dir, host + path);
        if (!offline_file.exists() || offline_file.length() == 0) {
            return null;
        }
        return offline_file;
    }

    public static String getUrlFromOfflineFile(String path) {
        if (path.startsWith(OLConstants.LOCAL_FILE_SIGN)) {
            path = path.substring(OLConstants.LOCAL_FILE_SIGN.length());
        }
        File file = new File(path);
        if (!file.exists()) {
            return null;
        }
        File root = OLFileHelper.getOfflineHomeDir();
        if (root == null) {
            return null;
        }
        if (!isParent(file, root)) {
            return null;
        }
        String rootPath = root.getAbsolutePath();
        String rp = path.substring(rootPath.length());
        if (rp.startsWith("/")) {
            rp = rp.substring(1);
        }
        if (rp.isEmpty()) {
            return null;
        }
        int index = rp.indexOf("/");
        if (index <= 0 || index >= rp.length()) {
            return null;
        }
        String bid = rp.substring(0, index);
        rp = rp.substring(index + 1);   //host + / + path
        if (rp.contains("?")) {
            bid = "&_bid=" + bid;
        } else {
            bid = "?_bid=" + bid;
        }
        return rp + bid;
    }

    public static boolean isParent(File target, File parent) {
        return target.getAbsolutePath().startsWith(parent.getAbsolutePath());
    }

    public static String getUrlPath(String url) {
        Uri uri = Uri.parse(url);
        String host = uri.getHost();
        String path = uri.getPath();
        if (!path.startsWith("/")) {
            path = "/" + path;
        }
        return host + path;
    }
}
