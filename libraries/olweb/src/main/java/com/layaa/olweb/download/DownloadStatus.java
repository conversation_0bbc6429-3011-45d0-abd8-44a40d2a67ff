package com.layaa.olweb.download;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

import androidx.annotation.IntDef;

/**
 * <AUTHOR>
 * @date 2021/3/10
 * @des
 **/
@Retention(RetentionPolicy.SOURCE)
@IntDef({DownloadStatus.TYPE_DOWNLOAD_START, DownloadStatus.TYPE_DOWNLOADING, DownloadStatus.TYPE_DOWNLOAD_SUC, DownloadStatus.TYPE_DOWNLOAD_FAIL, DownloadStatus.TYPE_NOT_UPDATE, DownloadStatus.TYPE_HAS_DOWNLOAD})
public @interface DownloadStatus {

    int TYPE_DOWNLOAD_START = 0x1001;
    int TYPE_DOWNLOADING = 0x1002;
    int TYPE_DOWNLOAD_SUC = 0x1003;
    int TYPE_DOWNLOAD_FAIL = 0x1004;
    int TYPE_NOT_UPDATE = 0X1005;
    int TYPE_HAS_DOWNLOAD = 0x1006;
}
