package com.layaa.olweb.download;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

import androidx.annotation.IntDef;

/**
 * <AUTHOR>
 * @date 2021/3/10
 * @des
 **/
@IntDef({UnzipStatus.TYPE_UNZIP_ING, UnzipStatus.TYPE_UNZIP_SUC, UnzipStatus.TYPE_UNZIP_FAIL})
@Retention(RetentionPolicy.SOURCE)
public @interface UnzipStatus {

    int TYPE_UNZIP_ING = 0x2001;
    int TYPE_UNZIP_SUC = 0x2002;
    int TYPE_UNZIP_FAIL = 0x2003;
}
