package com.layaa.olweb.session;

import android.text.TextUtils;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2021/3/10
 * @des
 **/
public class CookieValue {
    public String name;
    public String token;
    public long expire;
    public final long expiredTime = 86400;//Cookie的过期时间为1天

    public CookieValue() {
    }

    public CookieValue(String name, long expire, String token) {
        this.name = name;
        this.expire = expire;
        this.token = token;
    }

    public boolean isValidate() {
        return (!TextUtils.isEmpty(token)) && (expire > System.currentTimeMillis() / 1000);
    }

    public static CookieValue fromJson(JSONObject json) throws JSONException {
        if (json == null || !json.has("token") || !json.has("name")) {
            return null;
        }
        return new CookieValue(json.getString("name"), json.getLong("expire"), json.getString("token"));
    }

    public static JSONObject toJson(CookieValue value) throws JSONException {
        JSONObject json = new JSONObject();
        json.put("name", value.name);
        json.put("expire", value.expire);
        json.put("token", value.token);
        return json;
    }
}
