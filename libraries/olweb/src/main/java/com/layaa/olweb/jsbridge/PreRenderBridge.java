package com.layaa.olweb.jsbridge;

import android.text.TextUtils;

import com.layaa.olweb.annotation.OLJsBridge;
import com.layaa.olweb.annotation.OLSubscribe;
import com.layaa.olweb.annotation.ThreadMode;
import com.layaa.olweb.constant.OLConstants;
import com.layaa.olweb.pre.PreRenderManager;
import com.layaa.olweb.utils.OLHelper;
import com.layaa.olweb.widget.OLWebView;

import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2021/3/13
 * @des
 **/
@OLSubscribe(nameSpace = "view")
public class PreRenderBridge {

    private OLWebView OLWebView;

    public PreRenderBridge(OLWebView OLWebView) {
        this.OLWebView = OLWebView;
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public String prepare(JSONObject params) {

        if (OLWebView == null || OLWebView.isReleased()) {
            return null;
        }
        String url = params.optString("url");
        if (TextUtils.isEmpty(url)) {
            return null;
        }
        String callback = params.optString(OLConstants.callBack);
        PreRenderManager.getInstance().preRenderUrl(OLWebView, url);
        return OLHelper.assembleJsonObject(new String[]{"status"}, new Object[]{0}).toString();
//        MKHelper.insertCallback(mkWebView, callback, );
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void destroy(JSONObject params) {
        String url = params.optString("url");
        if (TextUtils.isEmpty(url)) {
            return;
        }
        PreRenderManager.getInstance().destroyPreRender(url);
    }
}
