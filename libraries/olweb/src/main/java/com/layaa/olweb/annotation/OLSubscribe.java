package com.layaa.olweb.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2021/1/7
 * @des js 桥接 方法注释
 **/
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface OLSubscribe {

    /**
     * @return
     */
    String nameSpace();
}
