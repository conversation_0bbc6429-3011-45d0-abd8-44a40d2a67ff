package com.layaa.olweb.service;

/**
 * Created by wangjianing on 2021/7/27.
 */

import android.annotation.SuppressLint;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.IBinder;

import com.layaa.libutils.module_thread.task.AbsJob;
import com.layaa.libutils.module_thread.task.ThreadPool;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * ol 离线包解压Service
 * Created by tangyuch<PERSON> on 11/24/15.
 */
@SuppressLint("MDLogUse")
public class OLPrepareService extends Service {

    private static final String TAG = "OLPrepareService";
    /**
     * ol 批量检查更新的时间间隔 2小时 OLPrepareService#checkPackageUpdateList()
     */
    private static final String KEY_UPDATE_PACKAGE_LIST = "ol_check_updatelist_time";
    private boolean isRunning = false;
    private static List<Runnable> extraRunnableList = null;

    public static void prepare(@NonNull Context context, List<Runnable> runnableList) {
        OLPrepareService.extraRunnableList = runnableList;
        try {
            Intent service = new Intent("com.layaa.olwebview.prepare_ol");
            service.setPackage(context.getPackageName());
            context.startService(service);
        } catch (Exception ex) {
        }
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        startPrepare();
        return super.onStartCommand(intent, flags, startId);
    }

    private void startPrepare() {
        if (!isRunning) {
            isRunning = true;
            AbsJob job = new AbsJob() {
                @Override
                protected void run() {
                    if (extraRunnableList != null) {
                        for (Runnable r : extraRunnableList) {
                            r.run();
                        }
                        extraRunnableList = null;
                    }
                    onPrepareFinished();
                }
            };
            ThreadPool.submit(job, "startPrepare");
        } else {
            onPrepareFinished();
        }
    }

    private void onPrepareFinished() {
        //        log.i("tang------离线包准备完毕 ");
        isRunning = false;
        stopSelf();
    }
}
