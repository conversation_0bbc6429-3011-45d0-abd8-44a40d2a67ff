package com.layaa.olweb.offline;

import android.os.SystemClock;
import android.text.TextUtils;

import com.layaa.olweb.OLKit;
import com.layaa.olweb.annotation.ZipType;
import com.layaa.olweb.api.OfflineAPI;
import com.layaa.olweb.bsdiff.BsdiffUtil;
import com.layaa.olweb.config.DownloadListener;
import com.layaa.olweb.download.DownloadNotificationCenter;
import com.layaa.olweb.download.DownloadStatus;
import com.layaa.olweb.download.OLDownloadCallback;
import com.layaa.olweb.download.OfflinePackageDownloader;
import com.layaa.olweb.download.PackageDownloadTask;
import com.layaa.olweb.entity.OfflinePackage;
import com.layaa.olweb.entity.PackageEntity;
import com.layaa.olweb.entity.PackageUsage;
import com.layaa.olweb.entity.UpdateResult;
import com.layaa.olweb.listener.BaseCallback;
import com.layaa.olweb.router.OLPackageRouter;
import com.layaa.olweb.server.OLUsageService;
import com.layaa.olweb.utils.OLFileHelper;
import com.layaa.olweb.utils.UnzipUtils;
import com.layaa.libutils.AppDirUtils;
import com.layaa.libutils.FileUtils;
import com.layaa.libutils.module_log.LogUtils;
import com.layaa.libutils.module_thread.task.AbsJob;
import com.layaa.libutils.module_thread.task.ThreadPool;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import androidx.annotation.Nullable;

import static com.layaa.olweb.offline.OfflineUtils.TYPE_ZIP_FILE;

/**
 * <AUTHOR>
 * @date 2021/3/9
 * @des
 **/
public class OLPackageManager {

    private static final String TAG = OLPackageManager.class.getSimpleName();

    /**
     * 如果一个离线包校验了，那2个小时之内不再校验
     */
    public static final long VERIFY_PACKAGE_EXPIRE_TIME = 2 * 60 * 60 * 1000;

    private static OLPackageManager instance;
    /**
     * 预处理的离线包，加入到缓存，避免每次都读取文件
     */
    private HashMap<String, OfflinePackage> packageInfoCache = new HashMap<>();
    private OLUsageService usageService;

    private Map<String, String> downloadList = new ConcurrentHashMap<>();

    private Map<String, Long> downloadTimeMap = new ConcurrentHashMap<>();

    public static OLPackageManager getInstance() {
        if (instance == null) {
            synchronized (OLPackageManager.class) {
                if (instance == null) {
                    instance = new OLPackageManager();
                }
            }
        }
        return instance;
    }

    private OLPackageManager() {
        //db数据库相关
        usageService = new OLUsageService();
    }


    /**
     * 是否需要下载游戏
     *
     * @param bid
     * @return
     */
    public String isGameNeedDownload(String bid) {
        OfflinePackage offlinePackage = getPackageInfo(bid);
        UpdateResult result = null;
        try {
            result = OfflineAPI.getInstance().checkUpdate(bid, "", offlinePackage.getVersion());
        } catch (Exception ex) {
            ex.printStackTrace();
            LogUtils.e(TAG, "tang----检查更新失败");
        }

        if (result == null) {
            //检查更新失败
            //回调通知
            return "";
        }

        LogUtils.d(TAG, "tang------完整包url " + result.getZipUrl() + "  增量包url " + result.getPatchUrl());
        if (result.hasNewVersion()) {
            return "";
        }
        OfflinePackage page = getPackageInfo(bid);
        return page == null ? "" : page.getGameVersion();

    }

    // 判断异步游戏，是否需要下载，异步游戏没有 gameVersion
    public Boolean isAsyncGameNeedDownload(String bid) {
        OfflinePackage offlinePackage = getPackageInfo(bid);
        UpdateResult result = null;
        try {
            result = OfflineAPI.getInstance().checkUpdate(bid, "", offlinePackage.getVersion());
        } catch (Exception ex) {
            ex.printStackTrace();
            LogUtils.e(TAG, "tang----检查更新失败");
        }

        if (result == null) {
            //检查更新失败
            //回调通知
            return true;
        }

        LogUtils.d(TAG, "tang------完整包url " + result.getZipUrl() + "  增量包url " + result.getPatchUrl());
        return result.hasNewVersion();
    }

    /**
     * 离线包是否已经缓存
     *
     * @param bid
     * @return
     */
    public long isCached(String bid) {
        File dir = OfflineUtils.getPackageDir(bid);
        if (!dir.exists()) {
            return -1;
        }
        try {
            PackageEntity configs = OfflineUtils.readPackageConfigs(dir.getAbsolutePath());
            return configs == null ? -1 : configs.getVersion();
        } catch (IOException e) {
            LogUtils.e(e);
        }
        return -1;
    }

    /**
     * 清除所有的离线包缓存
     *
     * @return
     */
    public void clearAllPackageCache(final BaseCallback callback) {

        AbsJob job = new AbsJob() {
            @Override
            protected void run() {
                boolean result = OfflineUtils.clearAllPackageCache();
                usageService.clearAllPackageUsage();
                if (callback != null) {
                    if (result) {
                        callback.onSuccess(null);
                    } else {
                        callback.onError("清除失败");
                    }
                }
            }
        };
        ThreadPool.submit(job, "clearAllPackageCache");
    }

    /**
     * 清除某一个离线包的缓存
     *
     * @param bid
     * @return
     */
    public boolean removeCachePackage(final String bid) {
        if (TextUtils.isEmpty(bid)) {
            return false;
        }
        File packageDir = OfflineUtils.getPackageDir(bid);
        if (!packageDir.exists()) {
            return false;
        }

        AbsJob job = new AbsJob() {
            @Override
            protected void run() {
                OfflineUtils.removeCachePackage(bid);
                usageService.deletePackageUsage(bid);
            }
        };
        ThreadPool.submit(job, "removeCachePackage");
        return true;
    }

    /**
     * 强制更新离线包 此处下载的是完整的离线包
     *
     * @param
     * @return
     */
    public void forceUpdatePackage(final String bid, final String url, final BaseCallback callback) {
        if (TextUtils.isEmpty(bid) || TextUtils.isEmpty(url)) {
            return;
        }
        AbsJob job = new AbsJob() {
            @Override
            protected void run() {
                OfflinePackage offlinePackage = getPackageInfo(bid);
                //TODO 日志去掉先
                /*MKPackageLog packageLog = new MKPackageLog();
                packageLog.setBid(bid);
                packageLog.onStart();
                packageLog.setLocalVersion(offlinePackage.getVersion());//本地版本号
                //检查更新时间
                packageLog.setCheckUpdateTime(-1);//-1代表强制更新*/

                /*
                 * 由于下载log需要根据版本号生成key，但是强制更新时并未知道最新版本号，指定一个特殊值
                 */
                int newVersion = 10086;
                // 下载的是否是增量包
                /*packageLog.setIsPatch(false);
                packageLog.setUrl(url);*/
                String logKey = getDownloadLogKey(bid, newVersion, url);
                /*packageLog.setLogID(logKey);
                MKLogManager.getInstance().recordTempLog(logKey, packageLog);*/
                //强制更新，此时下载的是完整的zip包

                downloadPackage(offlinePackage.bid, new UpdateResult(), url, PackageDownloadTask.DOWNLOAD_TYPE_ZIP, "", 0, null, new OLDownloadCallback() {

                    @Override
                    public void onDownloadSuccess(String bid, String gameVersion) {
                        if (callback != null) {
                            callback.onSuccess(null);
                        }
                    }

                    @Override
                    public void onDownloadProgress(String bid, int percent, long totalSize) {

                    }

                    @Override
                    public void onDownloadFail(String bid, String failMsg) {
                        if (callback != null) {
                            callback.onSuccess(null);
                        }
                    }

                    @Override
                    public void onDownloadStart(String bid) {

                    }
                });
               /* boolean result = downloadPackage(bid, url, PackageDownloadTask.DOWNLOAD_TYPE_ZIP,"" , 0);
                if (callback != null) {
                    if (!result) {
                        callback.onError(null);
                    } else {
                        callback.onSuccess(null);
                    }
                }*/
            }
        };
        ThreadPool.submit(job, "forceUpdatePackage");
    }

    public long getVersion(String bid) {
        if (TextUtils.isEmpty(bid)) {
            return 0;
        }
        OfflinePackage offlinePackage = getPackageInfo(bid);
        return offlinePackage.configs != null ? offlinePackage.getVersion() : 0;
    }

    public void checkUpdate(final String bid, @Nullable final String currentUrl, final Map<String, String> params, OLDownloadCallback downloadListener) {
        // bid=0则代表没有离线包，但是仍然想使用mk打开web页面
        if (TextUtils.equals("0", bid)) {
            return;
        }
        AbsJob absJob = new AbsJob() {
            @Override
            protected void run() {
                OfflinePackage offlinePackage = getPackageInfo(bid);
                UpdateResult result = null;
                Exception exception = null;
                try {
                    result = OfflineAPI.getInstance().checkUpdate(bid, currentUrl, offlinePackage.getVersion());
                } catch (Exception ex) {
                    //todo 统计等统计lib拆分后 统一替换
//                    VogaStatistics.Companion.reportNetError(OfflineAPI.GAME_UPDATE, ex);
                    ex.printStackTrace();
                    exception = ex;
                    LogUtils.e(TAG, "tang----检查更新失败");
                }

                if (result == null) {
                    //检查更新失败
                    //回调通知
                    DownloadNotificationCenter.getInstance().sendNotification(DownloadStatus.TYPE_DOWNLOAD_FAIL, bid);
                    return;
                }

                LogUtils.d(TAG, "tang------完整包url " + result.getZipUrl() + "  增量包url " + result.getPatchUrl());
                if (!result.hasNewVersion()) {
                    DownloadNotificationCenter.getInstance().sendNotification(DownloadStatus.TYPE_NOT_UPDATE, bid, offlinePackage.getGameVersion(), params);
                    //没有最新包，则更新检查更新的最后时间
                    refreshCheckUpdateTime(bid);
                    //没有最新包
                    LogUtils.d(TAG, "tang-------没有最新包，不需要更新");
                    return;
                }
                // 先更新内存中的离线包 最新检查时间
                offlinePackage.refreshLastCheckUpdateTime();

                int downloadType = getDownloadType(bid, PackageDownloadTask.DOWNLOAD_TYPE_PATCH, offlinePackage.getVersion());
                String url = result.getZipUrl();
                if (downloadType == PackageDownloadTask.DOWNLOAD_TYPE_PATCH) {
                    String patch_url = result.getPatchUrl();
                    if (!TextUtils.isEmpty(patch_url)) {
                        url = patch_url;
                    } else {
                        downloadType = PackageDownloadTask.DOWNLOAD_TYPE_ZIP;
                    }
                }
                // 下载的是否是增量包
               /* packageLog.setIsPatch(downloadType == PackageDownloadTask.DOWNLOAD_TYPE_PATCH);
                packageLog.setUrl(url);*/
                /*String logKey = getDownloadLogKey(bid, result.newVersion, url);
                packageLog.setLogID(logKey);
                MKLogManager.getInstance().recordTempLog(logKey, packageLog);*/

                LogUtils.d(TAG, "tang------下载类型是 " + downloadType + "   下载url是 " + url);
                downloadPackage(bid, result, url, downloadType, result.gameVersion, offlinePackage.getVersion(), params, downloadListener);
                //下载完成后更新检查时间，避免下载的新离线包配置中没有上次更新时间，导致刚下载完又去检查更新
                /*if (downloadResult) {
                    if (callback != null) {
                        callback.onSuccess(result.sourceJson);
                    }
                } else {
                    if (callback != null) {
                        callback.onError("离线包下载失败");
                    }
                }*/
            }
        };
        LogUtils.d(TAG, "tang-------检查更新 " + bid);
        ThreadPool.submit(absJob, "checkUpdate");
    }

    /**
     * 检查更新，如果有更新，则直接下载
     *
     * @param bid
     * @param currentUrl 当前打开的页面的url
     * @param callback
     */
    public void checkUpdate(final String bid, @Nullable final String currentUrl, final BaseCallback callback) {
        // bid=0则代表没有离线包，但是仍然想使用mk打开web页面
        if (TextUtils.equals("0", bid)) {
            return;
        }

        checkUpdate(bid, currentUrl, new HashMap<String, String>(), new OLDownloadCallback() {
            @Override
            public void onDownloadSuccess(String bid, String gameVersion) {
                if (callback != null) {
                    callback.onSuccess(null);
                }
            }

            @Override
            public void onDownloadProgress(String bid, int percent, long totalSize) {

            }

            @Override
            public void onDownloadFail(String bid, String failMsg) {
                if (callback != null) {
                    callback.onError("update error");
                }
            }

            @Override
            public void onDownloadStart(String bid) {

            }
        });
    }

    /**
     * 批量检查更新并且下载
     *
     * @param list
     */
    public void downloadByUpdateResult(List<UpdateResult> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        for (UpdateResult result : list) {
            if (result == null || TextUtils.isEmpty(result.bid)) {
                continue;
            }
            String bid = result.bid;
            LogUtils.d(TAG, "tang-------开始批量下载 " + bid);
            //LOG 统计
            /*MKPackageLog packageLog = new MKPackageLog();
            packageLog.setBid(bid);
            packageLog.onStart();
            packageLog.setLocalVersion(result.localVersion);//本地版本号
            packageLog.setCheckUpdateTime(0);*/

            int downloadType = getDownloadType(bid, PackageDownloadTask.DOWNLOAD_TYPE_PATCH, result.localVersion);
            String url = result.getZipUrl();
            if (downloadType == PackageDownloadTask.DOWNLOAD_TYPE_PATCH) {
                String patch_url = result.getPatchUrl();
                if (!TextUtils.isEmpty(patch_url)) {
                    url = patch_url;
                } else {
                    downloadType = PackageDownloadTask.DOWNLOAD_TYPE_ZIP;
                }
            }

            downloadPackage(bid, result, url, downloadType, result.gameVersion,
                    result.localVersion, null, new OLDownloadCallback() {

                        @Override
                        public void onDownloadSuccess(String bid, String gameVersion) {
                            refreshCheckUpdateTime(bid);
                        }

                        @Override
                        public void onDownloadProgress(String bid, int percent, long totalSize) {

                        }

                        @Override
                        public void onDownloadFail(String bid, String failMsg) {
                        }

                        @Override
                        public void onDownloadStart(String bid) {

                        }
                    });
        }
    }

    private static String getDownloadLogKey(String bid, long version, String url) {
        if (TextUtils.isEmpty(url)) {
            url = "";
        }
        return bid + "_" + version + "_" + url.hashCode();
    }

    /**
     * 获得正确的下载方式
     *
     * @param bid          业务bid
     * @param downloadType 预备要下载的离线包类型 {@link PackageDownloadTask#DOWNLOAD_TYPE_PATCH} 和 {@link PackageDownloadTask#DOWNLOAD_TYPE_ZIP}
     * @param localVersion 本地的离线包版本号:为了确保增量更新成功，需要将备份的离线包zip包按照版本号来存放
     * @return
     */
    private int getDownloadType(String bid, int downloadType, long localVersion) {
        if (downloadType == PackageDownloadTask.DOWNLOAD_TYPE_PATCH) {
            //如果手机中没有备份的ZIP包，则不下载增量包，没意义 = =
            try {
                if (!OfflineUtils.hasBackupPackage(bid, localVersion)) {
                    downloadType = PackageDownloadTask.DOWNLOAD_TYPE_ZIP;
                }
            } catch (IOException e) {
                LogUtils.e(e);
                downloadType = PackageDownloadTask.DOWNLOAD_TYPE_ZIP;
            }
        }
        return downloadType;
    }

    private void downloadPackage(final String bid, UpdateResult result, String url, int downloadType, String gameVersion, long localVersion, Map<String, String> params, OLDownloadCallback listener) {
        if (TextUtils.isEmpty(bid) || TextUtils.isEmpty(url) || result == null) {
            LogUtils.e(TAG, "tang-----下载离线包失败，bid为空");
            return;
        }

        final String gameId = result.dataId;

        if (!OfflinePackageDownloader.getInstance().recordDownloadTask(url)) {
//            sendToastMessage("下载任务已经进行中...");
            DownloadNotificationCenter.getInstance().sendNotification(DownloadStatus.TYPE_HAS_DOWNLOAD, bid, params);
            return;
        }
        DownloadNotificationCenter.getInstance().sendNotification(DownloadStatus.TYPE_DOWNLOAD_START, bid, params);

        final int zipProgress = copeZipByAsset(result, params);
        if (zipProgress == 100) {
            //本地解压成功 返回
            OfflinePackageDownloader.getInstance().unRecordDownloadTask(url);
            return;
        }
        try {
            if (TextUtils.isEmpty(url)) {
                throw new Exception("离线包下载失败，url为空");
            }
            LogUtils.i(TAG, "start download  ");
            boolean isTestUrl = OLPackageRouter.isTestUrl(url);
            String alphaPre = isTestUrl ? OfflineUtils.ALPHA_FILE : "";
            final String type = url.endsWith(OfflineUtils.TYPE_7Z_FILE) ? OfflineUtils.TYPE_7Z_FILE : TYPE_ZIP_FILE;

            File tmp = null;
            if (downloadType == PackageDownloadTask.DOWNLOAD_TYPE_PATCH) {
                tmp = OfflineUtils.getOfflinePackageZipFile(alphaPre + bid + "_patch", type);
            } else {
                tmp = OfflineUtils.getOfflinePackageZipFile(alphaPre + bid, type);
            }
            final File downloadFile = tmp;
//            LogUtil.d(TAG, "tang-------下载文件保存路径是 " + downloadFile.getAbsolutePath());

            long start = System.currentTimeMillis();
            try {
                downloadFile.delete();
//                downloadFile.createNewFile();

                try {
                    if (!TextUtils.isEmpty(bid)) {
                        if (downloadTimeMap.containsKey(bid)) {
                            downloadTimeMap.remove(bid);
                        }
                        downloadTimeMap.put(bid, SystemClock.uptimeMillis());
//                        ThirdStatistics.StatisticsAgent_basic_click_event("start_download", "-1", TextUtils.isEmpty(gameId) ? bid : gameId);
                    }
                } catch (Exception ignore) {

                }
                OLKit.getDownloadServer().startDownload(url, downloadFile.getParent(), downloadFile.getName(), new DownloadListener() {
                    @Override
                    public void onDownloadSuccess(String url, String savePath, long totalSize) {
                        try {
                            if (!TextUtils.isEmpty(bid)) {
                                if (downloadTimeMap.containsKey(bid)) {
                                    Long time = downloadTimeMap.remove(bid);
                                    if (time != null) {
                                        /*ThirdStatistics.StatisticsAgent_basic_time_event("end_download_suc", "", TextUtils.isEmpty(gameId) ? bid : gameId, String.valueOf(SystemClock.uptimeMillis() - time)
                                                , ExtraHelper.getInstance().getSource());*/
                                    }
                                }

                            }
                        } catch (Exception ignore) {

                        }
                        DownloadNotificationCenter.getInstance().sendNotification(DownloadStatus.TYPE_DOWNLOADING, bid, 90, totalSize);
                       /* if (listener != null) {
                            listener.onDownloadProgress(bid, 90, totalSize);
                        }*/
                        try {
                            if (downloadType == PackageDownloadTask.DOWNLOAD_TYPE_PATCH) {
                                File finalZipFile = OfflineUtils.getOfflinePackageZipFile(alphaPre + bid, type);
                                finalZipFile.delete();
                                finalZipFile.createNewFile();

                                /* 6.6.1 FixBug：BsDiff加载NDK的so包会报错，java.lang.UnsatisfiedLinkError:*/
                                boolean patchResult = false;
                                try {
                                    patchResult = handleDownloadPatchFile(downloadFile, finalZipFile, alphaPre + bid, localVersion);
                                } catch (Throwable ex) {
                                    /*VogaStatistics.with()
                                            .class1("apply_patch_error")
                                            .class2(ex.getMessage())
                                            .send();*/
                                    LogUtils.e(ex);
                                }
                                //增量包合成完成
                                if (!patchResult || !finalZipFile.exists() || finalZipFile.length() <= 0) {
                                    finalZipFile.delete();

                                    throw new Exception("增量包合成失败" + downloadFile.getAbsolutePath());
                                }
                                //删除下载完成的patch包
                                downloadFile.delete();
                                LogUtils.d(TAG, "tang------增量包合成完毕 " + finalZipFile);
                                DownloadNotificationCenter.getInstance().sendNotification(DownloadStatus.TYPE_DOWNLOADING, bid, 95, totalSize);
                                /*if (listener != null) {
                                    listener.onDownloadProgress(bid, 95, totalSize);
                                }*/
                            }
                            int result = UnzipUtils.unzipAndVerifyDownloadedPackage(bid, type, isTestUrl);
                            if (result != 1) {
                                /*
                                    发现更改了备份包之后，仍然可以合成增量包，但是会解压失败，这样导致一直无法下载新包
                                    于是在解压失败且下载类型是增量包时，删除备份包
                                */
                                if (downloadType == PackageDownloadTask.DOWNLOAD_TYPE_PATCH) {
                                    File backupFile = OfflineUtils.getBackupPackage(alphaPre + bid, localVersion, type);
                                    if (backupFile.exists()) {
                                        backupFile.delete();
                                        LogUtils.d(TAG, "tang-------增量合成的离线包解压失败，删除备份包 " + backupFile.getAbsolutePath());
                                    }
                                }
                            }
                            if (result == -1) {//解压失败

                            } else if (result == -2) {//校验失败

                            } else {
                                //解压且校验成功
                                //如果离线包下载且解压成功，就纪录校验时间以及更新缓存的离线包信息
                                resetRefreshPreProcessPackage(bid);
                                /*if (listener!=null){

                                    listener.onDownloadSuccess(bid, page.getGameVersion());
                                }*/

                                OfflinePackage page = getPackageInfo(bid);
                                DownloadNotificationCenter.getInstance().sendNotification(DownloadStatus.TYPE_DOWNLOAD_SUC, bid, 100, totalSize, page.getGameVersion(), params);
                            }
                        } catch (Exception e) {
                            LogUtils.i(TAG, e.getMessage());
                            /*if (listener!=null){
                                listener.onDownloadFail(bid,e.getMessage());
                            }*/
                            DownloadNotificationCenter.getInstance().sendNotification(DownloadStatus.TYPE_DOWNLOAD_FAIL, bid, -1);
                            try {
                                String errorInfo = getErrorInfo(e);
                                if (!TextUtils.isEmpty(bid)) {
                                    if (downloadTimeMap.containsKey(bid)) {
                                        Long time = downloadTimeMap.remove(bid);
                                        if (time != null) {
//                                            ThirdStatistics.StatisticsAgent_basic_time_event("end_download_fail", errorInfo, TextUtils.isEmpty(gameId) ? bid : gameId, String.valueOf(SystemClock.uptimeMillis() - time));
                                        }
                                    }

                                }
                            } catch (Exception ignore) {

                            }
                        } finally {
                            LogUtils.d(TAG, "tang-------取消下载任务纪录");
                            OfflinePackageDownloader.getInstance().unRecordDownloadTask(url);
                        }

                    }

                    @Override
                    public void onDownloadProgress(String url, int percent, long total, long progress) {
                        /*if (listener != null) {
                            listener.onDownloadProgress(bid, (int) (percent * 0.9f), total);
                        }*/
                        DownloadNotificationCenter.getInstance().sendNotification(DownloadStatus.TYPE_DOWNLOADING, bid, (int) (zipProgress + (90 - zipProgress) / 90f * (percent * 0.9f)), total);
                    }

                    @Override
                    public void onDownloadFail(String url) {

                        try {
                            downloadTimeMap.remove(gameId);
                        } catch (Exception ignore) {

                        }
                       /* if (listener != null) {
                            listener.onDownloadFail(bid,"下载失败");
                        }*/
                        DownloadNotificationCenter.getInstance().sendNotification(DownloadStatus.TYPE_DOWNLOAD_FAIL, bid, -1);
                        OfflinePackageDownloader.getInstance().unRecordDownloadTask(url);
                    }

                    @Override
                    public void onDownloadFail(Exception e) {
                        try {
                            String errorInfo = getErrorInfo(e);
                            if (!TextUtils.isEmpty(bid)) {
                                if (downloadTimeMap.containsKey(bid)) {
                                    Long time = downloadTimeMap.remove(bid);
                                    /*if (time != null) {
                                        DownloadConnection.Info info = DownloadConnection.createInfo(gameId, bid, url);
                                        String infoString = GsonUtils.toJson(info);
                                        errorInfo = errorInfo + "#" + infoString;
                                        ThirdStatistics.StatisticsAgent_basic_time_event("end_download_fail", errorInfo, TextUtils.isEmpty(gameId) ? bid : gameId, String.valueOf(SystemClock.uptimeMillis() - time));
                                        LogUtils.w("download error: " + e.getMessage());
                                        LogUtils.w("download error: " + infoString);
                                    }*/
                                }

                            }
                        } catch (Exception ignore) {

                        }
                    }
                });
            } catch (Exception ex) {
                ex.printStackTrace();
                downloadFile.delete();
                throw new Exception("离线包下载失败" + downloadFile.getAbsolutePath());
            }
        } catch (Exception ex) {
            DownloadNotificationCenter.getInstance().sendNotification(DownloadStatus.TYPE_DOWNLOAD_FAIL, bid, -1);
            ex.printStackTrace();
            try {
                String errorInfo = getErrorInfo(ex);
                if (!TextUtils.isEmpty(bid)) {
                    if (downloadTimeMap.containsKey(bid)) {
                        Long time = downloadTimeMap.remove(bid);
                        if (time != null) {
//                            ThirdStatistics.StatisticsAgent_basic_time_event("end_download_fail", errorInfo, TextUtils.isEmpty(gameId) ? bid : gameId, String.valueOf(SystemClock.uptimeMillis() - time));
                        }
                    }

                }
            } catch (Exception ignore) {

            }
        }
    }

    /**
     * asset文件下文件解压
     * 为防止中途解压失败，触发下载，进度条回退到0  返回一个进度，让下载那在这个进度后跟着增加
     *
     * @param result 更新结果
     * @return 进度
     */
    private int copeZipByAsset(UpdateResult result, Map<String, String> params) {
        int progress = 0;
        try {
            long totalSize = 0L;
            if (result == null || TextUtils.isEmpty(result.bid)) {
                return 0;
            }
            String bid = result.bid;
            String assetPath = bid + "_" + result.newVersion + ".zip";
            File file = new File(AppDirUtils.getCatchImage(), assetPath + "_temp.zip");
            if (file.exists()) {
                FileUtils.deleteAllFilesSafely(file);
            }
            boolean copyResult = FileUtils.copyFileFromAssets(assetPath, file.getPath());

            if (!copyResult) {
                //asset 解压失败 直接返回
                return progress;
            }
            progress = 10;
            //asset文件下有对应zip包，直接解压并返回
            totalSize = file.length();
            //发送10%进度条
            DownloadNotificationCenter.getInstance().sendNotification(DownloadStatus.TYPE_DOWNLOADING, bid, progress, file.length(), params);
            progress = unzipOfflinePackageHasProgress(bid, file.getAbsolutePath(), TYPE_ZIP_FILE, false, totalSize);

            if (progress >= 90) {
                progress = 100;
                resetRefreshPreProcessPackage(bid);
                OfflinePackage page = getPackageInfo(bid);
                DownloadNotificationCenter.getInstance().sendNotification(DownloadStatus.TYPE_DOWNLOAD_SUC, bid, 100, totalSize, page.getGameVersion());
            }
        } catch (Exception e) {
            LogUtils.e(e);
            return progress;
        }
        return progress;
    }

    private int unzipOfflinePackageHasProgress(String bid, String filePath, @ZipType String type, boolean alpha, long totalSize) throws Exception {
        int progress = 10;
        long start = System.currentTimeMillis();
        String alphaPre = alpha ? OfflineUtils.ALPHA_FILE : "";
        File offlineFile = new File(filePath);
        String tempUnZipName = OLFileHelper.OL_UNZIP_PREFIX + alphaPre + bid;
        File tempUnZipDir = OfflineUtils.getTempUnZipPackageFile(tempUnZipName);
        if (!tempUnZipDir.exists()) {
            tempUnZipDir.mkdirs();
        }
        //发送20%
        progress = 20;
        DownloadNotificationCenter.getInstance().sendNotification(DownloadStatus.TYPE_DOWNLOADING, bid, progress, totalSize);
        String tempUnZipPath = tempUnZipDir.getAbsolutePath();
//        LogUtil.d(TAG, "tang------解压路径是 " + tempUnZipPath + "    zip文件是 " + downloadZipFile);
        long unzipStart = System.currentTimeMillis();
        boolean unzip = UnzipUtils.unzip("", offlineFile, tempUnZipPath);
        //35%
        progress = 35;
        DownloadNotificationCenter.getInstance().sendNotification(DownloadStatus.TYPE_DOWNLOADING, bid, progress, totalSize);
//        boolean unzip = IOUtils.unZipFile(downloadZipFile, tempUnZipPath);
        if (!unzip) {
            FileUtils.deleteAllFilesSafely(tempUnZipDir);
            offlineFile.delete();
            return progress;
        }
        LogUtils.d(TAG, "tang-------解压离线包耗时 " + (System.currentTimeMillis() - unzipStart) + "   解压是否成功 " + unzip + "    " + tempUnZipDir.exists());
        //解压成功后，
        OfflinePackage offlinePackage = new OfflinePackage(bid);
        offlinePackage.init(tempUnZipPath);
        long newVersion = offlinePackage.getVersion();
        //50%
        progress = 50;
        DownloadNotificationCenter.getInstance().sendNotification(DownloadStatus.TYPE_DOWNLOADING, bid, progress, totalSize);
        if (offlinePackage.configs == null) {
            FileUtils.deleteAllFilesSafely(tempUnZipDir);
            offlineFile.delete();
            return progress;
        }
        boolean verify = UnzipUtils.verifyPackage(offlinePackage);
        progress = 85;
        DownloadNotificationCenter.getInstance().sendNotification(DownloadStatus.TYPE_DOWNLOADING, bid, progress, totalSize);
        if (verify) {//校验通过
            //删除旧的离线包，重命名新的离线包
            File oldPackageDir = OfflineUtils.getPackageDir(alphaPre + bid);
            FileUtils.deleteAllFilesSafely(oldPackageDir);
            LogUtils.d(TAG, "tang-------删除旧包 " + oldPackageDir.getAbsolutePath());
            //删除旧的备份包，重命名新的备份包
            tempUnZipDir.renameTo(oldPackageDir);
            LogUtils.d(TAG, "tang-------重命名新的离线包 " + tempUnZipDir.getAbsolutePath() + "  ----> " + oldPackageDir.getAbsolutePath());

            //将下载的离线包进行备份
            File backupFile = OfflineUtils.getBackupPackage(alphaPre + bid, newVersion, type);
            backupFile.delete();

            FileUtils.copyFile(offlineFile, backupFile);

            OfflineUtils.deleteBackupFile(alphaPre + bid, newVersion);
//            LogUtil.d(TAG, "tang------复制下载文件到备份目录");
//            tempPackageZip.renameTo(backupFile);
            offlineFile.delete();
//            LogUtil.d(TAG, "tang------删除下载文件");
//            LogUtil.d(TAG, "tang-------备份下载的离线包 " + backupFile.getAbsolutePath());
        } else {
            LogUtils.d(TAG, "tang------离线包校验失败，删除临时离线包和临时解压包");
//            long start2 = System.currentTimeMillis();
            FileUtils.deleteAllFilesSafely(tempUnZipDir);
//            LogUtil.d(TAG, "tang-----删除解压包耗时 time " + (System.currentTimeMillis() - start2));
            offlineFile.delete();

            return progress;
        }
        LogUtils.d(TAG, "tang---------解压并且校验离线包耗时 " + (System.currentTimeMillis() - start));
        //90%
        progress = 90;
        DownloadNotificationCenter.getInstance().sendNotification(DownloadStatus.TYPE_DOWNLOADING, bid, progress, totalSize);
        return progress;
    }

    private String getErrorInfo(Exception e) {
        if (e == null || e.getMessage() == null) {
            return "exception is null";
        }
        StringBuilder stringBuilder = new StringBuilder(e.getMessage());
        StackTraceElement[] elements = e.getStackTrace();
        int length = Math.min(elements.length, 5);
        for (int i = 0; i < length; i++) {
            StackTraceElement element = elements[i];
            if (element == null) {
                continue;
            }
            stringBuilder.append("#");
            stringBuilder.append(element.getLineNumber());
            stringBuilder.append(",");
            stringBuilder.append(element.getClassName());
            stringBuilder.append(",");
            stringBuilder.append(element.getMethodName());
        }
        return stringBuilder.toString();
    }

    public void closeDatabase() {
        if (usageService != null) {
            usageService.closeDatabase();
        }
    }

    /**
     * 处理下载后的离线包:合并成完整的zip包
     *
     * @param tempPatchFile
     * @param finalFile
     * @param bid
     * @param localVersion  本地的离线包版本号
     * @return
     */
    private boolean handleDownloadPatchFile(File tempPatchFile, File finalFile, String bid, long localVersion) throws Throwable {
        try {
            String type = TYPE_ZIP_FILE;
            if (finalFile.getName().endsWith(OfflineUtils.TYPE_7Z_FILE)) {
                type = OfflineUtils.TYPE_7Z_FILE;
            }
            File backupZipFile = OfflineUtils.getBackupPackage(bid, localVersion, type);
            //增量包或者备份包不存在，则废弃
            if (!tempPatchFile.exists() || !backupZipFile.exists() || backupZipFile.length() <= 0) {
                tempPatchFile.delete();
                throw new IOException("增量包或者备份包不存在 " + tempPatchFile.exists() + "   " + backupZipFile.exists());
            }
            long start = System.currentTimeMillis();
            LogUtils.w(TAG, "tang------合成增量包开始 bid=" + bid + "  source " + backupZipFile + "  final " + finalFile + "  patch " + tempPatchFile);
            int result = BsdiffUtil.apply_patch(backupZipFile.getAbsolutePath(), finalFile.getAbsolutePath(), tempPatchFile.getAbsolutePath());
            LogUtils.w(TAG, "tang------合成增量包完成，耗时 " + (System.currentTimeMillis() - start) + "  source " + backupZipFile + "  final " + finalFile + "  patch " + tempPatchFile);
            if (result < 0) {
                /*VogaStatistics.with()
                        .class1("apply_patch_error")
                        .class2("result =" + result)
                        .send();*/
            }
            return result > 0;
        } catch (Throwable ex) {
           /* VogaStatistics.with()
                    .class1("apply_patch_error")
                    .class2(ex.getMessage())
                    .send();*/
            LogUtils.e(ex);
            throw ex;
        }
    }

    /**
     * 读取已经缓存的预处理离线包数据
     *
     * @param bid
     * @return
     */
    public OfflinePackage getPackageInfo(String bid) {
        OfflinePackage offlinePackage = packageInfoCache.get(bid);
        //如果缓存的不对应，则重新读取离线包数据
        if (offlinePackage == null || offlinePackage.configs == null || !TextUtils.equals(offlinePackage.bid, bid)) {
            LogUtils.d(TAG, "tang-------需要读取离线包配置");
            offlinePackage = new OfflinePackage(bid);
            offlinePackage.init(OfflineUtils.getPackageDir(bid).getAbsolutePath());

            //读取使用情况
            long start = System.currentTimeMillis();
            PackageUsage usage = usageService.readPackageUsage(bid);
            if (usage == null) {
                usage = new PackageUsage(bid);
            }
            LogUtils.d(TAG, "tang-----读取离线包的使用情况 " + bid + "   耗时" + (System.currentTimeMillis() - start));
            offlinePackage.setUsages(usage);

            packageInfoCache.put(bid, offlinePackage);
        } else {
            LogUtils.d(TAG, "tang-------已经缓存离线包配置");
        }
        return offlinePackage;
    }

    /**
     * 离线包下载完成后，更新校验时间戳，更新上次检查更新时间，必须在异步线程中调用
     *
     * @param bid
     */
    private void resetRefreshPreProcessPackage(String bid) {
        LogUtils.d(TAG, "tang----下载成功后，更新预处理情况 " + bid);
        //清空缓存
        packageInfoCache.remove(bid);
        refreshCheckUpdateTime(bid);
    }

    /**
     * 更新检查更新时间
     *
     * @param bid
     */
    private void refreshCheckUpdateTime(String bid) {
        //更新检查时间
        OfflinePackage page = getPackageInfo(bid);
//        //更新最后检查更新时间
        if (page != null) {
            long time = page.refreshLastCheckUpdateTime();
            //db 入库
            boolean result = usageService.updateLastCheckUpdateTime(bid, time);
            LogUtils.d(TAG, "tang------更新检查时间 " + result + "  " + bid);
        }
    }

    /**
     * 打开离线包前进行一系列操作
     *
     * @param bid
     * @param currentUrl 当前打开的Url
     * @return 是否存在离线包  之所以需要返回结果，是防止在删除过期离线包时，页面去加载过期的离线包
     * -1代表没有离线包,-2 代表校验失败，1代表成功
     * 如果离线包过期，应该加载线上
     */
    public int preProcessPackage(final String bid, @Nullable String currentUrl) {
        return preProcessPackage(bid, currentUrl, true);
    }

    public int preProcessPackage(final String bid, @Nullable String currentUrl, boolean needUpdate) {
        if (TextUtils.equals("0", bid)) {
            return -1;
        }
        OfflinePackage offlinePackage = getPackageInfo(bid);
        if (!offlinePackage.isPackageExists()) {//离线包不存在
            return -1;
        }
        PackageEntity configs = offlinePackage.configs;
        if (configs == null) {
            return -1;
        }
        //离线包过期，删除离线包，不进行自动更新
        if (System.currentTimeMillis() > configs.getExpired()) {
            removeCachePackage(bid);
            return -1;
        }
        //如果没有过期，则检查更新
        if (offlinePackage.needCheckUpdate() && needUpdate) {
            checkUpdate(bid, currentUrl, null);
        }

        //纪录最近访问时间
        long lastVisitTime = offlinePackage.getLastVisitTime();
        //如果上次访问时间距离当前时间超过2小时，则写文件更新一次最新使用时间
        if (System.currentTimeMillis() - lastVisitTime > VERIFY_PACKAGE_EXPIRE_TIME) {
            AbsJob runnable = new AbsJob() {
                @Override
                public void run() {
                    usageService.updateLastVisitTime(bid, System.currentTimeMillis());
                }
            };
            ThreadPool.submit(runnable, "updateLastVisitTime");
        }
        return 1;
    }

    public static void unzipOfflinePage(String filePth, String bid) {

        try {
            OfflinePackage offlinePackage = new OfflinePackage(bid);
            offlinePackage.init(OfflineUtils.getPackageDir(bid).getAbsolutePath());
            if (offlinePackage.isPackageExists()) {
                return;
            }
            File file = new File(AppDirUtils.getCatchImage(), "temp.zip");
            if (file.exists()) {
                FileUtils.deleteAllFilesSafely(file);
            }
            FileUtils.copyFilesFassets(filePth, file.getAbsolutePath());
            UnzipUtils.unzipOfflinePackage(bid, file.getAbsolutePath(), TYPE_ZIP_FILE, false);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public static void unzipOfflineGiftVideo(String filePath, String resourceId) {
        try {
            File result = new File(AppDirUtils.getGiftVideo(), resourceId);
            if (result.exists()) {
                return;
            }

            File temp = new File(AppDirUtils.getGiftVideo(), resourceId + "_temp.zip");
            if (temp.exists()) {
                FileUtils.deleteAllFilesSafely(temp);
            }
            FileUtils.copyFilesFassets(filePath, temp.getAbsolutePath());

            FileUtils.upZip(AppDirUtils.getGiftVideo() + "/", temp.getName(), result.getName());

            FileUtils.deleteAllFilesSafely(temp);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private static boolean zip = false;

    private static void test() {
        if (zip) {
            return;
        }
        zip = true;
        AbsJob job = new AbsJob() {
            @Override
            protected void run() {
                File backupZipFile = new File(AppDirUtils.getCatchImage(), "0_1003400000_temp.zip");
                FileUtils.copyFileFromAssets("3_1.34.0_1003400000.zip", backupZipFile.getPath());

                File tempPatchFile = new File(AppDirUtils.getCatchImage(), "1.38.0-1003400000.patch.zip");
                FileUtils.copyFileFromAssets("1.38.0-1003400000.patch", tempPatchFile.getPath());

                File finalFile = new File(AppDirUtils.getCatchImage(), "0_1003400000.zip");

                try {
                    int result = BsdiffUtil.apply_patch(backupZipFile.getAbsolutePath(), finalFile.getAbsolutePath(), tempPatchFile.getAbsolutePath());
                    LogUtils.e(TAG, "result ==" + result);
                    UnzipUtils.unzip("aaa", finalFile, new File(AppDirUtils.getCatchImage(), "0_1003400000").getAbsolutePath(), false);
                } catch (Throwable ex) {
                    LogUtils.e(TAG, ex);
                }
            }
        };
        ThreadPool.submit(job, "test");
    }
}
