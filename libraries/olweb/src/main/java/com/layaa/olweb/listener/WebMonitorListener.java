package com.layaa.olweb.listener;

import android.graphics.Bitmap;
import android.net.http.SslError;
import android.webkit.SslErrorHandler;
import android.webkit.WebView;

/**
 * <AUTHOR>
 * @date 2020-01-10
 * @des
 **/
public interface WebMonitorListener {

    void onPageStarted(WebView webview, String url, Bitmap favicon);

    void onProgressChanged(WebView view, int newProgress);

    void onPageFinished(WebView webview, String url);

    // 针对 ol ，收集 native 层的错误
    void onReceiverNativeError(String url, Exception e);

    void onReceivedError(String url, int errorCode, String description, String failingUrl);

    void onReceivedSslError(String url, final SslErrorHandler handler, SslError error);

    void dispose();
}
