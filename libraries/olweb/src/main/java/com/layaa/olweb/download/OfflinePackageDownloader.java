package com.layaa.olweb.download;

import android.net.Uri;
import android.text.TextUtils;

import java.util.HashSet;

/**
 * <AUTHOR>
 * @date 2021/3/10
 * @des
 **/
public class OfflinePackageDownloader {

    private static final String TAG = "OfflinePackageDownloader";

    private static OfflinePackageDownloader instance;
    /**
     * 离线包下载任务纪录，防止重复下载
     * 使用Set来记录，避免并发导致重复的任务纪录
     */
    private static HashSet<String> downloadTaskRecord = new HashSet<>();

    private static HashSet<String> pathTaskRecord = new HashSet<>();

    public static OfflinePackageDownloader getInstance() {
        if (instance == null) {
            instance = new OfflinePackageDownloader();
        }
        return instance;
    }

    private OfflinePackageDownloader() {
    }

    /**
     * 纪录下载任务
     *
     * @param url
     * @return 返回true代表可以下载，false代表不进行下载
     */
    public boolean recordDownloadTask(String url) {
        if (TextUtils.isEmpty(url)) {
            return false;
        }
        //用路径判断 因为 会切换下载cdn 导致url不同
        Uri uri = Uri.parse(url);
        String path = uri.getEncodedPath();
        if (!downloadTaskRecord.contains(path)) {
            downloadTaskRecord.add(path);
            return true;
        }
        return false;
    }

    public void unRecordDownloadTask(String url) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        Uri uri = Uri.parse(url);
        String path = uri.getEncodedPath();
        downloadTaskRecord.remove(path);
    }
}
