package com.layaa.olweb.download;

import com.layaa.libutils.module_thread.task.ThreadPool;

import java.util.Map;
import java.util.WeakHashMap;

import androidx.annotation.MainThread;

/**
 * <AUTHOR>
 * @date 2021/3/10
 * @des
 **/
public class DownloadNotificationCenter {

    private WeakHashMap<String, DownloadObserver> observers = new WeakHashMap<>();

    public static DownloadNotificationCenter getInstance() {
        return SingleHolder.instance;
    }

    private static class SingleHolder {
        private static DownloadNotificationCenter instance = new DownloadNotificationCenter();
    }

    @MainThread
    public void addObserver(String tag, DownloadObserver observer) {
        observers.put(tag, observer);
    }

    @MainThread
    public void removeObserver(String tag) {
        observers.remove(tag);
    }

    public void sendNotification(final int id, final Object... objects) {
        ThreadPool.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (Map.Entry<String, DownloadObserver> stringDownloadObserverEntry : observers.entrySet()) {
                    if (stringDownloadObserverEntry.getValue() != null) {
                        stringDownloadObserverEntry.getValue().received(id, objects);
                    }
                }
            }
        });
    }

}
