package com.layaa.olweb.session;

import android.content.Context;
import android.webkit.CookieManager;
import android.webkit.CookieSyncManager;

import com.layaa.libutils.module_log.LogUtils;
import com.layaa.libutils.module_thread.task.AbsJob;
import com.layaa.libutils.module_thread.task.ThreadPool;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2021/3/10
 * @des
 **/
public class OLWebSessionHandler {
    private static final String TAG = OLWebSessionHandler.class.getSimpleName();

    private static ArrayList<CookieValue> webCookies = null;

    public static ArrayList<CookieValue> getWebSession() {
        if (webCookies == null) {
            webCookies = readWebPassport();
        }
        return webCookies;
    }

    public static boolean clearWebSession(Context pContext) {
        /*File file = getWebSessionFile();
        if (file != null && file.exists()) {
            file.delete();
        }*/
        webCookies = null;
        clearCookie(pContext);
        return true;
    }

    public static boolean updateWebSession(final WebSessionUpdateListener listener) {
        /**
         * 非登录状态下不去获取
         */
        AbsJob runnable = new AbsJob() {
            @Override
            public void run() {
                try {
                    //cookie获取需要重新定义
                    /*ArrayList<CookieValue> values = MKWebApi.getInstance().getWebSession();
                    updateAndSave(values);*/

                    if (listener != null) {
                        listener.onFinished();
                    }
                } catch (Exception e) {
                    LogUtils.e(TAG, e);
                    if (listener != null) {
                        listener.onError();
                    }
                }
            }
        };
        ThreadPool.submit(runnable, "updateWebSession");
        return true;
    }


    private static ArrayList<CookieValue> readWebPassport() {
        try {
            /*File pfile = getWebSessionFile();
            if (pfile == null || !pfile.exists()) {
                return null;
            }*/
            //cookie需要重新定义
            ArrayList<CookieValue> values = new ArrayList<>();
            /*byte[] data = FileUtil.readBytes(pfile);
            if (data == null) {
                return values;
            }
            String output = AESUtils.decryptCommon(data);
            JSONArray jsonArray = new JSONArray(output);
            CookieValue cookie = null;
            for (int i = 0; i < jsonArray.length(); i++) {
                cookie = CookieValue.fromJson(jsonArray.getJSONObject(i));
                if (cookie != null) {
                    values.add(cookie);
                }
            }*/
//            LogUtil.d(TAG, "tang-----getWebPassport " + values.size());
            return values;
        } catch (Exception ex) {
            LogUtils.e(TAG, ex);
        }
        return null;
    }

    private static void clearCookie(Context pContext) {
        if (pContext != null) {
            try {
                CookieSyncManager.createInstance(pContext);
                CookieSyncManager.getInstance().startSync();
                CookieManager.getInstance().removeSessionCookie();
            } catch (Throwable e) {
                LogUtils.i(TAG, e.toString());
            }
        }
    }

    public interface WebSessionUpdateListener {
        void onFinished();

        void onError();
    }
}
