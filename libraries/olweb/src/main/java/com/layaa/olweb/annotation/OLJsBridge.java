package com.layaa.olweb.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2021/1/7
 * @des js 桥接
 **/
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface OLJsBridge {
    /**
     * bridge方法执行线程
     *
     * @return
     */
    @ThreadMode
    int threadMode();
}
