package com.layaa.olweb.listener;

import android.graphics.Bitmap;
import android.webkit.WebView;

/**
 * <AUTHOR>
 * @date 2021/3/18
 * @des
 **/
public interface OLPageLifeCycleCallback {

    /**
     * @param webview
     * @param url
     * @param favicon
     */
    void onPageStarted(WebView webview, String url, Bitmap favicon);

    /**
     * @param webview
     * @param url
     */
    void onPageFinished(WebView webview, String url);

    /**
     * @param view
     * @param errorCode
     * @param description
     * @param failingUrl
     */
    void onPageError(WebView view, int errorCode, String description, String failingUrl);
}
