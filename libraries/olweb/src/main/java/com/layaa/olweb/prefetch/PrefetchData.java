package com.layaa.olweb.prefetch;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2020-01-10
 * @des
 **/
public class PrefetchData {
    private static final String RT = "requestTime";
    private static final String WT = "waitTime";
    private static final String PF = "prefetch";

    private JSONObject data;

    private long requestTime = -1;

    private long waitTime = -1;

    public PrefetchData() {
    }

    public PrefetchData(String data) {
        setData(data);
    }

    public PrefetchData(JSONObject data) {
        setData(data);
    }

    public void setData(JSONObject data) {
        this.data = data;
        JSONObject pf = data.optJSONObject(PF);
        if (pf != null) {
            requestTime = pf.optLong(RT, -1);
            waitTime = pf.optLong(WT, -1);
        }
    }

    public void setData(String data) {
        try {
            setData(new JSONObject(data));
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public JSONObject getData() {
        return data;
    }

    public void setRequestTime(long requestTime) {
        this.requestTime = requestTime;
    }

    public void setWaitTime(long waitTime) {
        this.waitTime = waitTime;
    }

    public JSONObject toJson() {
        try {
            JSONObject result = data;

            JSONObject pf = new JSONObject()
                    .putOpt(RT, requestTime)
                    .putOpt(WT, waitTime);

            result.putOpt(PF, pf);
            return result;
        } catch (JSONException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String toString() {
        JSONObject jo = toJson();
        if (jo != null)
            return jo.toString();
        return null;
    }
}
