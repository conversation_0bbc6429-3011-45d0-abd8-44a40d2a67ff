package com.layaa.olweb.prefetch;

import android.net.Uri;
import android.text.TextUtils;

import com.layaa.olweb.OLKit;
import com.layaa.olweb.config.IHttpClient;
import com.layaa.olweb.utils.OLFileHelper;
import com.layaa.libutils.EncryptUtils;
import com.layaa.libutils.FileUtils;
import com.layaa.libutils.module_log.LogUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;

import androidx.annotation.NonNull;

/**
 * <AUTHOR>
 * @date 2020-01-10
 * @des 这个是一个预加载接口管理类 某些接口未加载出来之前，不请求其他api 业务暂时不用
 **/
public class PreFetchManager {

    private static final String TAG = "SYNC-PreFetchManager";

    private static final String PREFETCH_DIR = "PREFETCH";
    private static volatile PreFetchManager instance;

    public static PreFetchManager getInstance() {
        if (instance == null) {
            synchronized (PreFetchManager.class) {
                if (instance == null) {
                    instance = new PreFetchManager();
                }
            }
        }
        return instance;
    }

    public static synchronized void releaseInstance() {
        if (instance != null) {
            instance.release();
        }
        instance = null;
        baseFile = null;
    }

    public static String getPrefetchKey(Object uniqueKey, String api, JSONObject params) {
        return uniqueKey + "_" + api + (params != null ? params.toString() : "");
    }

    private static File baseFile;

    private IScheduler scheduler;
    private ISyncObjectPool syncObjectPool;

    private HashMap<String, File> cacheFiles;

    private final Object syncForPrefetch;
    private final Object syncForCache;

    private PreFetchManager() {
        syncObjectPool = new SyncObjectPoolImpl();
        scheduler = new MultiThreadScheduler(syncObjectPool);
        cacheFiles = new HashMap<>();
        syncForPrefetch = new Object();
        syncForCache = new Object();
    }

    private void release() {
        synchronized (syncForPrefetch) {
            scheduler.release();
            deleteAllCacheFiles();
        }
    }

    public void addPrefetch(@NonNull Object uniqueKey, String url, String prefetch) {
        if (TextUtils.isEmpty(url) || TextUtils.isEmpty(prefetch)) {
            return;
        }
        synchronized (syncForPrefetch) {
            try {
                JSONArray ja = new JSONArray(prefetch);
                if (ja != null) {
                    final int len = ja.length();
                    for (int i = 0; i < len; i++) {
                        JSONObject jo = ja.optJSONObject(i);
                        addPrefetch(uniqueKey, url, jo);
                    }
                }
            } catch (JSONException e) {
                LogUtils.e(TAG, e);
            }
        }
    }

    private static File getBaseDir() {
        if (baseFile == null) {
            File dir = new File(OLFileHelper.getCacheDir(), PREFETCH_DIR);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            baseFile = dir;
        }
        return baseFile;
    }

    public void deleteDirByUniqueKey(@NonNull Object uniqueKey) {
        File dir = getBaseFileByUniqueKey(uniqueKey);
        if (dir != null) {
            FileUtils.deleteAllFilesSafely(dir);
        }
    }

    private File getBaseFileByUniqueKey(@NonNull Object uniqueKey) {
        String md5 = EncryptUtils.getMD5(uniqueKey.toString());
        File baseFile = new File(getBaseDir(), md5);
        if (!baseFile.exists()) {
            baseFile.mkdirs();
        }
        return baseFile;
    }

    private String getUrlPre(String originUrl) {
        Uri uri = Uri.parse(originUrl);
        return uri.getScheme() + "://" + uri.getHost();
    }

    private void addPrefetch(@NonNull Object uniqueKey, String url, JSONObject prefetch) {
        if (prefetch == null) {
            return;
        }
        String api = prefetch.optString("url");
        if (TextUtils.isEmpty(api)) {
            return;
        }
        if (api.startsWith("/")) {
            api = getUrlPre(url) + api;
        }
        JSONObject dataParam = prefetch.optJSONObject("data");
        String prefetchKey = getPrefetchKey(uniqueKey, api, dataParam);
        scheduler.schedule(prefetchKey, new PreFetchAction(uniqueKey, url, api, dataParam));
    }

    public boolean waitForKey(String prefetchKey) throws InterruptedException {
        synchronized (syncForPrefetch) {
            return syncObjectPool.waitForKey(prefetchKey);
        }
    }

    public File getCacheFile(@NonNull Object uniqueKey, String url, String prefetchStr) {
        synchronized (syncForCache) {
            File result = cacheFiles.get(prefetchStr);
            if (result != null && result.exists()) {
                return result;
            }
            File dir = getDirByUrl(uniqueKey, url);
            if (dir == null) {
                return null;
            }
            return new File(dir, EncryptUtils.getMD5(prefetchStr));
        }
    }

    public void deleteCacheFile(String prefetchStr) {
        synchronized (syncForCache) {
            File file = cacheFiles.remove(prefetchStr);
            if (file != null) {
                file.delete();
            }
        }
    }

    private File getDirByUrl(@NonNull Object uniqueKey, String url) {
        if (TextUtils.isEmpty(url)) {
            return null;
        }
        String md5 = EncryptUtils.getMD5(url);
        File dir = new File(getBaseFileByUniqueKey(uniqueKey), md5);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        return dir;
    }

    private void deleteAllCacheFiles() {
        synchronized (syncForCache) {
            FileUtils.deleteAllFilesSafely(getBaseDir());
        }
    }

    private class PreFetchAction implements Runnable {
        String baseUrl;
        String api;
        JSONObject data;
        Object uniqueKey;

        PreFetchAction(Object uniqueKey, String baseUrl, String api, JSONObject data) {
            this.uniqueKey = uniqueKey;
            this.baseUrl = baseUrl;
            this.api = api;
            this.data = data;
        }

        @Override
        public String toString() {
            return "PreFetchAction pk: " + api + (data != null ? data.toString() : "");
        }

        @Override
        public void run() {
            long time = System.currentTimeMillis();
            IHttpClient requester = OLKit.getHttpClient();
            HashMap<String, String> params = null;
            if (data != null) {
                params = new HashMap<>();
                Iterator<String> iterator = data.keys();
                boolean needDeviceId = data.optInt("needDeviceId", 0) == 1;
                String key;
                while (iterator.hasNext()) {
                    key = iterator.next();
                    params.put(key, String.valueOf(data.opt(key)));
                }
            }
            PrefetchData prefetchData = new PrefetchData();
            try {
                JSONObject response = new JSONObject(requester.doPost(api, params, null));
                prefetchData.setData(response);
            } catch (Exception e) {
                LogUtils.e(TAG, e);
                try {
                    JSONObject json = new JSONObject();
                    json.put("ec", -1);
                    json.put("em", "网络请求失败");
                    json.put("exmsg", e.getMessage());
                    prefetchData.setData(json);
                } catch (JSONException ex) {
                    LogUtils.e(TAG, ex);
                }
            }
            LogUtils.d(TAG, "request data: %s", prefetchData.getData());
            if (uniqueKey == null) {
                LogUtils.e(TAG, "unique key is null!");
                return;
            }
            time = System.currentTimeMillis() - time;
            prefetchData.setRequestTime(time);
            try {
                String prefetchKey = getPrefetchKey(uniqueKey, api, data);
                File file = saveToFile(uniqueKey, prefetchData.toString(), prefetchKey);
                cacheFiles.put(prefetchKey, file);
            } catch (IOException e) {
                LogUtils.e(TAG, e);
            }
        }

        private File saveToFile(@NonNull Object uniqueKey, String requestData, String prefetchKey) throws IOException {
            File file = getCacheFile(uniqueKey, baseUrl, prefetchKey);
            FileUtils.writeStr(file, requestData);
            return file;
        }
    }
}
