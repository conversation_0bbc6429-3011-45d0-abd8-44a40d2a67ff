package com.layaa.olweb.jsbridge;

import com.layaa.libutils.module_log.LogUtils;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2021/1/13
 * @des
 **/
public class WebBatteryStatus {

    private float level = -1;
    private boolean isCharging = false;

    @Override
    public String toString() {
        JSONObject json = new JSONObject();
        try {
            //不使用 Float.valueOf会出现精度错误的问题
            json.put("level", Float.valueOf(level));
            json.put("is_plugged", isCharging);
            return json.toString();
        } catch (JSONException e) {
            LogUtils.e(e);
        }
        return "";
    }

    public void setLevel(float level) {
        this.level = level;
    }

    public void setCharging(boolean charging) {
        isCharging = charging;
    }
}
