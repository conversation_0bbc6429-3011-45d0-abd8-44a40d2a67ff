package com.layaa.olweb.jsbridge;

import android.text.TextUtils;

import com.layaa.olweb.OLKit;
import com.layaa.olweb.annotation.OLJsBridge;
import com.layaa.olweb.annotation.OLSubscribe;
import com.layaa.olweb.annotation.ThreadMode;
import com.layaa.olweb.event.GlobalEventAdapter;
import com.layaa.olweb.event.GlobalEventManager;
import com.layaa.olweb.event.GlobalEventSubscriber;
import com.layaa.olweb.utils.OLHelper;
import com.layaa.olweb.widget.OLWebView;
import com.layaa.libutils.module_log.LogUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * <AUTHOR>
 * @date 2021/3/10
 * @des
 **/
@OLSubscribe(nameSpace = "globalEvent")
public class GlobalEventBridge implements GlobalEventSubscriber {

    private static final int NATIVE = 1;
    private static final int WEEX = 1 << 1;
    private static final int OL = 1 << 2;
    private static final int LUA = 1 << 3;

    private static final String TAG = GlobalEventBridge.class.getSimpleName();

    private Set<String> eventSet = new HashSet<>();

    private GlobalEventAdapter adapter;

    private OLWebView OLWebView;

    private String callback;

    public GlobalEventBridge(OLWebView OLWebView) {
        this.OLWebView = OLWebView;
        adapter = OLKit.getGlobalEventAdapter();
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void subscribeMessage(JSONObject params) {
        if (adapter != null) {
            adapter.register(this);
            String key = params.optString("eventId");
            eventSet.add(key);
        }
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void onSocketMsg(JSONObject params) {
        callback = OLWebView.getJSCallback(params);
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_CURRENT)
    public void postEvent(JSONObject params) {
        if (adapter != null) {
            doPostEvent(params);
        }
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_MAIN)
    public void unSubscribeMessage(JSONObject params) {
        String key = params.optString("eventId");
        eventSet.remove(key);
    }

    @Override
    public void onReceiveEvent(@NonNull String eventName, @Nullable String data) {
        LogUtils.i(TAG, "eventName: " + eventName + ", data: " + data);
        try {
            if (eventSet.contains(eventName)) {
                JSONObject result = new JSONObject();
                result.put("eventId", eventName);
                if (data != null) {
                    result.put(eventName, new JSONObject(data));
                }
                OLHelper.insertCallback(OLWebView, callback, result.toString());
            }
        } catch (JSONException e) {
            LogUtils.e(e);
        }
    }

    // ui.postMessage 跟 Global postMessage 做一个兼容
    private void doPostEvent(JSONObject paramsObj) {

        boolean hasNameKey = !TextUtils.isEmpty(paramsObj.optString("name"));
        if (hasNameKey) {
            // ui postMessage 数据格式
            adapter.sendEvent(paramsObj.optString("name"), paramsObj.optString("dst"), paramsObj.optString("data"));
        } else {
            // global postMessage 数据格式
            if (paramsObj.has("eventId")) {
                String eventName = paramsObj.optString("eventId");
                JSONObject options = paramsObj.optJSONObject("options");
                if (options != null) {
                    int dstValue = options.optInt("l_evn");
                    String dest = parseDest(dstValue);
                    if (dest == null) {
                        dest = "";
                    }
                    String data = options.optString("event_data");
                    adapter.sendEvent(eventName, dest, data);
                }
            }
        }
    }

    public void onDestroy() {
        if (adapter != null) {
            adapter.unregister(this);
        }
        eventSet.clear();
    }


    // native 1 weex 2 ol 4 lua 8 通知目的环境源
    private String parseDest(final int dstValue) {
        List<String> destArray = new ArrayList<>();
        // check native
        if ((dstValue & NATIVE) != 0) {
            destArray.add(GlobalEventManager.EVENT_NATIVE);
        }

        // check weex
        if ((dstValue & WEEX) != 0) {
            destArray.add(GlobalEventManager.EVENT_WEEX);
        }

        // check ol
        if ((dstValue & OL) != 0) {
            destArray.add(GlobalEventManager.EVENT_OL);
        }

        // check lua
        if ((dstValue & LUA) != 0) {
            destArray.add(GlobalEventManager.EVENT_LUA);
        }
        if (destArray.size() > 0) {
            return TextUtils.join("|", destArray);
        } else {
            return null;
        }
    }
}
