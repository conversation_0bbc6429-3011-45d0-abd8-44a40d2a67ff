package com.layaa.olweb.listener;

import android.webkit.WebResourceResponse;
import android.webkit.WebView;
import android.webkit.WebViewClient;

/**
 * <AUTHOR>
 * @date 2020-01-10
 * @des
 **/
public interface PreLoadingUrlListener {

    /**
     * 处理url 如果返回true，则代表拦截请求成功，OLWebView不再加载url
     * 在{@link WebViewClient#shouldOverrideUrlLoading} 加载一个url之前调用，处理网页请求
     *
     * @param url
     * @return
     */
    boolean beforeShouldOverrideUrlLoading(String url);

    /**
     * 在{@link WebView#loadUrl(String)}之前处理url，如果处理了，则不继续执行
     *
     * @param url
     * @return
     */
    boolean beforeLoadUrl(String url);

    public WebResourceResponse shouldInterceptRequest(WebView view, String url);
}
