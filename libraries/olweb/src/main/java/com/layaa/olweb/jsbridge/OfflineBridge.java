package com.layaa.olweb.jsbridge;

import android.text.TextUtils;

import com.layaa.olweb.annotation.OLJsBridge;
import com.layaa.olweb.annotation.OLSubscribe;
import com.layaa.olweb.annotation.ThreadMode;
import com.layaa.olweb.constant.OLConstants;
import com.layaa.olweb.core.BridgeAsyncCallback;
import com.layaa.olweb.entity.PackageEntity;
import com.layaa.olweb.offline.OLPackageManager;
import com.layaa.olweb.offline.OfflineUtils;
import com.layaa.olweb.router.OLPackageRouter;
import com.layaa.olweb.utils.OLFileHelper;
import com.layaa.olweb.utils.OLHelper;
import com.layaa.olweb.widget.OLWebView;
import com.layaa.libutils.module_log.LogUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.Iterator;

/**
 * <AUTHOR>
 * @date 2021/3/10
 * @des
 **/
@OLSubscribe(nameSpace = "offline")
public class OfflineBridge {
    private static final int SUCCESS = 0;
    private static final int DOWNLOAD_FAIL = 1;
    private static final int UNZIP_FAIL = 2;
    private static final String TAG = "OfflineBridge";
    private OLWebView OLWebView;

    public OfflineBridge(OLWebView pOLWebview) {
        OLWebView = pOLWebview;
    }

    public void checkUpdate(JSONObject params) {
        LogUtils.d(TAG, "检查更新 " + params);
        String bid = params.optString("bid");
        BridgeAsyncCallback.Builder checkBuilder = new BridgeAsyncCallback.Builder()
                .olWebview(OLWebView)
                .jsCallback(OLHelper.getJsCallback(params))
                .successCode(0)
                .successMsg("更新成功")
                .errorCode(0)
                .errorMsg("更新失败");
        OLPackageManager.getInstance().checkUpdate(bid, getCurrentUrl(), checkBuilder.build());
    }

    public void update(JSONObject params) {
        LogUtils.d(TAG, "强制更新 " + params);
        String updatebid = params.optString("bid");
        String updateurl = params.optString("url");

        BridgeAsyncCallback.Builder updateBuilder = new BridgeAsyncCallback.Builder()
                .olWebview(OLWebView)
                .jsCallback(OLHelper.getJsCallback(params))
                .successCode(0)
                .successMsg("更新成功")
                .errorCode(0)
                .errorMsg("更新失败");
        OLPackageManager.getInstance().forceUpdatePackage(updatebid, updateurl, updateBuilder.build());
    }

    public void clearCache(JSONObject params) {

        BridgeAsyncCallback.Builder clearBuilder = new BridgeAsyncCallback.Builder()
                .olWebview(OLWebView)
                .jsCallback(OLHelper.getJsCallback(params))
                .successCode(0)
                .successMsg("删除成功")
                .errorCode(0)
                .errorMsg("删除失败");
        OLPackageManager.getInstance().clearAllPackageCache(clearBuilder.build());
    }

    public void isCached(JSONObject params) {
        String isCachedId = params.optString("bid");
        long version = OLPackageManager.getInstance().isCached(isCachedId);
        JSONObject isCachedObject = OLHelper.assembleJsonObject(new String[]{"version"}, new Object[]{version});
        OLHelper.insertCallback(OLWebView, OLHelper.getJsCallback(params), isCachedObject.toString());
    }

    public void removeCache(JSONObject params) {
        String removeCacheBid = params.optString("bid");
        boolean isremoveCache = OLPackageManager.getInstance().removeCachePackage(removeCacheBid);
        JSONObject removeResultObject = OLHelper.assembleJsonObject(
                new String[]{OLConstants.status, OLConstants.message},
                new String[]{isremoveCache ? "0" : "1", isremoveCache ? "删除成功" : "删除失败"}
        );
        OLHelper.insertCallback(OLWebView, OLHelper.getJsCallback(params), removeResultObject.toString());
    }

    public void getVersion(JSONObject params) {
        String version_bid = params.optString("bid");
        boolean useAbPath = params.optInt("ap") == 1;
        long getVersion = 0;
        if (useAbPath) {
            File dir = OLFileHelper.getAppHomeDir();
            if (dir != null)
                dir = new File(dir, version_bid);
            if (dir != null) {
                try {
                    PackageEntity configs = OfflineUtils.readPackageConfigs(dir.getAbsolutePath());
                    if (configs != null) {
                        getVersion = configs.getVersion();
                    }
                } catch (Exception ignore) {

                }
            }
        } else {
            getVersion = OLPackageManager.getInstance().getVersion(version_bid);
        }
        OLHelper.insertCallback(OLWebView, OLHelper.getJsCallback(params), getVersion + "");
    }

    //todo 这个问下是否有用到
    public void sendOfflineRequest(JSONObject params) {
        /*final String bid = params.optString("bid");
        final String callback = MKHelper.getJsCallback(params);
        if (TextUtils.isEmpty(bid)) {
            MKHelper.insertCallback(mkWebView,callback, MKHelper.assembleJsonObject(new String[]{MKConstants.status}, new Object[]{DOWNLOAD_FAIL}).toString());
            return;
        }
        final GameResource resource = GameResource.parseJson(params);
        if (resource == null || !resource.isValid()) {
            MKHelper.insertCallback(mkWebView,callback, MKHelper.assembleJsonObject(new String[]{MKConstants.status}, new Object[]{DOWNLOAD_FAIL}).toString());
            return;
        }

        if (GameResourceFileUtils.isGameResourceOk(bid, resource)) {
            insertCallback(callback, MKUtils.assembleJsonObject(new String[]{MKConstants.status}, new Object[]{SUCCESS}).toString());
            return;
        }
        GameResourceDownloader.getInstance().downloadResource(bid, resource, false, true, new GameDownloadListener() {
            @Override
            public void onProgress(String tag, int type, int status, long total, long progress) {
                //
            }

            @Override
            public void onError(String tag, int type, Exception e) {
                if (type == GameDownloadListener.TYPE_UNZIP) {
                    insertCallback(callback, MKUtils.assembleJsonObject(new String[]{MKConstants.status}, new Object[]{UNZIP_FAIL}).toString());
                } else {
                    insertCallback(callback, MKUtils.assembleJsonObject(new String[]{MKConstants.status}, new Object[]{DOWNLOAD_FAIL}).toString());
                }
            }

            @Override
            public void onComplete(String tag, int type) {
                if (type == GameDownloadListener.TYPE_UNZIP) {
                    insertCallback(callback, MKUtils.assembleJsonObject(new String[]{MKConstants.status}, new Object[]{SUCCESS}).toString());
                }
            }

            @Override
            public void onPause(String bid, int type) {

            }
        });*/

    }

    public void isResourceOfflined(JSONObject params) {
        final String callback = OLHelper.getJsCallback(params);
        final JSONObject resources = params.optJSONObject("resources");
        if (resources == null)
            return;
        Iterator<String> keys = resources.keys();
        JSONObject result = new JSONObject();
        while (keys.hasNext()) {
            String k = keys.next();
            String url = resources.optString(k);
            if (TextUtils.isEmpty(url)) {
                putOpt(result, k, false);
                continue;
            }
            String bid = OLPackageRouter.getBidFromUrl(url);
            if (TextUtils.isEmpty(bid)) {
                putOpt(result, k, false);
                continue;
            }
            File root = OfflineUtils.getPackageDir(bid);
            if (root == null || !root.exists()) {
                putOpt(result, k, false);
                continue;
            }
            File file = new File(root, OLPackageRouter.getUrlPath(url));
            if (file.exists() && file.length() > 0) {
                putOpt(result, k, true);
                continue;
            }
            putOpt(result, k, false);
        }
        OLHelper.insertCallback(OLWebView, callback, result.toString());
    }

    @OLJsBridge(threadMode = ThreadMode.THREAD_SYNC)
    public void removeFiles(JSONObject params) {

        try {
            final JSONArray fileArray = params.optJSONArray("files");

            for (int i = 0; i < fileArray.length(); i++) {
                File file = OLPackageRouter.getOfflineFileFromUrl(fileArray.optString(i));
                if (file != null && file.exists() && file.isFile()) {
                    file.delete();
                }
            }
        } catch (Exception ex) {
            LogUtils.e(TAG, ex);
        }
    }

    private String getCurrentUrl() {
        return OLWebView != null ? OLWebView.getOriginURL() : "";
    }

    private void putOpt(JSONObject json, String k, boolean b) {
        try {
            json.putOpt(k, b);
        } catch (JSONException e) {
            LogUtils.e(TAG, e);
        }
    }
}
