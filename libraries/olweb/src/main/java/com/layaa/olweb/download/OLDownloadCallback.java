package com.layaa.olweb.download;

/**
 * <AUTHOR>
 * @date 2021/3/10
 * @des 下载callback
 **/
public interface OLDownloadCallback {

    /**
     * 下载成功
     *
     * @param bid
     * @param gameVersion
     */
    void onDownloadSuccess(String bid, String gameVersion);

    /**
     * 下载中
     *
     * @param bid
     * @param percent
     * @param total
     */
    void onDownloadProgress(String bid, int percent, long total);

    /**
     * 下载失败
     *
     * @param bid
     * @param failMsg
     */
    void onDownloadFail(String bid, String failMsg);

    /**
     * 开始下载
     *
     * @param bid
     */
    void onDownloadStart(String bid);
}
