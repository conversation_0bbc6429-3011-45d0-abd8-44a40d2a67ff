package com.layaa.olweb;

import android.content.Context;

import com.layaa.olweb.config.IDownloadServer;
import com.layaa.olweb.config.IHttpClient;
import com.layaa.olweb.config.IImageLoader;
import com.layaa.olweb.config.InterceptConfig;
import com.layaa.olweb.config.OLConfig;
import com.layaa.olweb.event.GlobalEventAdapter;

import okhttp3.OkHttpClient;

/**
 * <AUTHOR>
 * @date 2022/9/15
 * @des offline webView
 **/
public class OLKit {

    private static OLConfig sConfig;

    public static Context getContext() {
        return sConfig.getContext();
    }

    public static boolean isDebug() {
        return sConfig.isDebug();
    }

    private static String jsInterfaceName = "olAobj";

    private static String jsPromptScheme = "oljsbridge";

    public static boolean isIntranet() {
        return sConfig.isIntranet();
    }

    public static boolean isEnableLog() {
        return sConfig.isEnableLog();
    }

    public static void init(OLConfig config) {
        sConfig = config;
    }

    public static GlobalEventAdapter getGlobalEventAdapter() {
        return sConfig.getAdapter();
    }

    public static String getOlUA() {
        return sConfig.getOlUA();
    }

    public static InterceptConfig getInterceptConfig() {
        return sConfig.getInterceptConfig();
    }

    public static IHttpClient getHttpClient() {
        return sConfig.getHttpClient();
    }

    public static IImageLoader getImageLoader() {
        return sConfig.getImageLoader();
    }

    public static String getBaseUrl() {
        return sConfig.getBaseUrl();
    }

    public static OkHttpClient getOkHttpClient() {
        return sConfig.getClient();
    }

    public static IDownloadServer getDownloadServer() {
        return sConfig.getDownloadServer();
    }

    public static OLConfig getConfig() {
        return sConfig;
    }

    public static String getJsInterfaceName() {
        return jsInterfaceName;
    }

    public static void setJsInterfaceName(String jsInterfaceName) {
        OLKit.jsInterfaceName = jsInterfaceName;
    }

    public static String getJsPromptScheme() {
        return jsPromptScheme;
    }

    public static void setJsPromptScheme(String jsPromptScheme) {
        OLKit.jsPromptScheme = jsPromptScheme;
    }
}
