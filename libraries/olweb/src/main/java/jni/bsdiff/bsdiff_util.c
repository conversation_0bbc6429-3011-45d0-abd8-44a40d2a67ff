/* DO NOT EDIT THIS FILE - it is machine generated */
#include "bsdiff_util.h"
#include "bspatch.c"

/*
 * Class:     com_biz_weblogic_util_BsdiffUtil
 * Method:    apply_patch
 * Signature: (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)I
 */
JNIEXPORT jint

JNICALL Java_com_layaa_olweb_bsdiff_BsdiffUtil_apply_1patch(JNIEnv *env,
                                                          jclass jcls,
                                                          jstring old_file_path,
                                                          jstring new_file_path,
                                                          jstring patch_file_path) {
    char *ch[4];
    ch[0] = "bspatch";
    ch[1] = (char *) ((*env)->GetStringUTFChars(env, old_file_path, 0));
    ch[2] = (char *) ((*env)->GetStringUTFChars(env, new_file_path, 0));
    ch[3] = (char *) ((*env)->GetStringUTFChars(env, patch_file_path, 0));

    int ret = apply_patch(4, ch);
    (*env)->ReleaseStringUTFChars(env, old_file_path, ch[1]);
    (*env)->ReleaseStringUTFChars(env, new_file_path, ch[2]);
    (*env)->ReleaseStringUTFChars(env, patch_file_path, ch[3]);

    return ret;
}