/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
#include "./../log.h"
/* Header for class com_biz_weblogic_util_BsdiffUtil */

#ifndef _Included_com_layaa_olweb_bsdiff_BsdiffUtil
#define _Included_com_layaa_olweb_bsdiff_BsdiffUtil
#ifdef __cplusplus
extern "C" {
#endif

/*
 * Class:     com_biz_weblogic_util_BsdiffUtil
 * Method:    apply_patch
 * Signature: (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)I
 */
JNIEXPORT jint

JNICALL
Java_com_layaa_olweb_bsdiff_BsdiffUtil_apply_1patch(JNIEnv *env, jclass jcls, jstring old_file_path,
                                                  jstring new_file_path, jstring patch_file_path);

#ifdef __cplusplus
}
#endif
#endif

