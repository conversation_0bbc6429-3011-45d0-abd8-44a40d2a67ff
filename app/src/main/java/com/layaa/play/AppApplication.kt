package com.layaa.play

import android.app.Application
import android.os.Build
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import com.alibaba.android.arouter.launcher.ARouter
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.layaa.accountapi.config.ConfigRouter
import com.layaa.accountapi.config.RemoteConfigKey
import com.layaa.accountapi.event.LoginEvent
import com.layaa.accountapi.event.LogoutEvent
import com.layaa.accountapi.login.LoginPath
import com.layaa.accountapi.login.LoginRouter
import com.layaa.aiapi.AiRouter
import com.layaa.chatapi.ChatRouter
import com.layaa.chatapi.dialog.DialogRouter
import com.layaa.chatapi.im.IMConfig
import com.layaa.gameapi.GameRouter
import com.layaa.gotorouter.GotoApp
import com.layaa.language.LanguageController
import com.layaa.language.LanguageNotification
import com.layaa.language.LanguageObserve
import com.layaa.libnet.RetrofitFactory
import com.layaa.libnet.exception.NetErrorHandler
import com.layaa.libnet.http.HttpConfig
import com.layaa.libnet.http.HttpLogger
import com.layaa.libnet.http.HttpLoggingInterceptor
import com.layaa.libnet.net.RetrofitProxy
import com.layaa.libnet.util.NetError
import com.layaa.libui.GotoConst
import com.layaa.libui.log.LogUtils
import com.layaa.libui.utils.DebugUtil
import com.layaa.libui.utils.ImageAppLoaderUtil
import com.layaa.libutils.AppDirUtils
import com.layaa.libutils.DeviceUtils
import com.layaa.libutils.NetUtils
import com.layaa.libutils.PackageUtils
import com.layaa.libutils.UtilKit
import com.layaa.media.MediaConfig
import com.layaa.media.MediaKit
import com.layaa.olweb.event.GlobalEventManager
import com.layaa.payclient.PayClient
import com.layaa.payclient.config.PayConfig
import com.layaa.play.interceptor.EncryptInterceptor
import com.layaa.play.interceptor.GotoInterceptor
import com.layaa.play.interceptor.JSONInterceptor
import com.layaa.play.pay.PayTextConfig
import com.layaa.play.push.PushManager
import com.layaa.play.util.NetToastUtils
import com.layaa.roomapi.router.RoomRouter
import com.layaa.skinlib.SkinKit
import com.layaa.widget.AvatarView
import com.layaa.widget.AvatarView.Companion.loadImage
import com.tencent.mmkv.BuildConfig
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe


class AppApplication : Application(), LanguageObserve {

    companion object {
        const val TAG = "AppApplication"
    }

    override fun onCreate() {
        super.onCreate()
        DebugUtil.debug = BuildConfig.DEBUG || !getString(R.string.isRelease).toBoolean()
        if (DebugUtil.debug) {
            ARouter.openDebug()
            ARouter.openLog()
            AppKit.openDebug()
            LogUtils.setDebug(true)
        } else {
            LogUtils.setDebug(false)
        }
        UtilKit.context = this // 下面的 LanguageController 中的 KVDelegate 会用到，需要先初始化
        UtilKit.context = LanguageController.attachBaseContext(this)
        UtilKit.registerActivityMonitor(this)
        AppKit.instance.register(this)
        ARouter.init(this)
        GotoApp.scheme = GotoConst.SCHEME
        GotoApp.interceptor = GotoInterceptor()

        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        LoginRouter.initRemoteConfig()
        initLanguage()
        initMedia()
        initHttp()
        initSkin()
        LoginRouter.initMK(this)
        GameRouter.getGameService()
        ChatRouter.initIM(IMConfig(getString(R.string.tencent_im_app_id), ""))
        ConfigRouter.setupWithoutLogin()
        PayConfig.text = PayTextConfig()
        // PayConfig.logCallback = PayLogCallback()
        if (LoginRouter.isLogin()) {
            loginApp()
        }
        LanguageNotification.addObserve(TAG, this)
        loadImage = { view: ImageView?, path: Any?, size: Int?, type: AvatarView.Type? ->
            view?.let {
                if (type == AvatarView.Type.AVATAR) {
                    Glide.with(it)
                        .load(path)
                        .transform(CircleCrop())
                        .transition(DrawableTransitionOptions().crossFade(300))
                        .error(R.drawable.icon_def_avatar)
                        .placeholder(R.drawable.icon_def_avatar)
                        .into(view)
                } else {
                    if (path is Int) {
                        Glide.with(it).load(path).into(view)
                    } else if (path is String) {
                        ImageAppLoaderUtil.loadImageAndGift(path, 0, view)
                    }
                }
            }

            null
        }
        GlobalEventManager.getInstance().init(this)
    }

    @Subscribe
    fun onEvent(event: LogoutEvent) {
        RoomRouter.quitRoom("logout")
        AiRouter.logout()
        RetrofitFactory.getConfig().setCommonParams(*getCommonParams())
        LoginRouter.gotoLogin(event.banned)
        DialogRouter.stop()
        DialogRouter.clearDialog()
        ChatRouter.logout()
        AppKit.instance.cleanAllActivity()
        PayClient.instance.uid = ""

    }

    @Subscribe
    fun onEvent(event: LoginEvent) {
        RetrofitFactory.getConfig().setCommonParams(*getCommonParams())
        loginApp()
        val loginBundle = event.bundle ?: Bundle()
        ARouter.getInstance()
            .build(LoginPath.PAGE_MAIN)
            .with(loginBundle)
            .navigation()
        PushManager.getFcmToken(event)
    }

    private fun loginApp() {
        RoomRouter.getRoomService()
        AiRouter.login(LoginRouter.getUserId())
        ConfigRouter.setup()
        PayClient.instance.uid = LoginRouter.getUserId()
        //        AppsFlyerLib.getInstance().setCustomerUserId(LoginRouter.getUserId());
        MediaKit.getInstance().userId = LoginRouter.getUserId()
    }

    private fun initLanguage() {
        LanguageController.supportedLanguages.putAll(
            if (DebugUtil.debug) {
                mapOf(
                    "en" to "English",
                    "ar" to "العربية",
                    "zh" to "中文（简体）",
                )
            } else {
                mapOf(
                    "en" to "English",
                )
            }
        )
        SkinKit.getInstance().setConfig(
            SkinKit.getInstance().getConfigBuilder().setRtl(LanguageController.isRtl())
                .setLang(LanguageController.getLanguage())
                .build()
        )
    }

    private fun initMedia() {
        MediaKit.getInstance().init(
            this,
            MediaConfig(
                getString(R.string.zego_app_id),
                getString(R.string.zego_app_sign)
            )
        )
        MediaKit.getInstance().setAudioParentFile(AppDirUtils.getCatchAudio())
    }

    private fun initHttp() {
        RetrofitProxy.BASEURL = LoginRouter.getRemoteConfig(RemoteConfigKey.BASE_URL)

        val config = HttpConfig()
            .setBaseUrl(LoginRouter.getRemoteConfig(RemoteConfigKey.BASE_URL))
            .setEcdhEncryption(true)
            .setErrorHandler(object : NetErrorHandler {
                override fun handle(t: Throwable, showToast: Boolean): NetError? {
                    NetToastUtils.showNetErrorToast(t, showToast)
                    return null
                }
            })
            .setCommonParams(*getCommonParams())
        val interceptor = mutableListOf(
            JSONInterceptor(),
            EncryptInterceptor()
        )
        if (DebugUtil.debug) {
            interceptor.add(HttpLogger.create().setLevel(HttpLoggingInterceptor.Level.BODY))
        }
        config.addInterceptor(*interceptor.toTypedArray())
        RetrofitFactory.init(config)
    }

    private fun getCommonParams(): Array<String> {
        val baseParams = arrayOf<String>(
            "lang",
            LanguageController.getLanguage(),
            "clientType",
            "android",
            "net",
            if (NetUtils.isWifi()) "1" else "0",
            "deviceName",
            PushManager.getDeviceName(),
            "osVersion",
            Build.VERSION.RELEASE,
            "appVersion",
            PackageUtils.getVersionName().split("_")[0],
            "innerAppVersion",
            PackageUtils.getVersionCode().toString(),
            "deviceId",
            DeviceUtils.getDeviceID()
        )
        val params = ArrayList<String>(listOf(*baseParams))
        if (LoginRouter.isLogin()) {
            params.add("regionId")
            params.add(LoginRouter.getRegionId())
            params.add("country")
            params.add(LoginRouter.getCountry())
        }
        return params.toTypedArray<String>()
    }

    override fun languageObserver(id: Int, vararg objects: Any?) {
        if (id == LanguageNotification.MSG_CHANGE_LANGUAGE) {
//            MatchHintUtils.clearHintCache();
            UtilKit.context = LanguageController.attachBaseContext(this)
            RetrofitFactory.getConfig().setCommonParams(*getCommonParams())
            SkinKit.getInstance().setConfig(
                SkinKit.getInstance().getConfigBuilder().setRtl(LanguageController.isRtl())
                    .setLang(LanguageController.getLanguage())
                    .build()
            )
            SkinKit.getInstance().notifiChange()
            //            GiftPanelCache.getInstance().invalid();
        }
    }

    private fun initSkin() {
        SkinKit.getInstance().register(this)
        SkinKit.getInstance().addListener { name, context, attrs ->
            val view: View? = null
            when (name) {

                else -> {}
            }
            view
        }
    }
}