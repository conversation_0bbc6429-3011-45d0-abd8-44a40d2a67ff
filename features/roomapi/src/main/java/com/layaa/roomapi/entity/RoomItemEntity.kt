package com.layaa.roomapi.entity

import android.os.Parcelable
import android.text.TextUtils
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

/**
 *<AUTHOR>
 *@date 2020/9/19
 *@des
 **/
@Parcelize
@Keep
class RoomItemEntity(

    var roomId: String = "",

    var ownerId: String = "",

    /**
     * 模式id
     */
    var roomMode: String = "",

    /**
     * 媒体类型
     */
    var channelType: String = "",

    /**
     * 房间名称
     */
    var roomName: String = "",

    var notice: String? = null,

    var roomNotice: String? = null,

    /**
     * 房间类型
     */
    var typeName: String? = null,

    /**
     * 房间人数
     */
    var roomUserNum: Int = 0,

    var photo: String? = null,

    var coverImageUrl: String? = null,

    /**
     * 在线用户数量
     */
    var userCount: Int = 0,
    var title: String? = null,
    var coverUrl: String? = null,
    var ownerCountry: String? = null,

    /**
     * 房间国家
     */
    var roomOwnerCountry: String? = null,

    var headWear: String? = null,

    var type: Int = 0,

    var banner: List<BannerEntity>? = null,

    var rankBanner: List<BannerEntity>? = null,

    var specialMark: String? = null,

    var specialMarkWidth: Int? = null,
    var specialMarkHeight: Int? = null,

    var lockStatus: Int? = null,
    var lockId: String? = null, //密码房lockId

    var seatCount: Int = 8,
    val themeData: Map<String, RoomThemePropInfo> = emptyMap(),

    var badgeUrl: String? = null

) : Parcelable {

    fun isLocked(): Boolean {
        return lockStatus == 1
    }

    override fun describeContents(): Int {
        return 0
    }

    override fun equals(other: Any?): Boolean {
        if (other == null) {
            return false
        }
        if (other is RoomItemEntity) {
            return TextUtils.equals(other.roomId, roomId)
        }
        return false
    }

    override fun toString(): String {
        return "roomId =$roomId;roomName=$roomName"
    }

    override fun hashCode(): Int {
        val result = roomId.hashCode()
        return result
    }
}