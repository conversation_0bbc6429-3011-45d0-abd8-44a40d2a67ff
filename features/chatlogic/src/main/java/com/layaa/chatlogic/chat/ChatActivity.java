package com.layaa.chatlogic.chat;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.biz.audio.IAudioPlayer;
import com.biz.audio.IAudioRecorder;
import com.biz.audio.record.RecordManager;
import com.hjq.permissions.Permission;
import com.layaa.accountapi.bean.UserBean;
import com.layaa.accountapi.event.BlackRemoteEvent;
import com.layaa.accountapi.event.DeleteFriendEvent;
import com.layaa.accountapi.event.FriendApplyEvent;
import com.layaa.accountapi.event.LogoutEvent;
import com.layaa.accountapi.friend.FriendConst;
import com.layaa.accountapi.login.LoginRouter;
import com.layaa.chatapi.ChatPath;
import com.layaa.chatapi.ChatRouter;
import com.layaa.chatapi.ImEvent;
import com.layaa.chatapi.observer.ChatObserver;
import com.layaa.chatapi.observer.EventMsgModifiedObserver;
import com.layaa.chatapi.observer.EventMsgRevokedObserver;
import com.layaa.chatapi.observer.EventObserver;
import com.layaa.chatlogic.BaseChatMvpActivity;
import com.layaa.chatlogic.ChatHeaderView;
import com.layaa.chatlogic.R;
import com.layaa.chatlogic.adapter.MsgListAdapter;
import com.layaa.chatlogic.bean.ChatsResult;
import com.layaa.chatlogic.bean.InviteData;
import com.layaa.chatlogic.bean.MsgItem;
import com.layaa.chatlogic.bean.RelationChange;
import com.layaa.chatlogic.controller.AudioPlayerManagerController;
import com.layaa.chatlogic.dialog.CopyPopView;
import com.layaa.chatlogic.event.ChatSendEvent;
import com.layaa.chatlogic.event.RefreshChatItemEvent;
import com.layaa.chatlogic.util.ChatEventUtil;
import com.layaa.chatlogic.util.ChatHelper;
import com.layaa.chatlogic.util.MessageUtils;
import com.layaa.chatlogic.viewholder.MsgAudioSelfViewHolder;
import com.layaa.chatlogic.viewholder.MsgAudioViewHolder;
import com.layaa.chatlogic.widget.RecordMsgNewPlayView;
import com.layaa.chatlogic.widget.RecordingNewAudioView;
import com.layaa.emote.PreEmojiView;
import com.layaa.emote.entity.EmoteEntity;
import com.layaa.emote.widget.LongPressListener;
import com.layaa.gameapi.GameConst;
import com.layaa.gameapi.GameRouter;
import com.layaa.gotoannotation.GotoRoute;
import com.layaa.im.bean.ChatType;
import com.layaa.im.bean.MessageStatus;
import com.layaa.im.bean.MessageTypedEnvelope;
import com.layaa.im.bean.data.AudioMessageData;
import com.layaa.im.bean.data.BaseMessageData;
import com.layaa.im.bean.data.CustomMessageData;
import com.layaa.im.bean.data.EmojiMessageData;
import com.layaa.im.bean.data.ImageMessageData;
import com.layaa.im.bean.data.TextMessageData;
import com.layaa.libnet.download.DownloadListener;
import com.layaa.libnet.download.DownloadManager;
import com.layaa.libui.GotoConst;
import com.layaa.libui.dialog.CommonTipDialog;
import com.layaa.libui.utils.photo.PhotoUtil;
import com.layaa.libui.widget.emoji.EmojiPagerView;
import com.layaa.libutils.AppDirUtils;
import com.layaa.libutils.ClickValidUtil;
import com.layaa.libutils.FileUtils;
import com.layaa.libutils.GsonUtils;
import com.layaa.libutils.JumpSettingUtil;
import com.layaa.libutils.KeyBoardUtil;
import com.layaa.libutils.UIUtils;
import com.layaa.libutils.module_log.LogUtils;
import com.layaa.libutils.module_thread.task.WeakHandler;
import com.layaa.libutils.toast.ToastUtils;
import com.layaa.permission.BizPermission;
import com.layaa.roomapi.router.RoomRouter;
import com.layaa.widget.xrecyclerview.XRecyclerView;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.json.JSONObject;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import static com.layaa.chatapi.ChatConst.KEY_POSITION;
import static com.layaa.chatapi.ChatConst.KEY_SOURCE;
import static com.layaa.chatapi.ChatConst.KEY_TO_ACTIVITY_DIALOG;
import static com.layaa.chatapi.ChatConst.KEY_TO_USER;

/**
 * <AUTHOR>
 * @date 2020-01-02
 * @des
 **/
@GotoRoute(host = GotoConst.Host.IM_CHAT, path = ChatPath.CHAT_PAGE)
@Route(path = ChatPath.CHAT_PAGE)
public class ChatActivity extends BaseChatMvpActivity<ChatPresenter> implements ChatContract.View, ChatObserver
        , EventObserver, EventMsgRevokedObserver, EventMsgModifiedObserver {
    private static final int SELECT_IMAGE = 0x1001;
    private static final int MSG_DOWNLOAD_PROGRESS = 0X1001;
    private static final int MSG_DOWNLOAD_SUC = 0X1002;
    private static final int MSG_DOWNLOAD_FAIL = 0x1003;
    private static final int MSG_ADD_ITME = 0x1004;
    private static final int MSG_SCROLL_TO = 0x1005;

    private int lastStatus = -1;

    private static final String TAG_SELECT_FRAGMENT = "tag_select_fragment";
    private static final String TAG_TAKE_FRAGMENT = "tag_take_fragment";
    private ConstraintLayout root_view;
    protected XRecyclerView msgList;
    protected View bottomView;
    protected EditText edit;
    protected TextView holdSpeakTv;
    protected ImageView changEditKeyBord;
    protected ImageView send;
    private UserBean toUser;
    private MsgListAdapter adapter;
    private String source;

    private EmojiPagerView emojiTabView;
    private PreEmojiView preEmojiView;

    private int screenHeight = UIUtils.getScreenHeight();
    private boolean mIsSoftKeyboardShowing = false;
    private LinearLayoutManager msgLayoutManager;
    private ImageView recorder;
    private ImageView emoji;
    private AudioPlayerManagerController inputPlayer;
    private IAudioPlayer.OnStateChangedListener onAudioPlayerListener;
    private RecordManager record;

    private InviteData inviteData;

    private List<String> needPlayList = new ArrayList<>();

    private RecyclerView.ViewHolder currentPlayHolder, lastPlayHolder;

    private RecordingNewAudioView recorderAudio;

    private ImageView addImage;
    private ChatHeaderView headerView;

    private TextView bannedTxt;

    private String userId;
    private boolean isDialog = false;

    private View intercept;

    private CharSequence copy_str = "";

    private MsgItem deleteMsgItem;
    private CopyPopView copy_view;
    private ClipboardManager clipboardManager;
    private TextView goSet, pushTips;

    private final PhotoUtil photoUtil = new PhotoUtil(this);

    @Override
    public void onRevokedMsg(@NonNull String messageId) {
        for (int i = 0; i < adapter.getList().size(); i++) {
            MsgItem item = adapter.getList().get(i);
            if (TextUtils.equals(item.message.getId(), messageId)) {
                presenter.delImMessage(item.message);
                adapter.remove(i);
                break;
            }
        }
    }

    @Override
    public void onModified(@NonNull MessageTypedEnvelope message) {
        for (int i = 0; i < adapter.getList().size(); i++) {
            MsgItem item = adapter.getList().get(i);
            if (TextUtils.equals(item.message.getId(), message.getId())) {
                item.message = message;
                adapter.notifyItemChanged(msgList, item);
                break;
            }
        }
    }


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        isDialog = getIntent().getBooleanExtra(KEY_TO_ACTIVITY_DIALOG, false);

        if (isDialog) {
            // 去掉标题
            requestWindowFeature(Window.FEATURE_NO_TITLE);

            // 隐藏 ActionBar
            if (getSupportActionBar() != null) {
                getSupportActionBar().hide();
            }
        }

        setTheme(isDialog ? R.style.ActivityDialogTheme : R.style.AppTheme_MontserratFont); // 这是没有标题栏的常规主题

        super.onCreate(savedInstanceState);
        if (isDialog) {
            // 获取窗口对象
            Window window = getWindow();
            if (window != null) {
                // 设置窗口背景为透明
                window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

                WindowManager.LayoutParams params = window.getAttributes();
                // 设置窗口从底部弹出
                params.gravity = Gravity.BOTTOM;
                // 设置窗口的宽度和高度
                params.width = WindowManager.LayoutParams.MATCH_PARENT;
                params.height = UIUtils.getPixels(587f);
                window.setAttributes(params);
            }
        }

        photoUtil.registerCallBack();
        photoUtil.setResultListener(new PhotoUtil.OnPictureResultListener() {
            @Override
            public void onSuccess(@NonNull String path) {
                if (!TextUtils.isEmpty(path)) {
                    presenter.sendImage(toUser, path);
                }
            }

            @Override
            public void onFail(@NonNull String message) {
                if (!message.isEmpty()) {
                    ToastUtils.show(message);
                }
            }
        });
    }

    @Override
    protected int getLayoutId() {
        return isDialog ? R.layout.activity_chat_dialog : R.layout.activity_chat;
    }

    private String parseRoomId() {
        String userId = null;
        Object obj = getIntent().getExtras().get(KEY_TO_USER);
        if (obj != null) {
            userId = obj.toString();
        }
        if (TextUtils.isEmpty(userId) && getIntent().getExtras() != null) {
            obj = getIntent().getExtras().get(KEY_TO_USER);
        }
        if (obj != null) {
            userId = obj.toString();
        }
        return userId;
    }

    @Override
    protected void initData() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        setNavigationBarColor(Color.WHITE);

        ChatEventUtil.registerChat(TAG, this);
        ChatEventUtil.registerEvent(TAG, this);
        ChatEventUtil.registerRevokedEvent(TAG, this);
        ChatEventUtil.registerModifiedEvent(TAG, this);
        adapter = new MsgListAdapter(this);

        userId = parseRoomId();

        source = getIntent().getStringExtra(KEY_SOURCE);

        if (TextUtils.isEmpty(userId)) {
            return;
        }

//        StatisticsUtils.with("4-1").addEventInfo("source", TextUtils.isEmpty(source) ? "" : source).addUserInfo("to_uid", TextUtils.isEmpty(userId) ? "" : userId).send();

        MessageUtils.updateUnreadCount(userId, 0);

        try {
            toUser = LoginRouter.getAccountInfo(userId);
            ChatHelper.getInstance().setCurrentUser(toUser);
        } catch (Exception e) {
            LogUtils.e(e);
        }

        MessageUtils.initSendQuizCount(userId);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        //推送过来的
        String tempUid = parseRoomId();

        if (TextUtils.isEmpty(tempUid)) {
            return;
        }

        presenter.findLocalMsgList(ChatType.SINGLE, tempUid);

        boolean sameUser = TextUtils.equals(tempUid, userId);
        if (!sameUser) {
            //换了一个人
            userId = tempUid;
        }


        if (sameUser) {
            if (toUser != null) {
                headerView.updateOnlineStatus(toUser);
            }
            return;
        }


        this.userId = tempUid;

        //id变更
        MessageUtils.updateUnreadCount(userId, 0);

        try {
            adapter.clear();
            toUser = LoginRouter.getAccountInfo(userId);
            ChatHelper.getInstance().setCurrentUser(toUser);
        } catch (Exception e) {
            LogUtils.e(e);
        }
        if (toUser != null) {
            headerView.updateOnlineStatus(toUser);
        }

        headerView.setUserId(userId);

        headerView.setUserInfo(toUser);
        //更新好友关系
        adapter.setToUser(toUser);

        //从服务器获取user info
        presenter.getUserInfo(userId);
        showLoading();
//        presenter.findLocalMsgList(PhotonIMMessage.SINGLE, userId, "");
    }

    private CommonTipDialog commonPerDialog;

    @Override
    protected void initEvent() {

        //从服务器获取user info
        presenter.getUserInfo(userId);

        emojiTabView.setTabBackground(Color.parseColor("#FF1E735F"));
        emojiTabView.setLongPressListener(new LongPressListener() {
            @Override
            public void onPressDown(@NonNull View view, @NonNull EmoteEntity entity) {
//                if (!entity.isDynamic()) {
//                    return;
//                }
//                preEmojiView.setVisibility(View.VISIBLE);
//                int[] location = new int[2];
//                view.getLocationOnScreen(location);
//                if (SkinKit.getInstance().isRtl()) {
//                    preEmojiView.setTran(location[0] + preEmojiView.getBgWidth() - (preEmojiView.getBgWidth() - view.getWidth()) / 2 - UIUtils.getScreenWidth(), location[1] + UIUtils.getPixels(12));
//                } else {
//                    preEmojiView.setTran(location[0] - (preEmojiView.getBgWidth() - view.getWidth()) / 2, location[1] + UIUtils.getPixels(12));
//                }
//                preEmojiView.setData(entity);
            }

            @Override
            public void onPressUp(@NonNull View view, @NonNull EmoteEntity entity) {
                preEmojiView.setVisibility(View.GONE);
                preEmojiView.release(null);
            }

            @Override
            public void onCancel(@NonNull View view, @NonNull EmoteEntity emoteEntity) {
                preEmojiView.setVisibility(View.GONE);
                preEmojiView.release(null);
            }

        });

        emojiTabView.setShortPressListener((view, entity) -> {
            presenter.sendEmoji(toUser, entity);
        });

        adapter.setOnItemClickListener(item -> {
            if (item == null || item.message == null) {
                return;
            }
            BaseMessageData data = item.message.getContent().getContent();
            if (data instanceof CustomMessageData) {
                CustomMessageData customMessageData = (CustomMessageData) data;
                JSONObject jsonData;
                if (customMessageData.getData() == null) {
                    jsonData = new JSONObject();
                } else {
                    jsonData = new JSONObject(customMessageData.getData());
                }
                if (customMessageData.getEventId() == ImEvent.CustomP2pMsg.TYPE_AGENCY_INVITE) {
                    if (TextUtils.equals(item.message.getFrom(), LoginRouter.getUserId())) {
                        return;
                    }
                    try {
                        jsonData.put("nickname", toUser.getNickName());
                    } catch (Exception e) {
                        LogUtils.e(e);
                    }
                    presenter.checkAgencyInvite(jsonData);
                } else if (customMessageData.getEventId() == ImEvent.CustomP2pMsg.TYPE_LIVE_INVITE) {
                    RoomRouter.gotoRoom(jsonData.optString("roomId"), "2");
                }
            }
        });

       /* edit.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (event == null) {
                    return false;
                }
                if (event.getKeyCode() == KeyEvent.KEYCODE_ENTER) {
                    return true;
                }
                return false;
            }
        });*/


        edit.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {

                if (s == null || s.length() == 0) {
                    if (holdSpeakTv.getVisibility() == View.GONE) {
                        recorder.setVisibility(View.VISIBLE);
                        send.setVisibility(View.GONE);
                    }
                } else {
                    recorder.setVisibility(View.GONE);
                    send.setVisibility(View.VISIBLE);
                }
            }
        });

        bannedTxt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isFinishing()) {
                    return;
                }
                if (!ClickValidUtil.clickValidShort()) {
                    return;
                }
            }
        });

        adapter.setToUser(toUser);

        adapter.setOnClickLongListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                MsgItem item = (MsgItem) v.getTag();
                if (item == null || item.message == null) {
                    return false;
                }
                presenter.delImMessage(item.message);
//                int[] location = new int[2];
//                v.getLocationOnScreen(location);
//                Rect rect = new Rect();
//                rect.left = location[0];
//                rect.top = location[1];
//                rect.right = rect.left + v.getMeasuredWidth();
//                rect.bottom = rect.top + v.getMeasuredHeight();
//                float copy_width = UIUtils.getPixels(87F);
//                if (item.message.messageType == MessageType.TEXT) {
//                    copy_str = item.message.content;
//                    copy_view.showCopy(root_view,
//                            rect.left + v.getMeasuredWidth() / 2 - (int) copy_width / 2, rect.top, Gravity.TOP | Gravity.START);
//                } else {
//                    deleteMsgItem = item;
//                    copy_view.showDelete(root_view,
//                            rect.left + v.getMeasuredWidth() / 2 - (int) copy_width / 2,
//                            rect.top,
//                            Gravity.TOP | Gravity.START);
//                }
                return true;
            }
        });

        msgList.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (lastStatus == newState) {
                    return;
                }
                lastStatus = newState;
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    //停止滚动
                } else {
                    //开始滚动
                   /* if (copy_view != null) {
                        copy_view.dismiss();
                    }*/
                }
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
            }
        });

        adapter.setOnAgentTransferClickListener(() -> {
//            Bundle bundle = new Bundle();
//            bundle.putInt(WalletConst.SHOW_ACCOUNT_TYPE, WalletConst.WALLET_TYPE_DEALER);
//            WalletRouter.gotoBillingDetail(bundle);
            return null;
        });

        downloadTask = new DownloadTask(this);
        msgLayoutManager = new LinearLayoutManager(this, RecyclerView.VERTICAL, false);
        msgList.setLayoutManager(msgLayoutManager);
        msgList.setAdapter(adapter);

        msgList.setItemAnimator(null);
        msgList.setHasFixedSize(false);

        recorderAudio.setToUser(toUser);
        recorderAudio.setIsDialog(isDialog);

        addImage.setOnClickListener(v -> {
            if (!ClickValidUtil.clickValidShort()) {
                return;
            }
            if (toUser != null && toUser.isBlacked()) {
                ToastUtils.show(getString(R.string.my_block_message_rejected));
                return;
            }

            photoUtil.startImage();
        });


        headerView.setOnBackListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ChatActivity.this.finish();
            }
        });

        send.setOnClickListener(v -> {
            if (!ClickValidUtil.clickValidShort()) {
                return;
            }
            presenter.sendText(toUser, edit.getText().toString().trim());
        });

        intercept.setOnClickListener(v -> {
            if (!ClickValidUtil.clickValidShort()) {
                return;
            }
            emoji.setImageResource(R.drawable.icon_chat_emoji_btn);
            emojiTabView.setVisibility(View.GONE);
            intercept.setVisibility(View.GONE);
            KeyBoardUtil.hideSoftKeyboardNotAlways(ChatActivity.this);
            setBottomViewParams(0);
            if (TextUtils.isEmpty(edit.getText().toString().trim())) {
                recorder.setVisibility(View.VISIBLE);
                send.setVisibility(View.GONE);
            } else {
                recorder.setVisibility(View.GONE);
                send.setVisibility(View.VISIBLE);
            }

        });

        emoji.setOnClickListener(v -> {
            if (!ClickValidUtil.clickValidShort()) {
                return;
            }
            if (toUser != null && toUser.isBlacked()) {
                ToastUtils.show(R.string.my_block_message_rejected);
                return;
            }
            // recorder.setVisibility(View.GONE);
            if (emojiTabView.getVisibility() == View.VISIBLE) {
                emoji.setImageResource(R.drawable.icon_chat_emoji_btn);
                emojiTabView.setVisibility(View.GONE);
                intercept.setVisibility(View.GONE);
                setBottomViewParams(0);
                KeyBoardUtil.showSoftKeyboard(ChatActivity.this, edit);
            } else {
                emoji.setImageResource(R.drawable.icon_emoji_keyboard);
                emojiTabView.setVisibility(View.VISIBLE);
                emojiTabView.load(LoginRouter::canUseVipEmoji, false);
                intercept.setVisibility(View.VISIBLE);
                if (mIsSoftKeyboardShowing) {
                    KeyBoardUtil.hideSoftKeyboardNotAlways(ChatActivity.this);
                }
            }
        });

        adapter.setLudoInviteListener(v -> {

            MsgItem msg = (MsgItem) v.getTag();
            if (msg == null || msg.message == null) {
                return;
            }
           /* if (ChatHelper.isBan()) {
                ToastUtils.show(LanguageController.getInstance().getString(R.string.user_has_been_banned));
                return;
            }*/
            presenter.acceptLudo(msg.message);

        });

        adapter.setOnImageListener(v -> {
            Object o = v.getTag(R.id.tag_id_avatar);
            if (o instanceof MsgItem && ((MsgItem) o).message.getContent().getContent() instanceof ImageMessageData) {
                MsgItem item = (MsgItem) o;
                ChatRouter.gotoPreviewImage(
                        ((ImageMessageData) item.message.getContent().getContent()).getUrl(),
                        ChatHelper.getInstance().getAllList()
                );
            }
        });

        adapter.setOnAudioClickListener(v -> {
            if (v.getTag() instanceof MsgItem) {
                if (TextUtils.equals(RecordMsgNewPlayView.PLAYING, (String) v.getTag(R.id.item_status))) {
                    //正在播放，点击结束播放
                    stopPlay();

                    if (currentPlayHolder instanceof MsgAudioSelfViewHolder) {
                        ((MsgAudioSelfViewHolder) currentPlayHolder).stop();
                    }

                    if (currentPlayHolder instanceof MsgAudioViewHolder) {
                        ((MsgAudioViewHolder) currentPlayHolder).stop();
                    }
                    return;
                }

                if (lastPlayHolder instanceof MsgAudioSelfViewHolder) {
                    ((MsgAudioSelfViewHolder) lastPlayHolder).stop();
                }

                if (lastPlayHolder instanceof MsgAudioViewHolder) {
                    ((MsgAudioViewHolder) lastPlayHolder).stop();
                }

                lastPlayHolder = null;
                MsgItem item = (MsgItem) v.getTag();
                MessageTypedEnvelope message = item.message;

                if (message instanceof MessageTypedEnvelope.AudioMessageEnvelope) {
                    MessageTypedEnvelope.AudioMessageEnvelope audioMessageEnvelope = (MessageTypedEnvelope.AudioMessageEnvelope) message;
                    String fileName;
                    String localFile = audioMessageEnvelope.getContent().getContent().getLocalFile();
                    if (TextUtils.isEmpty(localFile)) {
                        Uri uri = Uri.parse(audioMessageEnvelope.getContent().getContent().getUrl());
                        fileName = uri.getLastPathSegment();
                        audioMessageEnvelope.getContent().getContent().setLocalFile(AppDirUtils.getCatchAudio() + AppDirUtils.SYSTEM_SEPARATOR + fileName);
                    } else {
                        fileName = new File(localFile).getName();
                    }

                    localFile = audioMessageEnvelope.getContent().getContent().getLocalFile();
                    currentPlayHolder = msgList.findContainingViewHolder(v);
                    if (!FileUtils.isFilePathExist(localFile)) {
                        needPlayList.add(localFile);
                        DownloadManager.getInstance().startAsynDownload(audioMessageEnvelope.getContent().getContent().getUrl(),
                                new File(localFile).getParent(), fileName, downloadTask);
                    } else {
                        play(localFile);
                    }
                }

            }
        });
        adapter.setOnAudioProgressListener(progress -> {
            LogUtils.d("拖动到" + progress + "秒");
            seekPlay(progress + "");
        });

        adapter.setOnFailListener(v -> {
            if (v.getTag() instanceof MsgItem) {
                MsgItem item = (MsgItem) v.getTag();
                resendMsg(item);
            }
        });

        recorderAudio.setApplyPermissionListener(v -> applyAudioPermission());


        recorderAudio.setOnRecordListener(new RecordingNewAudioView.OnRecordListener() {
            @Override
            public void onStart() {
                if (isFinishing()) {
                    return;
                }
                stopAudioPlay();
                recorder();
            }

            @Override
            public void onEnd() {
                if (isFinishing()) {
                    return;
                }
                stopRecorder();
            }

            @Override
            public void onCancel() {
                if (isFinishing()) {
                    return;
                }
                cancelRecorder();
            }
        });

        msgList.setLoadingMoreEnabled(false);
        msgList.setLoadingListener(new XRecyclerView.LoadingListener() {
            @Override
            public void onRefresh() {
                if (adapter.getList().size() == 0) {
                    msgList.refreshComplete();
                    msgList.setPullRefreshEnabled(false);
                    return;
                }
                MsgItem item = adapter.getList().get(0);
                presenter.findLocalMsgList(ChatType.SINGLE, userId);
            }

            @Override
            public void onLoadMore() {

            }
        });

        headerView.setUserId(userId);
        headerView.setUserInfo(toUser);
        //更新好友关系
        adapter.setToUser(toUser);

        recorderAudio.setView(holdSpeakTv);
//        initEmojiView();

        showLoading();
        presenter.findLocalMsgList(ChatType.SINGLE, userId);

        adapter.setHeadCount(msgList.getHeaders_includingRefreshCount());

        if (toUser != null) {
            headerView.updateOnlineStatus(toUser);
        }

        copy_view.setCopyListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                clipboardManager.setPrimaryClip(ClipData.newPlainText(null, copy_str.toString()));
                copy_view.dismiss();
                ToastUtils.show(getString(R.string.family_home_id_copy_success));
            }
        });

        copy_view.setDeleteListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });


        copy_view.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                copy_str = "";
            }
        });
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {

        if (keyCode == KeyEvent.KEYCODE_BACK) {
            Fragment fragment = getSupportFragmentManager().findFragmentByTag(TAG_TAKE_FRAGMENT);
            if (fragment != null) {
                getSupportFragmentManager().beginTransaction()
                        .setCustomAnimations(0, R.anim.anim_fragment_top_bottom)
                        .remove(fragment).commitAllowingStateLoss();
                return true;
            }
            fragment = getSupportFragmentManager().findFragmentByTag(TAG_SELECT_FRAGMENT);

            if (fragment != null) {
                getSupportFragmentManager().beginTransaction()
                        .setCustomAnimations(0, R.anim.anim_fragment_top_bottom)
                        .remove(fragment).commitAllowingStateLoss();
                return true;
            }
        }
        return super.onKeyUp(keyCode, event);
    }

    private void resendMsg(MsgItem item) {
        if (item == null || item.message == null) {
            return;
        }

        if (item.message.getContent().getContent() instanceof TextMessageData) {
            presenter.sendText(toUser, ((TextMessageData) item.message.getContent().getContent()).getText());
        } else if (item.message.getContent().getContent() instanceof AudioMessageData) {
            presenter.resendAudio(toUser, item);
        } else if (item.message.getContent().getContent() instanceof ImageMessageData) {
            presenter.resendImage(toUser, item);
        } else if (item.message.getContent().getContent() instanceof EmojiMessageData) {
            presenter.resendEmoji(toUser, item);
        }
    }

    @Override
    public void showRemoveBlackDialog(String remoteUid) {
        LoginRouter.showRemoveBlackDialog(remoteUid, this);
    }

    @Override
    public void updateMessageStatus(MsgItem item, com.layaa.im.bean.MessageStatus status) {
        item.message.setStatus(status);
        adapter.notifyItemChanged(msgList, item);
        EventBus.getDefault().post(new ChatSendEvent().setItem(item));
    }

    private void play(String path) {
        if (TextUtils.isEmpty(path)) {
            return;
        }

        if (inputPlayer == null) {
            initAudioPlayer();
        }

        if (lastPlayHolder != null && lastPlayHolder == currentPlayHolder) {
            inputPlayer.stopPlayer();
            lastPlayHolder = null;
        } else {
            inputPlayer.startPlayer(path);
        }

    }

    private void stopPlay() {

        if (inputPlayer == null) {
            return;
        }

        if (lastPlayHolder != null && lastPlayHolder == currentPlayHolder) {
            inputPlayer.stopPlayer();
            lastPlayHolder = null;
        }

    }

    private void seekPlay(String seek) {

        if (inputPlayer == null) {
            return;
        }

        if (lastPlayHolder != null && lastPlayHolder == currentPlayHolder) {
            inputPlayer.seekToPlayer(Long.parseLong(seek));
        }

    }

    private void initAudioPlayer() {

        if (inputPlayer == null) {
            inputPlayer = AudioPlayerManagerController.getInstance();
            onAudioPlayerListener = new IAudioPlayer.OnStateChangedListener() {
                @Override
                public void onStart() {

                    lastPlayHolder = currentPlayHolder;
                    if (currentPlayHolder instanceof MsgAudioViewHolder) {
                        int curPosition = ((MsgAudioViewHolder) currentPlayHolder).play();
                        if (curPosition == adapter.getList().size() - 1 + adapter.getHeadCount()) {
                            EventBus.getDefault().post(new RefreshChatItemEvent(getIntent().getIntExtra(KEY_POSITION, 0)));
                        }
                    }

                    if (currentPlayHolder instanceof MsgAudioSelfViewHolder) {
                        ((MsgAudioSelfViewHolder) currentPlayHolder).play();
                    }
                }

                @Override
                public void onStop() {

                }

                @Override
                public void onFinish() {
                    LogUtils.i(TAG, "onFinish");
                    if (currentPlayHolder instanceof MsgAudioSelfViewHolder) {
                        ((MsgAudioSelfViewHolder) currentPlayHolder).stop();
                    }

                    if (currentPlayHolder instanceof MsgAudioViewHolder) {
                        ((MsgAudioViewHolder) currentPlayHolder).stop();
                    }

//                    playerNext();
                }

                @Override
                public void onComplete() {

                }

                @Override
                public void onError(int errCode) {
                    if (currentPlayHolder instanceof MsgAudioSelfViewHolder) {
                        ((MsgAudioSelfViewHolder) currentPlayHolder).stop();
                    }

                    if (currentPlayHolder instanceof MsgAudioViewHolder) {
                        ((MsgAudioViewHolder) currentPlayHolder).stop();
                    }
                }
            };

            inputPlayer.addListener(onAudioPlayerListener);
        }
    }

    /*private void playerNext() {
        if (currentPlayHolder == null) {
            return;
        }

        int position = currentPlayHolder.getAdapterPosition();
        if (position + 1 == adapter.getItemCount() + msgList.getHeaders_includingRefreshCount()) {
            //最后一条
            return;
        }
        position += 1
        if (position >= msgLayoutManager.findFirstVisibleItemPosition()
                && position <=)
    }*/

    private long currentRecordTime = 0;

    private void cancelRecorder() {
        if (record == null) {
            return;
        }
        record.cancelRecord();
    }

    private void stopRecorder() {
        if (record == null) {
            return;
        }
        record.stopRecord();
    }

    private void recorder() {
        if (record == null) {
            initRecord();
        }

        String recorderName = AppDirUtils.getCatchAudio() + AppDirUtils.SYSTEM_SEPARATOR + System.currentTimeMillis() + ".opus";
        record.startRecord(recorderName);
    }

    private IAudioRecorder.OnStateChangeListener onRecordListener;

    private void initRecord() {
        if (record != null) {
            return;
        }
        record = RecordManager.getInstance();
        onRecordListener = new IAudioRecorder.OnStateChangeListener() {
            @Override
            public void onStart() {
                if (isFinishing()) {
                    return;
                }
                currentRecordTime = System.currentTimeMillis();
            }

            @Override
            public void onFakeStop(File handleFile, String fileName, long duration) {

            }

            @Override
            public void onRealStop(String fileName) {
                if (isFinishing()) {
                    return;
                }
                LogUtils.i(TAG, fileName);
                if (!recorderAudio.endRecorder()) {
                    ToastUtils.show(getString(R.string.record_sort));
                    return;
                }
                presenter.sendAudio(toUser, fileName, (System.currentTimeMillis() - currentRecordTime + 500L) / 1000L);
            }

            @Override
            public void onCancel() {
                if (recorderAudio != null) {
                    recorderAudio.endRecorder();
                }
            }

            @Override
            public void onComplete(File audioFile) {

            }

            @Override
            public void onError(int errCode) {

                LogUtils.i(TAG, "onError ==" + errCode);

                if (isFinishing()) {
                    return;
                }

                if (!recorderAudio.endRecorder()) {
                    //小于1s提示太短了
                    ToastUtils.show(getString(R.string.record_sort));
                    return;
                }
                //超过1s并录音失败 直接去申请权限
                showPerDialog();
            }

            @Override
            public void onRealData(String key, byte[] buffer) {

            }
        };
        record.addListener(onRecordListener);
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (mIsSoftKeyboardShowing) {
            mIsSoftKeyboardShowing = false;
            KeyBoardUtil.hideSoftKeyboardNotAlways(ChatActivity.this);
            setBottomViewParams(0);
        }
    }

    @Override
    public void finish() {

        ChatHelper.getInstance().clear();
        super.finish();
//        APngImageLoaderUtils.cancleTask();
        MessageUtils.updateUnreadCount(userId, 0);
    }

    @Override
    protected void onDestroy() {
        ChatHelper.getInstance().clear();
        if (headerView != null) {
            headerView.onDestroy();
        }
        super.onDestroy();
        EventBus.getDefault().unregister(this);
        MessageUtils.saveSendQuizCount(userId);

        stopAudioPlay();

        if (msgList != null && scrollTask != null) {
            msgList.removeCallbacks(scrollTask);
        }

        //离开聊天页 关闭声网
        ChatEventUtil.unregisterChat(TAG);
        ChatEventUtil.unregisterEvent(TAG);
        ChatEventUtil.unregisterRevokedEvent(TAG);
        ChatEventUtil.unModifiedEvent(TAG);

        if (record != null) {
            record.removeListener(onRecordListener);
            record.stopRecord();
        }

        if (inputPlayer != null) {
            inputPlayer.removeListener(onAudioPlayerListener);
            inputPlayer.stopPlayer();
        }

        if (commonPerDialog != null && commonPerDialog.isShowing()) {
            commonPerDialog.dismiss();
        }

        if (recorderAudio != null) {
            recorderAudio.endRecorder();
        }


        if (handler != null) {
            handler.removeMessages(MSG_SCROLL_TO);
            handler.removeMessages(MSG_ADD_ITME);
            handler.removeMessages(MSG_DOWNLOAD_FAIL);
            handler.removeMessages(MSG_DOWNLOAD_SUC);
            handler.removeMessages(MSG_DOWNLOAD_PROGRESS);
            handler.removeCallbacksAndMessages(null);
        }

        if (copy_view != null) {
            copy_view.dismiss();
        }

        photoUtil.unregisterCallBack();

    }

    @Override
    public void updateInput(String txt) {
        edit.setText(txt);
    }

    @Override
    public void refreshList(ChatsResult result) {

        dismissLoading();
        if (result == null) {
            msgList.setPullRefreshEnabled(false);
            msgList.refreshComplete();
            return;
        }

        if (!result.isHasLast()) {
            msgList.setPullRefreshEnabled(false);
            msgList.refreshComplete();
        }

        if (result.isRefresh()) {
            adapter.clear();
        }

        adapter.addListForIndex(0, msgList.getHeaders_includingRefreshCount(), result.getList());
        msgList.refreshComplete();

        if (result.isRefresh()) {
            scrollToBottom();
        }
    }

    @Override
    public void updateUserInfo(UserBean bean) {
        if (bean == null) {
            return;
        }
        toUser = bean;

        ChatHelper.getInstance().setCurrentUser(bean);

        headerView.setUserInfo(toUser);
        recorderAudio.setToUser(toUser);
        adapter.setToUser(toUser);
        headerView.updateOnlineStatus(toUser);
        recorderAudio.setVisibility(View.VISIBLE);
        LogUtils.i("friendHint", "updateUserInfo");

        LoginRouter.updateUser(bean);
        if (ChatHelper.isBan()) {
            bannedTxt.setVisibility(View.VISIBLE);
        } else {
            bannedTxt.setVisibility(View.GONE);
        }
        /*if (needUpdateRelationStatus) {
            //本地好友状态和服务器上不一样 刷新下
            //更新聊天列表状态
            int index = msgList.getHeaders_includingRefreshCount();
            for (MsgItem item : adapter.getList()) {
                if (item == null || item.message == null
                        || item.message.customArg1 != ImEvent.CustomP2pMsg.TYPE_FRIEND) {
                    index++;
                    continue;
                }
                if (index >= msgLayoutManager.findFirstVisibleItemPosition() && index <= msgLayoutManager.findLastVisibleItemPosition()
                        + msgList.getHeaders_includingRefreshCount()) {
                    adapter.notifyItemChanged(index);
                    break;
                }
                index++;
            }
        }*/
    }

    private final WeakHandler handler = new WeakHandler(Looper.getMainLooper(), new Handler.Callback() {
        @Override
        public boolean handleMessage(@NonNull Message msg) {
            if (isFinishing()) {
                return false;
            }
            switch (msg.what) {
                case MSG_SCROLL_TO:
                    scrollToBottom();
                    break;
                case MSG_ADD_ITME:
                    MsgItem item = (MsgItem) msg.obj;
                    adapter.add(item);
                    scrollToBottom();
                    break;
                case MSG_DOWNLOAD_PROGRESS:
                    /*if (currentViewHolder!=null){
                        currentViewHolder.progress(msg.arg1);
                    }*/
                    break;
                case MSG_DOWNLOAD_FAIL:
                   /* currentViewHolder.setProgressShow(false);
                    currentViewHolder = null;*/
//                    ToastUtils.show("下载失败");
                    break;
                case MSG_DOWNLOAD_SUC:
                   /* currentViewHolder.setProgressShow(false);
                    matchView.startMatch();
                    currentViewHolder = null;*/
//                    ToastUtils.show("音频下载成功");
                    String path = (String) msg.obj;
                    Iterator<String> iterator = needPlayList.iterator();
                    while (iterator.hasNext()) {
                        String data = iterator.next();
                        if (TextUtils.equals(data, path)) {
                            play(path);
                            iterator.remove();
                            break;
                        }
                    }
                    break;
                default:
                    break;
            }
            return false;
        }
    });


    private DownloadTask downloadTask;

    private void stopAudioPlay() {
        //停止音频的播放
        if (currentPlayHolder instanceof MsgAudioViewHolder) {
            ((MsgAudioViewHolder) currentPlayHolder).stop();
        }

        if (currentPlayHolder instanceof MsgAudioSelfViewHolder) {
            ((MsgAudioSelfViewHolder) currentPlayHolder).stop();
        }

        if (lastPlayHolder instanceof MsgAudioViewHolder) {
            ((MsgAudioViewHolder) lastPlayHolder).stop();
        }

        if (lastPlayHolder instanceof MsgAudioSelfViewHolder) {
            ((MsgAudioSelfViewHolder) lastPlayHolder).stop();
        }
/*
        if (inputPlayer != null) {
            inputPlayer.stopPlayer();
        }*/
    }

    @Override
    protected void initView() {
        root_view = findViewById(R.id.root_view);
        msgList = findViewById(R.id.msg_list);
        bottomView = findViewById(R.id.bottom_view);
        edit = findViewById(R.id.edit);
        send = findViewById(R.id.send);
        recorder = findViewById(R.id.recorder);
        emoji = findViewById(R.id.emoji);
        bannedTxt = findViewById(R.id.banned_txt);
        holdSpeakTv = findViewById(R.id.holdSpeakTv);
        changEditKeyBord = findViewById(R.id.changEditKeyBord);

        goSet = findViewById(R.id.go_set);
        pushTips = findViewById(R.id.tips_push);

        recorderAudio = findViewById(R.id.recorder_audio);
        addImage = findViewById(R.id.add_image);
        intercept = findViewById(R.id.intercept);

        headerView = findViewById(R.id.title_bar);
       /* quizChooseDialog = new QuizChooseDialog(this);
        quizChooseDialog.setOnSendClick(imMessage -> {
            if (presenter != null && toUser != null) {
                presenter.sendQuizAskMessage(imMessage, toUser);
                AgentMessageManager.getInstance().cancelRoundOneJob(userId, "quiz ask send");
            }
            return null;
        });*/

        emojiTabView = findViewById(R.id.emoji_tab);
        preEmojiView = findViewById(R.id.pre_emoji);

        copy_view = new CopyPopView(this);
        clipboardManager = (ClipboardManager) getSystemService(CLIPBOARD_SERVICE);

        headerView.post(new Runnable() {
            @Override
            public void run() {
                if (isDialog) {
                    headerView.setPadding(0, 0, 0, 0);
                }
            }
        });

        pushTips.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!ClickValidUtil.clickValidShort()) {
                    return;
                }
                JumpSettingUtil.goNotificationPage();
                goSet.setVisibility(View.GONE);
                pushTips.setVisibility(View.GONE);
//                StatisticsUtils.with("5-19").addEventInfo("scenes", "2").send();
            }
        });

//        if (NotificationManagerCompat.from(this).areNotificationsEnabled()) {
//            goSet.setVisibility(View.GONE);
//            pushTips.setVisibility(View.GONE);
//        } else {
//            goSet.setVisibility(View.VISIBLE);
//            pushTips.setVisibility(View.VISIBLE);
////            StatisticsUtils.with("5-18").addEventInfo("scenes", "2").send();
//        }

        //发送录音
        recorder.setOnClickListener(v -> {
            if (!ClickValidUtil.clickValidShort()) {
                return;
            }
            if (toUser != null && toUser.isBlacked()) {
                ToastUtils.show(R.string.my_block_message_rejected);
                return;
            }

            if (emojiTabView.getVisibility() == View.VISIBLE) {
                emojiTabView.setVisibility(View.GONE);
            }
            intercept.setVisibility(View.GONE);
            setBottomViewParams(0);
            KeyBoardUtil.hideSoftKeyboardNotAlways(ChatActivity.this);
            scrollToBottom();

            edit.setVisibility(View.GONE);
            emoji.setVisibility(View.GONE);
            recorder.setVisibility(View.GONE);
            send.setVisibility(View.GONE);
            holdSpeakTv.setVisibility(View.VISIBLE);
            changEditKeyBord.setVisibility(View.VISIBLE);
            intercept.setVisibility(View.GONE);
            edit.setText("");
            emoji.setImageResource(R.drawable.icon_chat_emoji_btn);

        });

        changEditKeyBord.setOnClickListener(v -> {
            edit.setVisibility(View.VISIBLE);
            emoji.setVisibility(View.VISIBLE);
            recorder.setVisibility(View.VISIBLE);
            send.setVisibility(View.GONE);
            holdSpeakTv.setVisibility(View.GONE);
            changEditKeyBord.setVisibility(View.GONE);
            edit.setText("");
        });

        setImageColor();

        ViewCompat.setOnApplyWindowInsetsListener(bottomView, (v, insets) -> {
            if (isShowFragment()) {
                return insets;
            }
            boolean isKeyboardShowing = insets.isVisible(WindowInsetsCompat.Type.ime());
            int keyboardHeight = insets.getInsets(WindowInsetsCompat.Type.ime()).bottom;
            int navigationBarHeight = insets.getInsets(WindowInsetsCompat.Type.navigationBars()).bottom;
            if (mIsSoftKeyboardShowing && !isKeyboardShowing
                    || !mIsSoftKeyboardShowing && isKeyboardShowing) {
                if (isKeyboardShowing) {
                    emoji.setImageResource(R.drawable.icon_chat_emoji_btn);
                    emojiTabView.setVisibility(View.GONE);
                    intercept.setVisibility(View.VISIBLE);
                    setBottomViewParams(keyboardHeight - navigationBarHeight);
                    scrollToBottom();
                } else {
                    intercept.setVisibility(View.GONE);
                    setBottomViewParams(0);
                    if (edit.getText().length() > 0) {
                        recorder.setVisibility(View.GONE);
                        send.setVisibility(View.VISIBLE);
                    } else {
                        if (holdSpeakTv.getVisibility() == View.GONE) {
                            recorder.setVisibility(View.VISIBLE);
                            send.setVisibility(View.VISIBLE);
                            send.setVisibility(View.GONE);
                        }
                    }

                }
                mIsSoftKeyboardShowing = isKeyboardShowing;
            }
            return insets;
        });
    }

    private void showPerDialog() {
        if (isFinishing()) {
            return;
        }
        if (commonPerDialog == null) {
            commonPerDialog = new CommonTipDialog(mContext);
            commonPerDialog.setConfirmTxt(getString(R.string.settings_page_title));
            commonPerDialog.setTitle(getString(R.string.need_to_access));
            commonPerDialog.setContent(getString(R.string.need_record_permission));
            commonPerDialog.setConfirmCallback(() -> {
                JumpSettingUtil.gotoPermissionManager(mContext);
                return null;
            });
        }
        if (isFinishing()) {
            return;
        }
        commonPerDialog.show();
    }

    private static class DownloadTask implements DownloadListener {

        private WeakReference<ChatActivity> weakReference;

        public DownloadTask(ChatActivity fragment) {
            weakReference = new WeakReference<>(fragment);
        }

        private boolean isValid() {
            return weakReference != null && weakReference.get() != null && weakReference.get().isValid();
        }

        @Override
        public void onDownloadSuccess(String url, String savePath, long totalSize) {

            if (!isValid()) {
                return;
            }
            if (weakReference.get() != null) {
                weakReference.get().dismissLoading();
                weakReference.get().handler.sendMessage(weakReference.get().handler.obtainMessage(MSG_DOWNLOAD_SUC, savePath));
            }
        }

        @Override
        public void onDownloadProgress(String url, int percent, long total, long progress) {
            if (!isValid()) {
                return;
            }
            if (weakReference.get() != null) {
                weakReference.get().handler.sendMessage(weakReference.get().handler.obtainMessage(MSG_DOWNLOAD_PROGRESS, percent, 0));
            }
        }

        @Override
        public void onDownloadFail(String url) {
            if (!isValid()) {
                return;
            }
            if (weakReference.get() != null) {
                weakReference.get().dismissLoading();
                weakReference.get().handler.sendEmptyMessage(MSG_DOWNLOAD_FAIL);
            }
        }
    }

    @Override
    public boolean onReceiveChat(MessageTypedEnvelope message) {
        MessageUtils.resetSendQuizCount();
        MsgItem item = new MsgItem();
        if (!TextUtils.equals(userId, message.getFrom())) {
            return false;
        }

        try {
            MessageUtils.updateUnreadCount(userId, 0);
        } catch (Exception e) {
            LogUtils.e(e);
        }

        item.message = message;
        message.setStatus(MessageStatus.SEND_SUCCESS);

        item.isShowTime = isShowMsgTime(System.currentTimeMillis());
        BaseMessageData data = message.getContent().getContent();
        if (data instanceof AudioMessageData) {
            AudioMessageData audioMessageData = (AudioMessageData) data;
            Uri uri = Uri.parse(audioMessageData.getUrl());
            String fileName = uri.getLastPathSegment();
            if (TextUtils.isEmpty(audioMessageData.getLocalFile())) {
                audioMessageData.setLocalFile(AppDirUtils.getCatchAudio() + AppDirUtils.SYSTEM_SEPARATOR + fileName);
            }
            if (!FileUtils.isFilePathExist(audioMessageData.getLocalFile())) {
                DownloadManager.getInstance().startAsynDownload(audioMessageData.getUrl(), AppDirUtils.getCatchAudio(), fileName, downloadTask);
            }
        } else if (data instanceof ImageMessageData) {
            ChatHelper.getInstance().addImage(((ImageMessageData) data).getUrl(), 0);
        }
        adapter.add(item);
        scrollToBottom();
        return false;
    }

    @Override
    public boolean onReceiveEvent(int event, JSONObject data) {
        switch (event) {
            case ImEvent.CustomSysMsg.RELATION_CHANGE:
                RelationChange change = GsonUtils.fromJson(data.toString(), RelationChange.class);
                if (change == null) {
                    return false;
                }
                if (TextUtils.equals(change.getUid(), LoginRouter.getUserId())
                        && TextUtils.equals(change.getRemoteUid(), toUser.getUserId())) {
                    if (change.getRelation() == FriendConst.TYPE_BLACK) {
                        // 我拉黑或取消拉黑对方
                        toUser.setRemoteBlack(change.getAdd());
                    }
                } else if (TextUtils.equals(change.getUid(), toUser.getUserId())
                        && TextUtils.equals(change.getRemoteUid(), LoginRouter.getUserId())) {
                    if (change.getRelation() == FriendConst.TYPE_BLACK) {
                        // 对方拉黑或取消拉黑我
                        toUser.setBlacked(change.getAdd());
                    }
                }
                break;
            default:
                break;
        }
        return false;
    }

    @Subscribe
    public void onEvent(FriendApplyEvent event) {
        if (toUser == null) {
            return;
        }
        if (!TextUtils.equals(event.getUid(), toUser.getUserId())) {
            return;
        }
        if (event.getStatus() != null && event.getStatus() == 2) {
            toUser.setRelationStatus(FriendConst.TYPE_FRIEND);
        }
    }

    @Subscribe
    public void onEvent(DeleteFriendEvent event) {
        if (toUser == null) {
            return;
        }
        if (!TextUtils.equals(event.getUserId(), toUser.getUserId())) {
            return;
        }
        toUser.setRelationStatus(FriendConst.TYPE_NO_FRIEND);
    }

    @Subscribe
    public void onEvent(LogoutEvent event) {
        finish();
    }

    @Override
    public void dismissNetWeakNotice() {
        super.dismissNetWeakNotice();
        if (headerView != null) {
            headerView.dismissNetWeakNotice();
        }
    }


    private boolean isShowFragment() {
        Fragment fragment = getSupportFragmentManager().findFragmentByTag(TAG_SELECT_FRAGMENT);
        if (fragment != null) {
            return true;

        }
        fragment = getSupportFragmentManager().findFragmentByTag(TAG_TAKE_FRAGMENT);
        if (fragment != null) {
            return true;
        }
        return false;
    }

    private void applyAudioPermission() {
        BizPermission.with(this)
                .permission(Permission.RECORD_AUDIO)
                .onDenied((permissions, doNotAskAgain) -> showPerDialog())
                .start();
    }


    private void setBottomViewParams(int bottom) {
        if (isDialog) {
            // 获取窗口对象
            Window window = getWindow();
            if (window != null) {
                WindowManager.LayoutParams winParams = window.getAttributes();
                // 设置窗口从底部弹出
                winParams.gravity = Gravity.BOTTOM;
                // 设置窗口的宽度和高度
                winParams.width = WindowManager.LayoutParams.MATCH_PARENT;
                winParams.height = bottom == 0 ? UIUtils.getPixels(587f) : WindowManager.LayoutParams.MATCH_PARENT;
                window.setAttributes(winParams);
            }
        }

        if (bottom != 0) {
            scrollToBottom();
        }
        if (bottomView == null) {
            return;
        }
        ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) bottomView.getLayoutParams();
        if (params.bottomMargin == bottom) {
            return;
        }
        params.bottomMargin = bottom;
        bottomView.requestLayout();

    }

    @Override
    public void onMsgItemAdd(MsgItem item) {
        if (adapter != null) {
            item.isShowTime = isShowMsgTime(System.currentTimeMillis());
            adapter.add(item);
            scrollToBottom();
        }
    }

    @Override
    public void onNetError() {
        dismissLoading();
        msgList.refreshComplete();
    }


    @Override
    public void gotoGame(InviteData data) {
        inviteData = data;
        if (inviteData == null) {
            return;
        }
        gotoGame();
    }

    private void gotoGame() {
//        if (UserActionStatus.getInstance().isPlaying()) {
//            //在玩游戏
//            ToastUtils.show(LanguageController.getInstance().getString("ingame_tip", R.string.input_message));
//            return;
//        }
        if (inviteData == null) {
            return;
        }
        if (TextUtils.equals(inviteData.getType(), InviteData.TYPE_TEAM)) {
            Bundle bundle = new Bundle();
            bundle.putString(GameConst.MATCH_INFO, inviteData.getData());
            bundle.putString(GameConst.BET_ICON, inviteData.getBetIcon());
            bundle.putString(GameConst.BET_ID, inviteData.getBetId());
            bundle.putString(GameConst.KEY_TYPE, GameConst.TYPE_INVITE);
            GameRouter.gotoCocosPage(bundle);
        } else if (TextUtils.equals(inviteData.getType(), InviteData.TYPE_INVITE)) {
            Bundle bundle = new Bundle();
            bundle.putString(GameConst.TEAM_INFO, inviteData.getData());
            GameRouter.gotoPlayFriend(bundle);
        } else {
            ToastUtils.show(getString(R.string.vr_network_unavailable));
        }
    }

    private int getScrollPosition() {
        if (adapter == null || msgList == null) {
            return 0;
        }
        return adapter.getItemCount() + msgList.getHeaders_includingRefreshCount() - 1;
    }

    private boolean isShowMsgTime(long time) {
        if (adapter.getItemCount() == 0) {
            presenter.setLastTime(time);
            return true;
        }
        return presenter.isShowMsgTime(time);
    }

    private class ScrollTask implements Runnable {

        public int position;

        ScrollTask(int position) {
            this.position = position;
        }

        @Override
        public void run() {
            if (isFinishing() || msgList == null) {
                return;
            }
            msgList.scrollToPosition(position);
        }
    }

    private ScrollTask scrollTask;

    private void scrollToBottom() {
        scrollTo(getScrollPosition());
    }

    private void scrollTo(int position) {
        if (msgList == null) {
            return;
        }
        if (scrollTask != null) {
            msgList.removeCallbacks(scrollTask);
            scrollTask.position = position;
        } else {
            scrollTask = new ScrollTask(position);
        }
        msgList.postDelayed(scrollTask, 30);
        // 有新消息 不展示复制气泡
       /* if (copy_view != null) {
            copy_view.dismiss();
        }*/
    }

    public ChatPresenter getPresenter() {
        return presenter;
    }

    @Override
    protected boolean isStatusBarFontDarkColor() {
        return false;
    }

    @Override
    protected int getNavigationBarColor() {
        return Color.parseColor("#1E735F");
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        photoUtil.onActivityResult(requestCode, resultCode, data);
    }

    private void setImageColor() {
        if (isDialog) {
            addImage.setColorFilter(getResources().getColor(R.color.color_room_menu), PorterDuff.Mode.SRC_IN);  // 设置颜色过滤器
            emoji.setColorFilter(getResources().getColor(R.color.color_room_menu), PorterDuff.Mode.SRC_IN);  // 设置颜色过滤器
            recorder.setColorFilter(getResources().getColor(R.color.color_room_menu), PorterDuff.Mode.SRC_IN);  // 设置颜色过滤器
            changEditKeyBord.setColorFilter(getResources().getColor(R.color.color_room_menu), PorterDuff.Mode.SRC_IN);  // 设置颜色过滤器

        }
    }

    @Override
    protected boolean isNavigationBarHide() {
        return !isDialog;
    }

    @Override
    protected int navigationBarOffsetView() {
        return R.id.root_view;
    }

    @Subscribe
    public void onBlackRemote(BlackRemoteEvent event) {
        if (toUser != null && TextUtils.equals(toUser.getUserId(), event.getRemoteUid())) {
            toUser.setRelationStatus(FriendConst.TYPE_BLACK);
        }
    }
}
