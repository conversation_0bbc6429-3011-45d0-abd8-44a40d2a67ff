<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_dialog_top_radius_24">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/room_mic_mode"
        android:textColor="@color/color_202530"
        app:fontFamily="@font/montserrat_medium"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="18dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/micModeList"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="11.5dp"
        android:layout_marginBottom="15dp"
        android:layout_marginHorizontal="11.5dp"
        android:overScrollMode="never"
        app:layout_constrainedHeight="true"
        app:layout_constraintTop_toBottomOf="@id/title"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>