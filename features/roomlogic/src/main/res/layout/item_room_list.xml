<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="8dp"
    android:layout_marginTop="4.5dp"
    tools:layout_width="150dp"
    android:background="@drawable/bg_6_fffdf5">

    <ImageView
        android:id="@+id/roomPic"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintDimensionRatio="1:1"
        tools:background="@drawable/bg_3_1fb58d"
        android:layout_margin="3dp"
        android:scaleType="centerCrop" />


    <ImageView
        android:id="@+id/roomTag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginTop="4dp"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="@+id/roomPic"
        app:layout_constraintTop_toTopOf="@+id/roomPic"
        android:layout_marginTop="3dp"
        android:layout_marginEnd="3dp"
        android:paddingHorizontal="6dp"
        android:paddingVertical="1.5dp"
        android:orientation="horizontal"
        android:background="@drawable/bg_10_24black">

        <ImageView
            android:id="@+id/imgLock"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:src="@drawable/room_icon_lock"
            android:layout_marginEnd="3dp"
            android:visibility="gone"
            tools:visibility="visible" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lottie"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_marginEnd="3dp"
            app:layout_constraintBottom_toBottomOf="@+id/number"
            app:layout_constraintEnd_toStartOf="@+id/number"
            app:layout_constraintTop_toTopOf="@+id/number"
            app:lottie_colorFilter="@color/color_white"
            app:lottie_fileName="lottie/room_list_online.json"
            app:lottie_autoPlay="false"
            app:lottie_repeatCount="-1" />

        <TextView
            android:id="@+id/number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:textColor="#ffffff"
            android:textSize="10sp"
            app:fontFamily="@font/montserrat_bold"
            tools:text="24"
            tools:ignore="SmallSp" />
    </LinearLayout>

    <ImageView
        android:id="@+id/country_flag"
        android:layout_width="21dp"
        android:layout_height="14dp"
        android:layout_marginStart="3dp"
        android:layout_marginTop="6.5dp"
        android:scaleType="fitCenter"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/roomPic" />

    <TextView
        android:id="@+id/roomName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="3dp"
        android:layout_marginStart="3dp"
        android:ellipsize="end"
        android:gravity="start|center_vertical"
        android:maxLines="1"
        android:textColor="@color/color_202530"
        android:textSize="13sp"
        app:fontFamily="@font/montserrat_regular"
        app:layout_constraintBottom_toBottomOf="@id/country_flag"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/country_flag"
        app:layout_constraintTop_toTopOf="@id/country_flag"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        tools:text="房间名称"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/imgBadge"
        android:layout_width="33dp"
        android:layout_height="33dp"
        tools:src="@color/color_white"
        app:layout_constraintBottom_toBottomOf="@id/roomPic"
        app:layout_constraintStart_toStartOf="@id/roomPic"
        android:layout_margin="1dp" />

</androidx.constraintlayout.widget.ConstraintLayout>
