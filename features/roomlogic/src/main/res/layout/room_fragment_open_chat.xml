<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_room_voice">

    <ImageView
        android:id="@+id/roomBgViewIv"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:scaleType="centerCrop" />

    <com.layaa.widget.TitleStatusBarView
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="45dp" />

    </com.layaa.widget.TitleStatusBarView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutContent"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title_bar"
        app:layout_constraintBottom_toBottomOf="parent">

        <View
            android:id="@+id/bgTopView"
            android:layout_width="0dp"
            android:layout_height="72dp"
            android:layout_marginHorizontal="37.5dp"
            android:layout_marginTop="9dp"
            android:background="@drawable/room_bg_video_start_top"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/headImg"
            android:layout_width="66dp"
            android:layout_height="66dp"
            android:layout_marginStart="3dp"
            app:layout_constraintBottom_toBottomOf="@+id/bgTopView"
            app:layout_constraintStart_toStartOf="@+id/bgTopView"
            app:layout_constraintTop_toTopOf="@+id/bgTopView"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/modifyCover"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/room_modify_video_cover"
            android:textColor="#FFFFFF"
            android:textSize="10sp"
            android:background="@drawable/bg_gradient_00_50"
            app:layout_constraintBottom_toBottomOf="@id/headImg"
            app:layout_constraintEnd_toEndOf="@id/headImg"
            app:layout_constraintStart_toStartOf="@id/headImg"
            tools:ignore="SmallSp" />

        <TextView
            android:id="@+id/nicknameTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            app:layout_constrainedWidth="true"
            android:drawableEnd="@drawable/icon_room_voice_open_edit"
            android:maxLength="50"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintEnd_toStartOf="@+id/refresh"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@id/headImg"
            app:layout_constraintTop_toTopOf="@+id/bgTopView"
            app:layout_constraintBottom_toBottomOf="@id/bgTopView"
            tools:text="昵称昵称昵称昵称昵昵称昵称昵称昵称昵称称昵称昵称昵称昵称昵昵称昵称昵称昵称昵称称" />

        <ImageView
            android:id="@+id/refresh"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginEnd="15dp"
            android:src="@drawable/icon_room_voice_open_refresh"
            app:layout_constraintBottom_toBottomOf="@+id/bgTopView"
            app:layout_constraintEnd_toEndOf="@+id/bgTopView"
            app:layout_constraintTop_toTopOf="@+id/bgTopView" />

        <View
            android:id="@+id/tagBgView"
            android:layout_width="0dp"
            android:layout_height="30dp"
            app:layout_constraintTop_toBottomOf="@id/bgTopView"
            app:layout_constraintStart_toStartOf="@id/bgTopView"
            app:layout_constraintEnd_toEndOf="@id/bgTopView"
            android:layout_marginTop="6dp"
            android:background="@drawable/room_bg_video_start_top" />

        <TextView
            android:id="@+id/tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/chat_room"
            android:textColor="@color/color_room_name"
            android:textSize="14sp"
            android:layout_marginStart="6dp"
            app:layout_constraintTop_toTopOf="@id/tagBgView"
            app:layout_constraintBottom_toBottomOf="@+id/tagBgView"
            app:layout_constraintStart_toEndOf="@+id/tagImg" />

        <ImageView
            android:id="@+id/tagImg"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginStart="9dp"
            android:src="@drawable/icon_room_voice_open_chat"
            app:layout_constraintBottom_toBottomOf="@+id/tagBgView"
            app:layout_constraintStart_toStartOf="@+id/tagBgView"
            app:layout_constraintTop_toTopOf="@+id/tagBgView"
            app:srcReverse="true" />

        <ImageView
            android:layout_width="9dp"
            android:layout_height="9dp"
            app:layout_constraintEnd_toEndOf="@id/tagBgView"
            app:layout_constraintTop_toTopOf="@id/tagBgView"
            app:layout_constraintBottom_toBottomOf="@id/tagBgView"
            android:layout_marginEnd="9dp"
            app:srcReverse="true"
            android:src="@drawable/icon_room_tag_row" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/seatList"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:overScrollMode="never"
            android:layout_marginHorizontal="12dp"
            android:layout_marginTop="80dp"
            app:layout_constraintTop_toBottomOf="@+id/tagBgView" />

        <ImageView
            android:id="@+id/background"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginBottom="45dp"
            android:src="@drawable/icon_room_voice_open_background"
            app:layout_constraintBottom_toTopOf="@+id/startLiveTv"
            app:layout_constraintEnd_toStartOf="@+id/imgMicMode"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent" />

        <ImageView
            android:id="@+id/imgMicMode"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginStart="45dp"
            android:src="@drawable/icon_room_voice_open_share"
            app:layout_constraintBottom_toBottomOf="@+id/background"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@+id/background" />

        <TextView
            android:id="@+id/startLiveTv"
            android:layout_width="0dp"
            android:layout_height="42dp"
            android:layout_marginHorizontal="37.5dp"
            android:layout_marginBottom="45dp"
            android:background="@drawable/room_bg_live_start"
            android:gravity="center"
            android:text="@string/room_start_live"
            android:textColor="#fff"
            android:textSize="15sp"
            android:enabled="false"
            app:fontFamily="@font/montserrat_semi_bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>