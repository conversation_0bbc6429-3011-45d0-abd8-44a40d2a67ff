package com.layaa.roomlogic.viewholder

import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import com.airbnb.lottie.LottieAnimationView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.GranularRoundedCorners
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.layaa.accountapi.profile.ProfileRouter
import com.layaa.libutils.dp
import com.layaa.roomapi.entity.RoomItemEntity
import com.layaa.roomlogic.R
import com.layaa.widget.basepage.BaseViewHolder

/**
 *<AUTHOR>
 *@date 2020/9/19
 *@des
 **/
class RoomListViewHolder(itemView: View) : BaseViewHolder<RoomItemEntity>(itemView) {


    private var onClickRoomListener: (View.() -> Unit)? = null

    private val lottie: LottieAnimationView = findViewById(R.id.lottie)

    private val roomName: TextView = findViewById(R.id.roomName)

    private val number: TextView = findViewById(R.id.number)

    private val roomPic: ImageView = findViewById(R.id.roomPic)
    private val countryFlag: ImageView = findViewById(R.id.country_flag)

    private val imgLock: ImageView = findViewById(R.id.imgLock)

    private val imgBadge = findViewById<ImageView>(R.id.imgBadge)

    init {
        itemView.setOnClickListener {
            onClickRoomListener?.invoke(it)
        }
    }

    override fun update(bean: RoomItemEntity) {

        if (TextUtils.isEmpty(bean.roomId)) {
            return
        }

        if (bean.isLocked()) {
            imgLock.isVisible = true
            lottie.isVisible = false
            lottie.cancelAnimation()
        } else {
            imgLock.isVisible = false
            lottie.isVisible = true
            lottie.playAnimation()
        }

        itemView.tag = bean
        number.text = bean.userCount.toString()
        Glide.with(roomPic)
            .load(bean.coverUrl)
            .transform(CenterCrop(), GranularRoundedCorners(3.dp.toFloat(), 3.dp.toFloat(), 0F, 0F))
            .placeholder(R.drawable.icon_def_bg_room_list)
            .error(R.drawable.icon_def_bg_room_list)
            .into(roomPic)
        roomName.visibility = View.VISIBLE

        Glide.with(countryFlag)
            .load(ProfileRouter.getCountryFlagByShort(bean.ownerCountry))
            .transform(RoundedCorners(2.dp))
            .into(countryFlag)

        roomName.text = bean.title

        bean.badgeUrl?.let {
            imgBadge.isVisible = true
            Glide.with(imgBadge)
                .load(bean.badgeUrl)
                .into(imgBadge)
        } ?: run {
            imgBadge.isVisible = false
        }
    }

    fun setOnClickRoomListener(onClickRoomListener: (View.() -> Unit)?): RoomListViewHolder {
        this.onClickRoomListener = onClickRoomListener
        return this
    }

}