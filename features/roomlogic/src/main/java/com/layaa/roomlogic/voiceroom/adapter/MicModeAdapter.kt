package com.layaa.roomlogic.voiceroom.adapter

import android.content.Context
import android.view.View
import android.widget.ImageView
import com.layaa.roomlogic.R
import com.layaa.roomlogic.voiceroom.entity.MicModeEntity
import com.layaa.widget.basepage.BaseAdapter
import com.layaa.widget.basepage.BaseViewHolder

/**
 *<AUTHOR>
 *@date 2024/4/27
 *@des
 **/
class MicModeAdapter(
    context: Context,
) : BaseAdapter<MicModeEntity>(context) {

    var selectedMicMode: Int = 0

    var callback: (MicModeEntity.() -> Unit)? = null
    override fun getLayoutResId(viewType: Int): Int {
        return R.layout.item_mic_mode
    }

    override fun getViewHolder(viewType: Int, view: View): BaseViewHolder<*> {
        return ViewHolder(view)
    }

    private inner class ViewHolder(view: View) : BaseViewHolder<MicModeEntity>(view) {

        private val icon: ImageView = findViewById(R.id.icon)

        init {
            itemView.setOnClickListener {
                val data = it.tag as? MicModeEntity ?: return@setOnClickListener
                callback?.invoke(data)
            }
        }

        override fun update(bean: MicModeEntity) {
            itemView.tag = bean
            icon.setImageResource(bean.resId)
            itemView.isSelected = bean.id == selectedMicMode
        }

    }
}