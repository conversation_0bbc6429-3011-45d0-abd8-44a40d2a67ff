package com.layaa.roomlogic.module.pk

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Constraints
import androidx.core.view.isVisible
import com.bumptech.glide.Glide
import com.layaa.skinlib.SkinKit

/**
 * Created by <PERSON> on 2025/08/27
 * 麦位上 PK 武器展示
 */
class PKAnchorWeaponView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {

    fun show(teamType: PKTeamType, value: PKValue?) {
        val weaponIcon = when (teamType) {
            PKTeamType.LEFT -> {
                value?.left?.levelIcon
            }

            PKTeamType.RIGHT -> {
                value?.right?.levelIcon
            }

            PKTeamType.NEITHER -> {
                null
            }
        }
        isVisible = weaponIcon != null

        val params = layoutParams as? ConstraintLayout.LayoutParams
        if (teamType == PKTeamType.LEFT) {
            params?.startToStart = Constraints.LayoutParams.UNSET
            params?.endToEnd = Constraints.LayoutParams.PARENT_ID
            scaleX = if (SkinKit.getInstance().isRtl) {
                -1F
            } else {
                1F
            }
        } else {
            params?.startToStart = Constraints.LayoutParams.PARENT_ID
            params?.endToEnd = Constraints.LayoutParams.UNSET
            scaleX = if (SkinKit.getInstance().isRtl) {
                1F
            } else {
                -1F
            }
        }
        layoutParams = params

        val request = Glide.with(this)
        request.clear(this)
        request.load(weaponIcon)
            .into(this)
    }

    fun hide() {
        isVisible = false
    }
}