package com.layaa.roomlogic.module.cell

import android.content.Context
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.GridLayoutManager
import com.layaa.accountapi.login.LoginRouter
import com.layaa.chatapi.ImEvent
import com.layaa.emote.entity.EmoteEntity
import com.layaa.im.bean.MessageTypedEnvelope
import com.layaa.language.LanguageController
import com.layaa.libui.dialog.AudioPerDialog
import com.layaa.libutils.ClickValidUtil
import com.layaa.libutils.GsonUtils
import com.layaa.libutils.JumpSettingUtil
import com.layaa.libutils.UIUtils
import com.layaa.libutils.module_log.LogUtils
import com.layaa.libutils.toast.ToastUtils
import com.layaa.roomlogic.R
import com.layaa.roomlogic.databinding.LayoutRoomVoiceAnchorBinding
import com.layaa.roomlogic.databinding.RoomItemAnchorListBinding
import com.layaa.roomlogic.datastore.BaseRoomDataStore
import com.layaa.roomlogic.datastore.SeatDataStore
import com.layaa.roomlogic.dialog.voice.VoiceMicManagerDialog
import com.layaa.roomlogic.dialog.voice.VoiceUserProfileDialog
import com.layaa.roomlogic.entity.GiftPointEntity
import com.layaa.roomlogic.entity.RoomChatMessageExtra
import com.layaa.roomlogic.entity.RoomSeatInfo
import com.layaa.roomlogic.entity.RoomUserEntity
import com.layaa.roomlogic.module.annotations.RoomMode
import com.layaa.roomlogic.module.event.CloseSeatAnimEvent
import com.layaa.roomlogic.module.event.PlaySeatVoiceAnimEvent
import com.layaa.roomlogic.module.event.ReceiveSeatMsgEvent
import com.layaa.roomlogic.module.event.StopSeatVoiceAnimEvent
import com.layaa.roomlogic.module.event.UpdateSeatEvent
import com.layaa.roomlogic.module.event.UserMicStatusEvent
import com.layaa.roomlogic.module.pk.PKEndEvent
import com.layaa.roomlogic.module.pk.PKResult
import com.layaa.roomlogic.module.pk.PKStatus
import com.layaa.roomlogic.module.pk.PKTeamType
import com.layaa.roomlogic.module.pk.PKViewModel
import com.layaa.roomlogic.module.score.ScoreAction
import com.layaa.roomlogic.module.score.ScoreValueUpdate
import com.layaa.roomlogic.module.score.ScoreViewModel
import com.layaa.roomlogic.module.view.VoiceAnchorSpanSizeLookup
import com.layaa.roomlogic.viewmodel.RoomBackgroundViewModel
import com.layaa.roomlogic.viewmodel.RoomInfoViewModel
import com.layaa.roomlogic.viewmodel.SeatViewModel
import com.layaa.skinlib.SkinKit
import com.layaa.widget.AvatarView
import com.layaa.widget.basepage.BaseAdapter
import com.layaa.widget.basepage.BaseViewHolder

/**
 *<AUTHOR>
 *@date 2021/7/20
 *@des
 **/
class VoiceAnchorCell(orderIndex: Int) : RoomCell<LayoutRoomVoiceAnchorBinding>(orderIndex) {
    private var anchorListAdapter: AnchorListAdapter? = null
    private var viewModel: RoomInfoViewModel? = null
    private var seatViewModel: SeatViewModel? = null
    private var scoreViewModel: ScoreViewModel? = null
    private var pkViewModel: PKViewModel? = null
    private var backgroundViewModel: RoomBackgroundViewModel? = null
    private var audioDialog: AudioPerDialog? = null
    private val manager = GridLayoutManager(getContext(), 4)

    override fun onCreate() {
        super.onCreate()
        viewModel = getViewModel(RoomInfoViewModel::class.java)
        seatViewModel = getViewModel(SeatViewModel::class.java)
        scoreViewModel = getViewModel(ScoreViewModel::class.java)
        pkViewModel = getViewModel(PKViewModel::class.java)
        backgroundViewModel = getViewModel(RoomBackgroundViewModel::class.java)
        viewModel?.seatCountData?.observe(this) {
            val newSpanCount = VoiceAnchorSpanSizeLookup.spanCount(it)
            if (newSpanCount != manager.spanCount) {
                manager.spanCount = newSpanCount
            }
            anchorListAdapter?.notifyDataSetChanged()
        }
        viewModel?.seatData?.observe(this) {
            if (BaseRoomDataStore.isPendingAutoUpSeat) {
                BaseRoomDataStore.isPendingAutoUpSeat = false
                autoUpSeat()
            }
            refreshData(it)
        }
        scoreViewModel?.scoreValueData?.observe(this) {
            it.dataList.forEach { scoreUser ->
                val seatId = viewModel?.getSeatId(scoreUser.uid) ?: -1
                anchorListAdapter?.getItem(seatId)?.let { seatInfo ->
                    seatInfo.scoreValue = scoreUser.score
                    anchorListAdapter?.refreshItem(seatId, PAYLOAD_SCORE_VALUE)
                }
            }
        }
        scoreViewModel?.scoreStatusData?.observe(this) {
            if (it.action == ScoreAction.END.value || it.action == ScoreAction.OFF.value) {
                anchorListAdapter?.list?.forEach { seatInfo ->
                    seatInfo?.scoreValue = if (it.action == ScoreAction.OFF.value) {
                        RoomSeatInfo.SCORE_VALUE_INVALID
                    } else {
                        0
                    }
                }
                anchorListAdapter?.notifyDataSetChanged()
                scoreViewModel?.scoreValueData?.value = ScoreValueUpdate()
            } else {
                anchorListAdapter?.list?.forEach { seatInfo ->
                    seatInfo?.scoreValue = 0
                }
                anchorListAdapter?.notifyDataSetChanged()
            }
        }
        pkViewModel?.pkInfoData?.observe(this) {
            anchorListAdapter?.notifyDataSetChanged()
        }
        pkViewModel?.pkValueData?.observe(this) {
            anchorListAdapter?.list?.forEachIndexed { index, seatInfo ->
                anchorListAdapter?.refreshItem(index, PAYLOAD_PK_VALUE)
            }
        }
        backgroundViewModel?.roomThemeData?.observe(this) {
            anchorListAdapter?.list?.forEachIndexed { index, _ ->
                anchorListAdapter?.refreshItem(index, PAYLOAD_THEME_HEADWEAR)
            }
        }
    }

    private fun autoUpSeat() {
        viewModel?.seatData?.value?.firstOrNull()?.let {
            seatViewModel?.selectSeat(
                getActivity() ?: return,
                it.seatId,
                viewModel?.roomProfileData?.value?.roomId
            )
        }
    }

    override fun onViewCreate() {
        super.onViewCreate()
        anchorListAdapter = AnchorListAdapter(getContext()!!)
        anchorListAdapter?.setHasStableIds(true)

        manager.spanSizeLookup = VoiceAnchorSpanSizeLookup(anchorListAdapter)
        viewBinding?.anchorList?.layoutManager = manager

        viewBinding?.anchorList?.itemAnimator = null
        viewBinding?.anchorList?.adapter = anchorListAdapter

//        refreshData(viewModel?.seatData?.value)
    }


    private fun updateItem(bean: RoomSeatInfo) {
        try {
            anchorListAdapter?.list?.set(bean.seatId, bean)
            anchorListAdapter?.refreshItem(bean.seatId)
        } catch (e: Exception) {
            LogUtils.e(e)
        }
    }

    override fun onDetachFromWindow() {
        super.onDetachFromWindow()
        audioDialog?.dismiss()
        audioDialog = null
    }


    private fun startEmojiAnim(
        message: MessageTypedEnvelope,
        seatInfo: RoomSeatInfo
    ) {
        val viewHolder =
            viewBinding?.anchorList?.findViewHolderForAdapterPosition(seatInfo.seatId) as? AnchorListAdapter.AnchorListViewHolder
                ?: return
        viewHolder.startEmojiAnim(message)
    }

    private fun stopEmojiAnim(seatInfo: RoomSeatInfo) {
        val viewHolder =
            viewBinding?.anchorList?.findViewHolderForAdapterPosition(seatInfo.seatId) as? AnchorListAdapter.AnchorListViewHolder
                ?: return
        viewHolder.stopEmojiAnim()
    }

    private fun playRippleAnim(seatInfo: RoomSeatInfo) {
        val viewHolder = viewBinding?.anchorList?.findViewHolderForAdapterPosition(seatInfo.seatId)
        viewHolder ?: return
        if (viewHolder is AnchorListAdapter.AnchorListViewHolder && !TextUtils.isEmpty(seatInfo.uid)
            && seatInfo.isMute.not() && SeatDataStore.instance.isSeatMicMute(seatInfo).not()
        ) {
            val userBean = viewModel?.seatUserData?.value?.get(seatInfo.uid)
            viewHolder.startRippleAnim(userBean)
        }
    }

    private fun stopRippleAnim(seatId: Int) {
        val viewHolder = viewBinding?.anchorList?.findViewHolderForAdapterPosition(seatId)
        if (viewHolder is AnchorListAdapter.AnchorListViewHolder) {
            viewHolder.stopRippleAnim()
        }
    }

    private fun getViewPointByUid(uid: String?): GiftPointEntity? {
        val seatId = viewModel?.getSeatId(uid) ?: -1
        if (seatId < 0) {
            //没在座位 返回null
            return null
        }
        val view = viewBinding?.anchorList?.findViewHolderForAdapterPosition(seatId)?.itemView
        view ?: return null
        val point = GiftPointEntity()
        val avatarView = view.findViewById<AvatarView>(R.id.avatar)
        if (avatarView != null) {
            val location = IntArray(2)
            avatarView.getLocationOnScreen(location)
            point.viewWidth = avatarView.width
            point.viewHeight = avatarView.height
            point.x = location[0] - UIUtils.getScreenWidth().div(6)
                .times(1 - avatarView.width.div(UIUtils.getScreenWidth().div(3f)))
                .toInt()//+ avatarView.width.div(2) - UIUtils.getScreenWidth().div(6)
            point.y = location[1] - UIUtils.getScreenWidth().div(6)
                .times(1 - avatarView.height.div(UIUtils.getScreenWidth().div(3f)))
                .toInt()//+ avatarView.width.div(2) - UIUtils.getScreenWidth().div(6)
        } else {
            val location = IntArray(2)
            view.getLocationOnScreen(location)
            point.viewWidth = view.width
            point.viewHeight = view.height
            point.x = location[0] - UIUtils.getScreenWidth()
                .div(2)// + view.width.div(2) - UIUtils.getScreenWidth().div(6)
            point.y = location[1] + UIUtils.getPixels(32f) - UIUtils.getScreenWidth()
                .div(6)//+ view.height / 2 - UIUtils.getPixels(75F)
        }


        return point
    }

    override fun changeRoomMode(roomMode: Int, roomType: String?) {
        addSelf(roomMode, roomType)
    }

    fun getViewPointByUid(uid: String?, sender: Boolean = false): GiftPointEntity? {
        if (sender) {
            return if (TextUtils.equals(uid, LoginRouter.getUserId())) {
                getSelfPoint()
            } else {
                getViewPointByUid(uid) //?: getSelfPoint()
            }
        }
        return getViewPointByUid(uid)
    }

    private fun getSelfPoint(): GiftPointEntity {
        val point = GiftPointEntity()
        if (SkinKit.getInstance().isRtl) {
            point.x = -UIUtils.getPixels(9f)
        } else {
            point.x =
                UIUtils.getScreenWidth() - UIUtils.getPixels(69F) - UIUtils.getScreenWidth().div(6)
        }
        point.viewWidth = UIUtils.getPixels(24f)

        point.y = UIUtils.getScreenHeight() - UIUtils.getPixels(169F)
        return point
    }

    fun refreshData(list: List<RoomSeatInfo>?) {
        anchorListAdapter?.refreshList(list)
    }

    private fun startEmojiAnim(message: MessageTypedEnvelope) {
        val extra = GsonUtils.fromJson(
            message.content.clientExt.toString(),
            RoomChatMessageExtra::class.java
        ) ?: return
        startEmojiAnim(message, viewModel?.getSeatInfo(extra.sender?.uid) ?: return)
    }

    private fun stopEmojiAnim(uid: String?) {
        stopEmojiAnim(viewModel?.getSeatInfo(uid) ?: return)
    }

    private fun playVoiceAnim(uid: String) {
        playRippleAnim(viewModel?.getSeatInfo(uid) ?: return)
    }

    private fun stopVoiceAnim(seatId: Int) {
        stopRippleAnim(seatId)
    }

    private fun showMicStatus(uid: String, isMute: Boolean) {
        val seatInfo = viewModel?.getSeatInfo(uid) ?: return
        val viewHolder =
            viewBinding?.anchorList?.findViewHolderForAdapterPosition(seatInfo.seatId) as? AnchorListAdapter.AnchorListViewHolder
                ?: return

        viewHolder.showMicStatus(isMute)
    }

    private fun showPKResultEmoji(result: PKResult) {
        val winners = if (result.leftScore > result.rightScore) {
            arrayOf(PKTeamType.LEFT)
        } else if (result.leftScore == result.rightScore) {
            arrayOf(PKTeamType.LEFT, PKTeamType.RIGHT)
        } else {
            arrayOf(PKTeamType.RIGHT)
        }
        anchorListAdapter?.list?.forEachIndexed { index, seatInfo ->
            (viewBinding?.anchorList?.findViewHolderForAdapterPosition(seatInfo.seatId)
                    as? AnchorListAdapter.AnchorListViewHolder)?.showPKResultEmoji(
                seatInfo,
                winners
            )
        }
    }

    private fun operateMic(bean: RoomSeatInfo) {
        if (!TextUtils.isEmpty(bean.uid)) {
            //麦上有人并且不是自己 打开个人卡片
            (getActivity() as? FragmentActivity)?.let { activity ->
                VoiceUserProfileDialog.start(activity.supportFragmentManager, bean.uid)
            }

            return
        }
        if (TextUtils.isEmpty(bean.uid) && (viewModel?.isNormalUser() == true)) {
            if (bean.isLock) {
                ToastUtils.show(
                    LanguageController.getInstance().getString(R.string.vr_profile_mic_locked)
                )
                return
            }
            seatViewModel?.selectSeat(
                getActivity() ?: return,
                bean.seatId,
                viewModel?.roomProfileData?.value?.roomId
            ) {
                showAudioDialog()
            }
            return
        }
        viewBinding?.anchorList?.let {
            (getActivity() as? FragmentActivity)?.let { activity ->
                VoiceMicManagerDialog.start(
                    activity.supportFragmentManager,
                    bean,
                    viewModel?.getRoomId()
                )
            }
        }
    }

    override fun getViewBinding(inflater: LayoutInflater): LayoutRoomVoiceAnchorBinding? {
        return LayoutRoomVoiceAnchorBinding.inflate(inflater)
    }

    override fun handleEvent(event: Any) {
        when (event) {
            is ReceiveSeatMsgEvent -> {
                onReceive(event.message)
            }

            is CloseSeatAnimEvent -> {
                stopEmojiAnim(event.uid)
            }

            is UpdateSeatEvent -> {
                updateItem(event.seat)
            }

            is StopSeatVoiceAnimEvent -> {
                stopVoiceAnim(event.seatId)
            }

            is PlaySeatVoiceAnimEvent -> {
                playVoiceAnim(event.uid)
            }

            is UserMicStatusEvent -> {
                showMicStatus(event.uid, event.isMute)
            }

            is PKEndEvent -> {
                showPKResultEmoji(event.result)
            }
        }
    }

    override fun generateLayoutParams(
        @RoomMode roomMode: Int,
        roomType: String?
    ): ViewGroup.LayoutParams {
        val params = ConstraintLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        params.topToBottom = R.id.roomBanner
        params.topMargin = UIUtils.getPixels(9F)
        return params
    }

    private fun onReceive(message: MessageTypedEnvelope) {
        if (message is MessageTypedEnvelope.EmojiMessageEnvelope) {
            startEmojiAnim(message)
        } else if (message is MessageTypedEnvelope.SystemNoticeMessageEnvelope) {
            val eventId = message.content.content.eventId
            when (eventId) {
                ImEvent.CustomRoomMsg.TYPE_ROOM_FINGER_GAME, ImEvent.CustomRoomMsg.TYPE_ROOM_DICE -> {
                    startEmojiAnim(message)
                }
            }
        }
    }

    private inner class AnchorListAdapter(context: Context) : BaseAdapter<RoomSeatInfo>(context) {

        override fun getLayoutResId(viewType: Int): Int {
            return 0
        }

        override fun getViewHolder(viewType: Int, view: View): BaseViewHolder<*>? {
            return null
        }

        override fun getViewHolder(
            viewType: Int,
            inflater: LayoutInflater,
            parent: ViewGroup
        ): BaseViewHolder<RoomSeatInfo>? {
            return AnchorListViewHolder(
                RoomItemAnchorListBinding.inflate(inflater, parent, false)
            )
        }

        override fun onBindViewHolder(holder: BaseViewHolder<*>, position: Int) {
        }

        override fun onBindViewHolder(
            holder: BaseViewHolder<*>,
            position: Int,
            payloads: MutableList<Any>
        ) {
            val data = getItem(position)
            (holder as? AnchorListViewHolder)?.update(data ?: RoomSeatInfo(), payloads)
        }

        override fun getItemId(position: Int): Long {
            return position.toLong()
        }

        override fun getItemCount(): Int {
            return viewModel?.seatCountData?.value ?: super.getItemCount()
        }

        inner class AnchorListViewHolder(private val binding: RoomItemAnchorListBinding) :
            BaseViewHolder<RoomSeatInfo>(binding.root) {
            private val rippleMaxSize = (95F / 375F * UIUtils.getScreenWidth()).toInt()

            init {
                itemView.setOnClickListener {
                    if (!ClickValidUtil.clickValiShortest()) {
                        return@setOnClickListener
                    }
                    val bean = itemView.tag as RoomSeatInfo
                    operateMic(bean)
                }

                (binding.ripple.layoutParams as ConstraintLayout.LayoutParams).let {
                    it.matchConstraintMaxWidth = rippleMaxSize
                    it.matchConstraintMaxHeight = rippleMaxSize
                }
            }

            override fun update(bean: RoomSeatInfo) {

            }

            fun update(bean: RoomSeatInfo, payloads: MutableList<Any>) {
                itemView.tag = bean

                val userBean = viewModel?.seatUserData?.value?.get(bean.uid)

                val headwear = if (pkViewModel?.isPKOpen() == true) {
                    userBean?.headwearUrl ?: ""
                } else {
                    backgroundViewModel?.roomThemeData?.value?.seatHeadwear ?: userBean?.headwearUrl
                    ?: ""
                }

                val pkTeamType = pkViewModel?.checkPKTeamType(bean.seatId) ?: PKTeamType.NEITHER
                val bg = when (pkTeamType) {
                    PKTeamType.LEFT -> {
                        R.drawable.room_bg_anchor_pk_left
                    }

                    PKTeamType.RIGHT -> {
                        R.drawable.room_bg_anchor_pk_right
                    }

                    PKTeamType.NEITHER -> {
                        0
                    }
                }
                binding.viewBg.setBackgroundResource(bg)

                if (userBean == null) {
                    //空麦位
                    val avatarBackground = when (pkTeamType) {
                        PKTeamType.LEFT -> R.drawable.room_bg_anchor_avatar_pk_left
                        PKTeamType.RIGHT -> R.drawable.room_bg_anchor_avatar_pk_right
                        PKTeamType.NEITHER -> R.drawable.room_bg_anchor_avatar_normal
                    }
                    binding.avatar.setAvatarBackground(avatarBackground)
                    if (bean.isLock) {
                        //锁定状态
                        binding.avatar.setAvatarResId(R.drawable.room_icon_anchor_lock)
                    } else if (bean.isMute) {
                        //禁麦状态
                        binding.avatar.setAvatarResId(R.drawable.room_icon_anchor_mute)
                    } else {
                        binding.avatar.setAvatarResId(R.drawable.room_icon_anchor_empty)
                    }
                    binding.avatar.loadHeadgear(headwear)
                    binding.nickname.text = (bean.seatId + 1).toString()
                    binding.nickname.isSelected = false
                    binding.micStatus.isVisible = false
                    binding.scoreValueView.isInvisible = true
                    binding.pkValueView.isInvisible = true
                    binding.pkWeaponView.hide()
                    stopRippleAnim()
                } else {
                    if (bean.scoreValue >= 0) {
                        binding.scoreValueView.isInvisible = false
                        binding.scoreValueView.show(bean.scoreValue)
                    } else {
                        binding.scoreValueView.isInvisible = true
                    }
                    if (payloads.firstOrNull() == PAYLOAD_SCORE_VALUE) {
                        //只刷新麦位积分
                        return
                    }

                    val pkScore = pkViewModel?.pkValueData?.value?.micUserScore?.get(bean.uid)
                    val pkStatus =
                        pkViewModel?.pkInfoData?.value?.pkStatus ?: PKStatus.NOT_OPEN.value
                    if (pkStatus == PKStatus.IN_PROGRESS.value && pkTeamType != PKTeamType.NEITHER) {
                        binding.pkValueView.isInvisible = false
                        binding.pkValueView.show(pkTeamType, pkScore)
                        binding.pkWeaponView.show(pkTeamType, pkViewModel?.pkValueData?.value)
                    } else {
                        binding.pkValueView.isInvisible = true
                        binding.pkWeaponView.hide()
                    }
                    if (payloads.firstOrNull() == PAYLOAD_PK_VALUE) {
                        //只刷新 pk 值和武器
                        return
                    }

                    if (pkStatus == PKStatus.NOT_OPEN.value || pkStatus == PKStatus.IN_PROGRESS.value) {
                        binding.viewEmojiAnim.stopPKResult()
                    }

                    binding.avatar.loadHeadgear(headwear)
                    if (payloads.firstOrNull() == PAYLOAD_THEME_HEADWEAR) {
                        // 只刷新头像框
                        return
                    }
                    binding.avatar.setAvatarBackground(0)
                    binding.avatar.loadAvatar(userBean.photo)

                    binding.nickname.text = userBean.nickName
                    binding.nickname.isSelected = true
                    binding.micStatus.isVisible =
                        SeatDataStore.instance.isSeatMicMute(bean) || bean.isMute
                }
            }

            fun startRippleAnim(userBean: RoomUserEntity?) {
                binding.ripple.startAnim(userBean)
            }

            fun stopRippleAnim() {
                binding.ripple.stopAnim()
            }

            fun showMicStatus(isMute: Boolean) {
                binding.micStatus.isVisible = isMute
            }

            fun startEmojiAnim(message: MessageTypedEnvelope) {
                binding.viewEmojiAnim.start(message, true)
            }

            fun stopEmojiAnim() {
                binding.viewEmojiAnim.isVisible = false
                binding.viewEmojiAnim.stop()
            }

            fun showPKResultEmoji(bean: RoomSeatInfo, winners: Array<PKTeamType>) {
                viewModel?.seatUserData?.value?.get(bean.uid) ?: return
                val pkTeamType = pkViewModel?.checkPKTeamType(bean.seatId) ?: PKTeamType.NEITHER
                if (pkTeamType == PKTeamType.NEITHER) {
                    return
                }
                if (winners.contains(pkTeamType)) {
                    binding.viewEmojiAnim.startPKResult(
                        EmoteEntity(
                            "common_04_daxiao",
                            EmoteEntity.Type.GIF.value
                        )
                    )
                } else {
                    binding.viewEmojiAnim.startPKResult(
                        EmoteEntity(
                            "common_10_wulianxiao",
                            EmoteEntity.Type.GIF.value
                        )
                    )
                }
            }
        }

    }

    private fun showAudioDialog() {
        val activity = getActivity() ?: return
        if (audioDialog == null) {
            audioDialog = AudioPerDialog(activity)
            audioDialog?.setContent(
                LanguageController.getInstance().getString(R.string.need_record_permission)
            )
            audioDialog?.okCallback = {
//                StatisticsUtils.with("5-6").addEventInfo("scenes", "2").send()
                JumpSettingUtil.gotoPermissionManager(activity)
            }
        }
//        StatisticsUtils.with("5-5").addEventInfo("scenes", "2").send()
        audioDialog?.show()
    }

    companion object {
        const val PAYLOAD_SCORE_VALUE = "payload_score_value"
        const val PAYLOAD_PK_VALUE = "payload_pk_value"
        const val PAYLOAD_THEME_HEADWEAR = "payload_theme_headwear"
    }
}