package com.layaa.roomlogic.module.cell

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.provider.Settings
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.text.set
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.isVisible
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.layaa.accountapi.config.ConfigRouter
import com.layaa.accountapi.login.LoginRouter
import com.layaa.chatapi.ChatRouter
import com.layaa.chatapi.observer.EventMsgRevokedObserver
import com.layaa.emote.entity.EmoteEntity
import com.layaa.emote.widget.ShortPressListener
import com.layaa.language.LanguageController
import com.layaa.libui.dialog.VipAlertDialog
import com.layaa.libui.widget.SelectImageActivity
import com.layaa.libui.widget.at.AitTextChangeListener
import com.layaa.libui.widget.at.ChatAtHelper
import com.layaa.libutils.ClickValidUtil
import com.layaa.libutils.KeyBoardUtil
import com.layaa.libutils.RegularUtils
import com.layaa.libutils.UIUtils
import com.layaa.libutils.dp
import com.layaa.libutils.module_log.LogUtils
import com.layaa.libutils.module_thread.task.ThreadPool
import com.layaa.libutils.toast.ToastUtils
import com.layaa.roomapi.RoomObserver
import com.layaa.roomlogic.R
import com.layaa.roomlogic.databinding.RoomLayoutRoomSendMsgBinding
import com.layaa.roomlogic.entity.BaseReplyEntity
import com.layaa.roomlogic.entity.EmojiReplyEntity
import com.layaa.roomlogic.entity.ImageReplyEntity
import com.layaa.roomlogic.entity.TextReplyEntity
import com.layaa.roomlogic.event.AtUserEvent
import com.layaa.roomlogic.module.helper.RoomImHelper
import com.layaa.roomlogic.notify.RoomNotificationCenter
import com.layaa.roomlogic.util.FeatureToggle
import com.layaa.roomlogic.util.ImContentUtils
import com.layaa.roomlogic.viewmodel.RoomInfoViewModel
import com.layaa.roomlogic.voiceroom.VoiceRoomActivity
import com.layaa.widget.basepage.BaseActivity.RESULT_OK
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe

/**
 *<AUTHOR>
 *@date 2023/4/14
 *@des
 **/
class RoomSendMsgCell(orderIndex: Int) : RoomCell<RoomLayoutRoomSendMsgBinding>(orderIndex),
    RoomObserver, AitTextChangeListener, EventMsgRevokedObserver {

    private var firstInput = true
    private var showKeyboard = false
    private var hideKeyboard = false
    private var roomInfoViewModel: RoomInfoViewModel? = null
    private var reply: BaseReplyEntity? = null
    private var launcher: ActivityResultLauncher<Intent>? = null

    private val vipAlertDialog: VipAlertDialog? by lazy {
        getContext()?.let {
            VipAlertDialog(it)
        } ?: run {
            null
        }
    }


    override fun onCreate() {
        super.onCreate()
        roomInfoViewModel = getViewModel(RoomInfoViewModel::class.java)
        launcher = (getActivity() as VoiceRoomActivity).registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) {
            if (it.resultCode != RESULT_OK) {
                return@registerForActivityResult
            }
            val data = it.data ?: return@registerForActivityResult
            val path =
                data.getStringExtra(SelectImageActivity.PATH) ?: return@registerForActivityResult
            RoomImHelper.instance.sendImageMessage(
                path,
                ChatAtHelper.instance.getAllAt()
            )
        }
    }

    override fun onViewCreate() {
        setNavigationPadding(0, viewBinding?.emojiTab)
        ChatAtHelper.instance.setListener(this)
        ViewCompat.setOnApplyWindowInsetsListener(viewBinding?.root ?: return) { _, insets ->
            val imeVisible = insets.isVisible(WindowInsetsCompat.Type.ime())
            val imeHeight = insets.getInsets(WindowInsetsCompat.Type.ime()).bottom
            val params = viewBinding?.root?.layoutParams as? ConstraintLayout.LayoutParams
            if (imeVisible) {
                hideKeyboard = false//只要有键盘弹出，就设置可关闭退出发送
                if (!this.showKeyboard) {
                    return@setOnApplyWindowInsetsListener insets
                }
                viewBinding?.sendEdit?.isFocusable = true
                viewBinding?.sendEdit?.isFocusableInTouchMode = true
                viewBinding?.sendEdit?.isCursorVisible = true
                viewBinding?.sendEdit?.requestFocus()
                viewBinding?.root?.isVisible = true
                viewBinding?.emojiTab?.isVisible = false
                switchEmojiIcon()
                params?.bottomMargin = imeHeight
                printInputLanguages()
                if (!TextUtils.isEmpty(reply?.sender?.nickName)) {
                    viewBinding?.replyView?.isVisible = true//引用头部布局
                    viewBinding?.colorView?.isVisible = true//引用头部布局
                    showReplyView(reply)
                } else {
                    viewBinding?.close?.isVisible = false
                    viewBinding?.emoji?.isVisible = true
                    viewBinding?.replyView?.isVisible = false//引用头部布局
                    viewBinding?.colorView?.isVisible = false//引用头部布局
                    viewBinding?.sendEdit?.setText(RoomImHelper.instance.cachedSendMsgCellText)
                    viewBinding?.sendEdit?.setSelection(viewBinding?.sendEdit?.text?.length ?: 0)
                }
                getCell<ImCell>(ImCell::class.java.simpleName)?.showKeyboard()
                firstInput = viewBinding?.sendEdit?.text?.isEmpty() ?: false
            } else {
                params?.bottomMargin = 0
                if (this.hideKeyboard) {
                    viewBinding?.emojiTab?.isVisible = true
                    switchEmojiIcon()
                    return@setOnApplyWindowInsetsListener insets
                }
                this.showKeyboard = false
                this.hideKeyboard = false
                getCell<ImCell>(ImCell::class.java.simpleName)?.hideKeyboard()
                viewBinding?.sendEdit?.isFocusable = false
                viewBinding?.sendEdit?.isFocusableInTouchMode = false
                viewBinding?.sendEdit?.isCursorVisible = false
                viewBinding?.sendEdit?.requestFocus()
                viewBinding?.root?.isVisible = false
                viewBinding?.replyView?.isVisible = false//引用头部布局
                viewBinding?.colorView?.isVisible = false//引用头部布局
            }
            insets
        }

        viewBinding?.sendEdit?.setOnEditorActionListener { textView, i, keyEvent ->
            if (i == EditorInfo.IME_ACTION_SEND) {
                if (!ClickValidUtil.clickValiShortest()) {
                    return@setOnEditorActionListener false
                }
                if (TextUtils.isEmpty(viewBinding?.sendEdit?.text?.trim())) {
                    return@setOnEditorActionListener false
                }
                val (canSend, reason) = roomInfoViewModel?.canSendMsg() ?: Pair(true, "")
                if (canSend.not()) {
                    ToastUtils.show(reason)
                    return@setOnEditorActionListener false
                }
                val text =
                    RegularUtils.replaceBlank(viewBinding?.sendEdit?.text?.toString() ?: "").trim()
                RoomImHelper.instance.sendTextMessage(
                    text,
                    ChatAtHelper.instance.getAllAt(),
                    reply
                )
                viewBinding?.close?.performClick()
                showKeyboard = false
                hideKeyboard = false
                getActivity()?.let {
                    KeyBoardUtil.hideSoftKeyboardNotAlways(it)
                }
                return@setOnEditorActionListener true
            }
            return@setOnEditorActionListener false
        }
        viewBinding?.root?.setOnClickListener {
            if (!ClickValidUtil.clickValiShortest()) {
                return@setOnClickListener
            }
            showKeyboard = false
            hideKeyboard = false
            getActivity()?.let {
                KeyBoardUtil.hideSoftKeyboardNotAlways(it)
            }
        }
        viewBinding?.emoji?.setOnClickListener {
            if (!ClickValidUtil.clickValiShortest()) {
                return@setOnClickListener
            }
            if (viewBinding?.emojiTab?.isVisible == true) {//切换键盘
                showSendView()
            } else { //切换表情
                showEmojiView()
            }
        }

        viewBinding?.root?.isVisible = false

        viewBinding?.close?.setOnClickListener {
            reply = null
            viewBinding?.sendEdit?.hint =
                LanguageController.getInstance().getString(R.string.please_chat_friendly)
            viewBinding?.sendEdit?.setText("")
            viewBinding?.emoji?.isVisible = true
            viewBinding?.close?.isVisible = false
        }
        viewBinding?.closeDialogIv?.setOnClickListener {
            if (!ClickValidUtil.clickValiShortest()) {
                return@setOnClickListener
            }
            showKeyboard = false
            hideKeyboard = false
            getActivity()?.let {
                KeyBoardUtil.hideSoftKeyboardNotAlways(it)
            }
        }

        ChatAtHelper.instance.addTextChangedListener(viewBinding?.sendEdit)
        viewBinding?.sendEdit?.hint =
            LanguageController.getInstance().getString(R.string.please_chat_friendly)
        viewBinding?.sendEdit?.imeOptions = EditorInfo.IME_ACTION_SEND

        viewBinding?.sendEdit?.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

            }

            override fun afterTextChanged(s: Editable?) {
                s?.let {
                    if (it.length > 200) {
                        ToastUtils.show(
                            LanguageController.getInstance()
                                .getString(R.string.input_too_long)
                        )
                        s.delete(it.length - 1, it.length)
                        return
                    }

                    if (firstInput && s.isNotEmpty()) {
                        if (isProbablyArabic(s.toString())) {
                            viewBinding?.sendEdit?.textDirection =
                                View.LAYOUT_DIRECTION_RTL
                        } else {
                            viewBinding?.sendEdit?.textDirection =
                                View.LAYOUT_DIRECTION_LTR
                        }
                        firstInput = false
                    }
                }
                RoomImHelper.instance.cachedSendMsgCellText = s
            }
        })

        viewBinding?.pictureIv?.isVisible = FeatureToggle.isShowIMImage()
        viewBinding?.pictureIv?.setOnClickListener {
            if (LoginRouter.canSendRoomIMImage()) {
                launcher?.launch(Intent(getActivity(), SelectImageActivity::class.java))
            } else {
                val minLevel = ConfigRouter.getMinLevelOfRoomIMImage()
                vipAlertDialog?.show(
                    minLevel,
                    "VIP$minLevel",
                    R.string.vip_room_im_image_alert_desc
                )
            }
        }
        initEmojiView()
    }

    fun initEmojiView() {
        viewBinding?.emojiTab?.setTabBackground(Color.WHITE)
        viewBinding?.emojiTab?.shortPressListener = object : ShortPressListener {
            override fun onClick(view: View, entity: EmoteEntity) {
                when (entity.name) {
                    "guessing_dice" -> {
                        RoomImHelper.instance.sendDiceMessage()
                    }

                    "guessing_finger" -> {
                        RoomImHelper.instance.sendFingerMessage()
                    }

                    else -> {
                        RoomImHelper.instance.sendEmojiMessage(
                            entity,
                            ChatAtHelper.instance.getAllAt()
                        )
                    }
                }
                getCell<ImCell>(ImCell::class.java.simpleName)?.hideKeyboard()
            }

        }
    }

    /**
     * 关闭发送框
     */
    fun closeSendView() {
        showKeyboard = false
        hideKeyboard = false
        viewBinding?.sendEdit?.isFocusable = false
        viewBinding?.sendEdit?.isFocusableInTouchMode = false
        viewBinding?.sendEdit?.isCursorVisible = false
        viewBinding?.sendEdit?.requestFocus()
        viewBinding?.root?.visibility = View.GONE
        viewBinding?.emojiTab?.visibility = View.GONE
        val params = viewBinding?.root?.layoutParams as? ConstraintLayout.LayoutParams
        params?.bottomMargin = 0
        viewBinding?.replyView?.isVisible = false//引用头部布局
        viewBinding?.colorView?.isVisible = false//引用头部布局
        viewBinding?.emojiTab?.isVisible = false
    }

    fun closeEmojiView() {
        viewBinding?.emojiTab?.isVisible = false
    }

    fun switchEmojiIcon() {
        if (viewBinding?.emojiTab?.isVisible == true) {
            viewBinding?.emoji?.setImageResource(R.drawable.icon_room_switch_keybord)
        } else {
            viewBinding?.emoji?.setImageResource(R.drawable.room_icon_room_bottom_emoji)
        }
    }

    @Subscribe
    fun onEvent(atUserEvent: AtUserEvent) {
        if (TextUtils.isEmpty(atUserEvent.user.uid)) {
            return
        }
        reply = null
        if (!TextUtils.isEmpty(atUserEvent.atTxt)) {
            viewBinding?.sendEdit?.setText(atUserEvent.atTxt)
            ChatAtHelper.instance.clear()
        }
        ChatAtHelper.instance.setAtUser(arrayOf(atUserEvent.user), viewBinding?.sendEdit)
        ThreadPool.postDelayed(getCellName(), {
            showKeyboard()
        }, 300L)
    }

    fun showReply(reply: BaseReplyEntity, atUserEvent: AtUserEvent) {
        this.reply = reply
        if (!TextUtils.isEmpty(atUserEvent.user.uid)) {
        }
        ChatAtHelper.instance.setAtUser(arrayOf(atUserEvent.user), viewBinding?.sendEdit)
        ThreadPool.postDelayed(getCellName(), {
            showKeyboard()
        }, 300L)
    }

    override fun changeRoomMode(roomMode: Int, roomType: String?) {
        addSelf(roomMode, roomType)
    }

    override fun getViewBinding(inflater: LayoutInflater): RoomLayoutRoomSendMsgBinding {
        return RoomLayoutRoomSendMsgBinding.inflate(inflater)
    }

    override fun generateLayoutParams(roomMode: Int, roomType: String?): ViewGroup.LayoutParams {
        val params = ConstraintLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        params.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
        return params
    }

    override fun handleEvent(event: Any) {

    }

    override fun onAttachToWindow() {
        super.onAttachToWindow()
        RoomNotificationCenter.instances.addObserver(javaClass.simpleName, this)
        ChatRouter.registerMsgRevokedEvent(javaClass.simpleName, this)
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    override fun onDetachFromWindow() {
        super.onDetachFromWindow()
        RoomNotificationCenter.instances.removeObserver(javaClass.simpleName)
        ChatRouter.unregisterMsgRevokedEvent(javaClass.simpleName)
        EventBus.getDefault().unregister(this)
        ThreadPool.cancelAllRunnables(getCellName())
        ChatAtHelper.instance.removeListener()
        ChatAtHelper.instance.removeTextChangedListener(viewBinding?.sendEdit)
    }

    override fun roomObserver(id: Int, vararg arg: Any?) {
        when (id) {
            RoomObserver.SHOW_KEYBOARD -> {
                showKeyboard()
            }
        }
    }

    //非回复模式
    fun showSendView() {
        reply = null
        showKeyboard()
    }

    fun showKeyboard() {
        showKeyboard = true
        getActivity()?.let {
            KeyBoardUtil.showSoftKeyboard(it, viewBinding?.sendEdit)
        }
    }

    fun showEmojiView() {
        reply = null
        if (!showKeyboard) {
            viewBinding?.emojiTab?.isVisible = true
            viewBinding?.emojiTab?.load({ LoginRouter.canUseVipEmoji() }, true)
            switchEmojiIcon()
        }
        viewBinding?.root?.visibility = View.VISIBLE
        getCell<ImCell>(ImCell::class.java.simpleName)?.showKeyboard()
        hideKeyboard = true
        KeyBoardUtil.hideSoftKeyboardNotAlways(getActivity())

    }

    private fun printInputLanguages() {
        LogUtils.i(
            "printInputLanguages",
            Settings.Secure.getString(
                getContext()?.contentResolver,
                Settings.Secure.SELECTED_INPUT_METHOD_SUBTYPE
            )
        )
        val imm: InputMethodManager? =
            getContext()?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager?
        imm ?: return
        LogUtils.i("printInputLanguages", "${imm.currentInputMethodSubtype?.locale}")
        val list = imm.enabledInputMethodList ?: return
        list.forEach {
            val submethods = imm.getEnabledInputMethodSubtypeList(it, true);
            submethods.forEach { action ->
                LogUtils.i(
                    "printInputLanguages",
                    "action == ${action.mode};locale = ${action.locale}"
                )
            }
        }
    }

    fun isProbablyArabic(s: String?): Boolean {
        s ?: return true
        var i = 0
        while (i < s.length) {
            val c = s.codePointAt(i)
            if (c in 0x0600..0x06E0) return true
            i += Character.charCount(c)
        }
        return false
    }


    override fun onTextAdd(content: String?, start: Int, length: Int) {
        viewBinding?.sendEdit?.editableText?.insert(start, content)
        viewBinding?.sendEdit?.editableText?.set(
            start,
            start + (content?.length ?: 0),
            ForegroundColorSpan(ImContentUtils.AT_COLOR)
        )
        viewBinding?.sendEdit?.setSelection(
            viewBinding?.sendEdit?.text?.length ?: (start + (content?.length ?: 0))
        )
    }

    override fun onTextDelete(start: Int, length: Int) {
        val end = start + length - 1
        viewBinding?.sendEdit?.editableText?.replace(start, end, "")
    }

    private fun calculateWidth(ratio: Float): Array<Int> {
        // var maxWidth = UIUtils.getPixels(219f)
        val maxWidth = ImCell.imCellWidth.minus(UIUtils.getPixels(66f)).times(0.4).toInt()
        val maxHeight = UIUtils.getPixels(100f).times(0.4).toInt()
        val minWidth = UIUtils.getPixels(50f).times(0.4).toInt()
        val minHeight = UIUtils.getPixels(70f).times(0.4).toInt()
        if (ratio < 0.4) {
            return arrayOf(minWidth, maxHeight)
        }
        if (ratio in 0.4..1.0) {
            return arrayOf(maxHeight.times(ratio).toInt(), maxHeight)
        }
        if (ratio > 1 && ratio <= 2.7) {
            return arrayOf(maxWidth, maxWidth.div(ratio).toInt())
        }
        return arrayOf(maxWidth, minHeight)
    }

    override fun onRevokedMsg(messageId: String) {
        ThreadPool.runOnUiThread {
            val reply = this.reply
            if (reply?.msgId == messageId && reply is ImageReplyEntity) {
                reply.image?.url = ""
                viewBinding?.replyIv?.layoutParams =
                    viewBinding?.replyIv?.layoutParams?.apply {
                        width = 40.dp
                        height = 40.dp
                    }
                viewBinding?.replyIv?.setImageResource(R.drawable.icon_image_revoked)
            }
        }
    }

    private fun showReplyView(reply: BaseReplyEntity?) {
        viewBinding?.replyNickNameTv?.text = reply?.sender?.nickName
        if (reply is TextReplyEntity) {
            viewBinding?.replyIv?.isVisible = false
            viewBinding?.replyEmojiIv?.isVisible = false
            viewBinding?.replyContentTv?.isVisible = true
            viewBinding?.replyContentTv?.text = reply.text
        }
        if (reply is ImageReplyEntity) {
            viewBinding?.replyContentTv?.isVisible = false
            viewBinding?.replyEmojiIv?.isVisible = false
            viewBinding?.replyIv?.isVisible = true
            val imageSize = calculateWidth(reply.image?.ratio ?: 1F)
            viewBinding?.replyIv?.layoutParams =
                viewBinding?.replyIv?.layoutParams?.apply {
                    width = imageSize[0]
                    height = imageSize[1]
                }
            Glide.with(
                viewBinding?.replyIv ?: return
            )
                .load(reply.image?.url)
                .transform(CenterCrop(), RoundedCorners(3.dp))
                .into(
                    viewBinding?.replyIv ?: return
                )
        }
        if (reply is EmojiReplyEntity) {
            viewBinding?.replyContentTv?.isVisible = false
            viewBinding?.replyIv?.isVisible = false
            viewBinding?.replyEmojiIv?.isVisible = true
            val emojiEntity = EmoteEntity(
                reply.emoji?.name ?: return,
                reply.emoji.type
            )
            if (emojiEntity.type.lowercase() == EmoteEntity.Type.GIF.value) {
                Glide.with(viewBinding?.replyEmojiIv ?: return)
                    .asGif()
                    .load(emojiEntity.getEffectResource())
                    .into(viewBinding?.replyEmojiIv ?: return)
            } else {
                Glide.with(viewBinding?.replyEmojiIv ?: return)
                    .load(emojiEntity.getEffectResource())
                    .into(viewBinding?.replyEmojiIv ?: return)
            }
        }
    }
}