package com.layaa.roomlogic.module.pk

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.layaa.libui.base.BaseVBDialog
import com.layaa.libui.numberCommonFormat
import com.layaa.roomapi.RoomObserver
import com.layaa.roomlogic.R
import com.layaa.roomlogic.databinding.DialogPkResultBinding
import com.layaa.roomlogic.databinding.ItemPkResultTopUserBinding
import com.layaa.roomlogic.notify.RoomNotificationCenter
import com.layaa.skinlib.SkinKit
import com.layaa.widget.basepage.BaseAdapter
import com.layaa.widget.basepage.BaseViewHolder
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Created by Todd on 25/08/29_周五
 */
class PKResultDialog(context: Context) : BaseVBDialog<DialogPkResultBinding>(context) {

    private var delayJob: Job? = null
    private var delayCoroutineScope = CoroutineScope(Dispatchers.Main)
    private var winnerTeamType = PKTeamType.NEITHER

    private val memberAdapter = MemberAdapter(context)

    private var result: PKResult? = null

    override fun getViewBinding(): DialogPkResultBinding {
        return DialogPkResultBinding.inflate(layoutInflater)
    }

    override fun initAction() {

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding.imgClose.setOnClickListener {
            dismiss()
        }
        binding.recyclerMember.adapter = memberAdapter
        binding.recyclerMember.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        binding.imgTop1Headwear.setOnClickListener {
            RoomNotificationCenter.instances.sendNotification(
                RoomObserver.SHOW_USER_PROFILE,
                result?.contributorTopUser?.uid ?: return@setOnClickListener
            )
        }
    }

    fun show(result: PKResult) {
        show()
        this.result = result
        delayJob?.cancel()
//        delayJob = delayCoroutineScope.launch {
//            delay(5000)
//            dismiss()
//        }
        val winner = if (result.leftScore > result.rightScore) {
            PKTeamType.LEFT
        } else if (result.leftScore == result.rightScore) {
            PKTeamType.NEITHER
        } else {
            PKTeamType.RIGHT
        }
        this.winnerTeamType = winner

        when (winner) {
            PKTeamType.LEFT -> {
                binding.imgBg.setImageResource(R.drawable.room_bg_pk_result_decoration_left)
                binding.imgTop.setImageResource(R.drawable.room_icon_pk_result_top_left)
                binding.layoutContent.setBackgroundResource(R.drawable.room_bg_pk_result_content_left)
                binding.imgFlagLeft.setImageResource(R.drawable.room_icon_pk_result_flag_win_left)
                binding.imgFlagRight.setImageResource(R.drawable.room_icon_pk_result_flag_lose_right)
                binding.txtProgressLeft.setBackgroundColor("#C41F1F".toColorInt())
                binding.txtProgressRight.setBackgroundColor("#6E737B".toColorInt())
                binding.imgTop1Headwear.setImageResource(R.drawable.room_icon_pk_result_top1_headwear_left)
                binding.imgTop1ScoreBg.setImageResource(R.drawable.room_bg_pk_result_top1_score_left)
                binding.txtDraw.isVisible = false
                binding.txtMemberEmpty.isVisible = result.topUserList.isEmpty()
                binding.recyclerMember.isVisible = result.topUserList.isNotEmpty()
                binding.groupContent.isVisible = true
            }

            PKTeamType.RIGHT -> {
                binding.imgBg.setImageResource(R.drawable.room_bg_pk_result_decoration_right)
                binding.imgTop.setImageResource(R.drawable.room_icon_pk_result_top_right)
                binding.layoutContent.setBackgroundResource(R.drawable.room_bg_pk_result_content_right)
                binding.imgFlagLeft.setImageResource(R.drawable.room_icon_pk_result_flag_lose_left)
                binding.imgFlagRight.setImageResource(R.drawable.room_icon_pk_result_flag_win_right)
                binding.txtProgressLeft.setBackgroundColor("#8D666C".toColorInt())
                binding.txtProgressRight.setBackgroundColor("#227FFF".toColorInt())
                binding.imgTop1Headwear.setImageResource(R.drawable.room_icon_pk_result_top1_headwear_right)
                binding.imgTop1ScoreBg.setImageResource(R.drawable.room_bg_pk_result_top1_score_right)
                binding.txtDraw.isVisible = false
                binding.txtMemberEmpty.isVisible = result.topUserList.isEmpty()
                binding.recyclerMember.isVisible = result.topUserList.isNotEmpty()
                binding.groupContent.isVisible = true
            }

            PKTeamType.NEITHER -> {
                binding.imgBg.setImageResource(R.drawable.room_bg_pk_result_decoration_draw)
                binding.imgTop.setImageResource(R.drawable.room_icon_pk_result_top_draw)
                binding.layoutContent.setBackgroundResource(R.drawable.room_bg_pk_result_content_draw)
                binding.txtMemberEmpty.isVisible = false
                binding.recyclerMember.isVisible = false
                binding.groupContent.isVisible = false
                binding.txtDraw.isVisible = true
            }
        }

        binding.txtTitle.text = if (winner != PKTeamType.NEITHER) {
            context.getString(R.string.pk_result_win)
        } else {
            context.getString(R.string.pk_result_draw)
        }
        binding.txtProgressLeft.text = result.leftScore.toString()
        binding.txtProgressRight.text = result.rightScore.toString()

        if (result.leftScore != 0L || result.rightScore != 0L) {
            val totalScore = result.leftScore + result.rightScore
            var leftPercent = result.leftScore / totalScore.toFloat()
            val rightPercent = result.rightScore / totalScore.toFloat()
            // 最小宽度 0.3
            if (leftPercent < 0.35f) {
                leftPercent = 0.35f
            } else if (rightPercent < 0.35f) {
                leftPercent = 0.65f
            }

            // RTL适配：在RTL环境下，guideline的百分比需要镜像
            val finalPercent = if (SkinKit.getInstance().isRtl) {
                1.0f - leftPercent
            } else {
                leftPercent
            }

            val params =
                binding.guidelineProgress.layoutParams as? ConstraintLayout.LayoutParams ?: return
            params.guidePercent = finalPercent
            binding.guidelineProgress.layoutParams = params
        }

        Glide.with(context)
            .load(result.contributorTopUser.photo)
            .circleCrop()
            .into(binding.imgTop1Avatar)
        binding.txtTop1Nickname.text = result.contributorTopUser.nickName
        binding.txtTop1Score.text = result.contributorTopUser.score.toString()

        memberAdapter.refreshList(result.topUserList)
    }

    private inner class MemberAdapter(context: Context) : BaseAdapter<PKUser>(context) {

        override fun getLayoutResId(viewType: Int): Int {
            return 0
        }

        override fun getViewHolder(
            viewType: Int,
            view: View
        ): BaseViewHolder<*>? {
            return null
        }

        override fun getViewHolder(
            viewType: Int,
            inflater: LayoutInflater,
            parent: ViewGroup
        ): BaseViewHolder<PKUser>? {
            return ViewHolder(
                ItemPkResultTopUserBinding.inflate(
                    inflater,
                    parent,
                    false
                )
            )
        }

        inner class ViewHolder(private val binding: ItemPkResultTopUserBinding) :
            BaseViewHolder<PKUser>(binding.root) {

            init {
                itemView.setOnClickListener {
                    val bean = it.tag as? PKUser ?: return@setOnClickListener
                    RoomNotificationCenter.instances.sendNotification(
                        RoomObserver.SHOW_USER_PROFILE,
                        bean.uid
                    )
                }
            }

            override fun update(bean: PKUser) {
                itemView.tag = bean
                Glide.with(binding.imgAvatar)
                    .load(bean.photo)
                    .circleCrop()
                    .into(binding.imgAvatar)
                binding.txtNickname.text = bean.nickName
                binding.txtScore.text = bean.score.numberCommonFormat()
                when (winnerTeamType) {
                    PKTeamType.LEFT -> {
                        if (bindingAdapterPosition == 0) {
                            binding.imgAvatar.foreground = ContextCompat.getDrawable(
                                binding.imgAvatar.context,
                                R.drawable.room_icon_pk_result_mvp_headwear_left
                            )
                        } else {
                            binding.imgAvatar.foreground = ContextCompat.getDrawable(
                                binding.imgAvatar.context,
                                R.drawable.room_icon_pk_result_normal_headwear_left
                            )
                        }
                        binding.txtNickname.setTextColor("#FFFF7272".toColorInt())
                        binding.txtScore.setTextColor("#FFFF7272".toColorInt())
                        binding.txtScore.setCompoundDrawablesWithIntrinsicBounds(
                            R.drawable.room_icon_pk_result_star_win_left,
                            0,
                            0,
                            0
                        )
                    }

                    PKTeamType.RIGHT -> {
                        if (bindingAdapterPosition == 0) {
                            binding.imgAvatar.foreground = ContextCompat.getDrawable(
                                binding.imgAvatar.context,
                                R.drawable.room_icon_pk_result_mvp_headwear_right
                            )
                        } else {
                            binding.imgAvatar.foreground = ContextCompat.getDrawable(
                                binding.imgAvatar.context,
                                R.drawable.room_icon_pk_result_normal_headwear_right
                            )
                        }
                        binding.txtNickname.setTextColor("#FFFF7272".toColorInt())
                        binding.txtScore.setTextColor("#FFFF7272".toColorInt())
                        binding.txtScore.setCompoundDrawablesWithIntrinsicBounds(
                            R.drawable.room_icon_pk_result_star_win_right,
                            0,
                            0,
                            0
                        )
                    }

                    PKTeamType.NEITHER -> {

                    }
                }
            }

        }
    }

    override fun getAnimationStyle(): Int {
        return R.style.popupAnimation
    }
}