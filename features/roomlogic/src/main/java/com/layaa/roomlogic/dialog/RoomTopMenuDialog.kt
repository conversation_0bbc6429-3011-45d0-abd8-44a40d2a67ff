package com.layaa.roomlogic.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.Window
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import com.layaa.accountapi.login.LoginRouter
import com.layaa.language.LanguageController
import com.layaa.libui.dialog.BaseDialogFragment
import com.layaa.libui.dialog.CommonTipDialog
import com.layaa.libutils.ClickValidUtil
import com.layaa.libutils.UIUtils
import com.layaa.libutils.toast.ToastUtils
import com.layaa.roomapi.RoomObserver
import com.layaa.roomlogic.R
import com.layaa.roomlogic.databinding.DialogRoomTopMenuBinding
import com.layaa.roomlogic.datastore.BaseRoomDataStore
import com.layaa.roomlogic.entity.RoomMenuEntity
import com.layaa.roomlogic.liveroom.dialog.SelectBackgroundFragment
import com.layaa.roomlogic.liveroom.dialog.SelectRoomThemeFragment
import com.layaa.roomlogic.module.event.MinimizeRoomEvent
import com.layaa.roomlogic.module.event.QuitRoomEvent
import com.layaa.roomlogic.module.helper.RoomEventHelper
import com.layaa.roomlogic.module.pk.PKSettingEvent
import com.layaa.roomlogic.module.pk.PKViewModel
import com.layaa.roomlogic.module.score.ScoreBoardDialog
import com.layaa.roomlogic.module.score.ScoreViewModel
import com.layaa.roomlogic.music.MusicListActivity
import com.layaa.roomlogic.notify.RoomNotificationCenter
import com.layaa.roomlogic.viewmodel.RoomBackgroundViewModel
import com.layaa.roomlogic.viewmodel.RoomInfoViewModel
import com.layaa.roomlogic.viewmodel.RoomManagerViewModel
import com.layaa.widget.GridLayoutManagerWrapper
import com.layaa.widget.basepage.BaseAdapter
import com.layaa.widget.basepage.BaseViewHolder
import org.greenrobot.eventbus.EventBus

/**
 *<AUTHOR>
 *@date 2024/3/27
 *@des
 **/
class RoomTopMenuDialog(context: Context) :
    BaseDialogFragment<RoomInfoViewModel, DialogRoomTopMenuBinding>() {

    companion object {
        @JvmStatic
        fun start(fragmentManager: FragmentManager, context: Context) {
            val fragment = RoomTopMenuDialog(context)
            fragment.showNow(fragmentManager, RoomTopMenuDialog::class.java.simpleName)
        }
    }

    private val mContext = context
    private val settingAdapter by lazy { Adapter() }
    private val modeAdapter by lazy { Adapter() }
    private val moreAdapter by lazy { Adapter() }

    private var managerViewModel: RoomManagerViewModel? = null
    private var roomInfoViewModel: RoomInfoViewModel? = null
    private var backgroundViewModel: RoomBackgroundViewModel? = null
    private var scoreViewModel: ScoreViewModel? = null
    private var pkViewModel: PKViewModel? = null

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)

        // 请求透明主题
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)

        return dialog
    }

    override fun getGravity(): Int {
        return Gravity.TOP
    }

    override fun initBinding(
        inflater: LayoutInflater,
        parent: ViewGroup?,
        attachToParent: Boolean
    ): DialogRoomTopMenuBinding {
        return DialogRoomTopMenuBinding.inflate(inflater, parent, attachToParent)
    }

    override fun getViewModel(): Class<RoomInfoViewModel> {
        return RoomInfoViewModel::class.java
    }

    @SuppressLint("SetTextI18n")
    override fun initAction() {
        managerViewModel =
            ViewModelProvider(requireActivity()).get(RoomManagerViewModel::class.java)
        roomInfoViewModel = ViewModelProvider(requireActivity()).get(RoomInfoViewModel::class.java)
        backgroundViewModel =
            ViewModelProvider(requireActivity()).get(RoomBackgroundViewModel::class.java)
        scoreViewModel = ViewModelProvider(requireActivity()).get(ScoreViewModel::class.java)
        pkViewModel = ViewModelProvider(requireActivity())[PKViewModel::class.java]


        binding.roomSettingList.itemAnimator = null
        binding.roomSettingList.adapter = settingAdapter

        binding.roomModeList.layoutManager = GridLayoutManagerWrapper(context, 4)
        binding.roomModeList.itemAnimator = null
        binding.roomModeList.adapter = modeAdapter

        binding.roomMoreList.layoutManager = GridLayoutManagerWrapper(context, 4)
        binding.roomMoreList.itemAnimator = null
        binding.roomMoreList.adapter = moreAdapter

        initSettingList()
        initModeList()
        initMoreList()
    }

    private fun initSettingList() {
        val list = arrayListOf<RoomMenuEntity>()
        list.add(
            RoomMenuEntity(
                RoomMenuEntity.TYPE_MUSIC,
                getString(R.string.account_setting_music),
                R.drawable.icon_room_setting_music,
            )
        )

        if (viewModel?.roomProfileData?.value?.ownerId == LoginRouter.getUserId()) {
            if (viewModel?.roomLockData?.value == true) {
                list.add(
                    RoomMenuEntity(
                        RoomMenuEntity.TYPE_UNLOCK,
                        getString(R.string.text_room_unlock),
                        R.drawable.icon_room_setting_unlock,
                    )
                )
            } else {
                list.add(
                    RoomMenuEntity(
                        RoomMenuEntity.TYPE_LOCK,
                        getString(R.string.text_room_lock),
                        R.drawable.icon_room_setting_lock,
                    )
                )
            }

            list.add(
                RoomMenuEntity(
                    RoomMenuEntity.TYPE_BACKGROUND,
                    getString(R.string.text_room_background),
                    R.drawable.icon_room_setting_bg,
                )
            )
            list.add(
                RoomMenuEntity(
                    RoomMenuEntity.TYPE_THEME,
                    getString(R.string.text_room_theme),
                    R.drawable.icon_room_setting_theme,
                )
            )
        }

        list.add(
            RoomMenuEntity(
                RoomMenuEntity.TYPE_MIC_SEAT_MODE,
                getString(R.string.text_room_mic_number),
                R.drawable.icon_room_setting_seat,
            )
        )

        val scoreTitle =
            if (scoreViewModel?.isScoreOpen() == true) {
                R.string.vr_scoreboard_title_ing
            } else {
                R.string.vr_scoreboard_title
            }
        list.add(
            RoomMenuEntity(
                RoomMenuEntity.TYPE_SCORE_BOARD,
                getString(scoreTitle),
                R.drawable.icon_room_setting_score_board,
            )
        )

        list.add(
            RoomMenuEntity(
                RoomMenuEntity.TYPE_CLEAN_CHAT,
                getString(R.string.room_live_clear_screen),
                R.drawable.icon_room_setting_clear_chat,
            )
        )
        list.add(
            RoomMenuEntity(
                RoomMenuEntity.TYPE_SETTING_COMMENT,
                getString(R.string.text_room_setting_comment),
                R.drawable.icon_room_setting_comment,
            )
        )
        list.add(
            RoomMenuEntity(
                RoomMenuEntity.TYPE_PK,
                getString(R.string.pk_title),
                R.drawable.icon_room_setting_pk,
            )
        )

        if (list.size <= 8) {
            binding.roomSettingList.layoutManager = GridLayoutManagerWrapper(context, 4)
            binding.roomSettingList.layoutParams =
                (binding.roomSettingList.layoutParams as MarginLayoutParams).apply {
                    marginEnd = UIUtils.getPixels(15f)
                }

        } else {
            binding.roomSettingList.layoutManager =
                GridLayoutManagerWrapper(context, 2, GridLayoutManager.HORIZONTAL, false)
        }
        settingAdapter.refreshList(list)
    }

    private fun initModeList() {
        val list = arrayListOf<RoomMenuEntity>()
        list.add(
            RoomMenuEntity(
                RoomMenuEntity.MODE_PARTY,
                getString(R.string.text_room_party),
                R.drawable.icon_room_mode_party,
            )
        )
        modeAdapter.refreshList(list)
    }

    private fun initMoreList() {
        val list = arrayListOf<RoomMenuEntity>()
        list.add(
            RoomMenuEntity(
                RoomMenuEntity.MORE_KEEP,
                getString(R.string.Keep),
                R.drawable.icon_room_keep_room,
            )
        )
        list.add(
            RoomMenuEntity(
                RoomMenuEntity.MORE_CLOSE,
                if (viewModel?.isRoomOwn() == true) {
                    getString(R.string.close_room)
                } else {
                    getString(R.string.exit_room)
                },
                R.drawable.icon_room_close_room,
            )
        )
        moreAdapter.refreshList(list)
    }

    override fun getStyle(): Int {
        return R.style.TopSheetDialog
    }


    private inner class Adapter : BaseAdapter<RoomMenuEntity>(binding.root.context) {
        override fun getLayoutResId(viewType: Int): Int {
            return R.layout.room_item_menu
        }

        override fun getViewHolder(viewType: Int, view: View): BaseViewHolder<*> {
            return ViewHolder(view)
        }

    }

    private inner class ViewHolder(view: View) : BaseViewHolder<RoomMenuEntity>(view) {

        private var icon: ImageView = findViewById(R.id.icon)
        private var name: TextView = findViewById(R.id.name)

        init {
            itemView.setOnClickListener {
                if (!ClickValidUtil.clickValiShortest()) {
                    return@setOnClickListener
                }
                val bean = itemView.tag as? RoomMenuEntity ?: return@setOnClickListener
                when (bean.id) {
                    RoomMenuEntity.TYPE_MUSIC -> {
                        if ((roomInfoViewModel?.getMySeatId() ?: -1) < 0) {
                            ToastUtils.show(
                                getString(R.string.music_need_mic)
                            )
                            return@setOnClickListener
                        }
                        val intent = Intent(context, MusicListActivity::class.java)
                        context?.startActivity(intent)
                    }

                    RoomMenuEntity.TYPE_BACKGROUND -> {
                        (context as? FragmentActivity)?.let {
                            SelectBackgroundFragment.start(
                                it.supportFragmentManager,
                                false,
                                backgroundViewModel?.roomThemeData?.value?.backgroundPropId
                            )
                        }
                    }

                    RoomMenuEntity.TYPE_THEME -> {
                        (context as? FragmentActivity)?.let {
                            SelectRoomThemeFragment.start(it.supportFragmentManager)
                        }
                    }

                    RoomMenuEntity.TYPE_LOCK -> {
                        RoomNotificationCenter.instances.sendNotification(
                            RoomObserver.LOCK_ROOM,
                            viewModel?.getRoomId()
                        )
                    }

                    RoomMenuEntity.TYPE_UNLOCK -> {
                        RoomNotificationCenter.instances.sendNotification(
                            RoomObserver.UN_LOCK_ROOM,
                            viewModel?.getRoomId()
                        )
                    }

                    RoomMenuEntity.TYPE_CLEAN_CHAT -> {
                        RoomNotificationCenter.instances.sendNotification(
                            RoomObserver.SHOW_CLEAR_MSG_DIALOG,
                            viewModel?.getRoomId()
                        )
                    }

                    RoomMenuEntity.TYPE_MIC_SEAT_MODE -> {
                        (context as? FragmentActivity)?.let {
                            MicModeFragment.start(
                                it.supportFragmentManager,
                                roomInfoViewModel?.seatCountData?.value ?: 0
                            )
                        }
                    }

                    RoomMenuEntity.TYPE_SETTING_COMMENT -> {
                        (context as? FragmentActivity)?.let {
                            CommentSettingDialog.start(it.supportFragmentManager)
                        }
                    }

                    RoomMenuEntity.TYPE_SCORE_BOARD -> {
                        if (pkViewModel?.isPKOpen() == true) {
                            ToastUtils.show(R.string.pk_conflict_score_1)
                            return@setOnClickListener
                        }
                        ScoreBoardDialog.start(
                            (context as? FragmentActivity)?.supportFragmentManager
                                ?: return@setOnClickListener
                        )
                    }

                    RoomMenuEntity.TYPE_PK -> {
                        if (scoreViewModel?.isScoreOpen() == true) {
                            ToastUtils.show(R.string.pk_conflict_score_2)
                            return@setOnClickListener
                        }
                        RoomEventHelper.postEvent(PKSettingEvent())
                    }

                    RoomMenuEntity.MODE_PARTY -> {

                    }

                    RoomMenuEntity.MORE_KEEP -> {
                        RoomEventHelper.postEvent(MinimizeRoomEvent())
                    }

                    RoomMenuEntity.MORE_CLOSE -> {
                        if (viewModel?.isRoomOwn() == true) {
                            CommonTipDialog(requireContext()).apply {
                                setContent(
                                    LanguageController.getInstance()
                                        .getString(R.string.close_room_tips)
                                )
                                confirmCallback = {
                                    EventBus.getDefault()
                                        .post(QuitRoomEvent(BaseRoomDataStore.QUIT_ROOM_REASON_SELF))
                                }
                            }.show()
                        } else {
                            EventBus.getDefault()
                                .post(QuitRoomEvent(BaseRoomDataStore.QUIT_ROOM_REASON_SELF))
                        }
                    }
                }
                dismiss()
            }

            var layoutParams = itemView.layoutParams
            if (settingAdapter.list.size > 8) {
                layoutParams.width =
                    (binding.roomSettingList.measuredWidth / 4.5).toInt()  // 设置每个 item 的宽度为屏幕宽度的四分之一
            }
            itemView.layoutParams = layoutParams
        }

        override fun update(bean: RoomMenuEntity) {
            itemView.tag = bean
            icon.setImageResource(bean.iconId)
            name.text = bean.name
        }

    }

}