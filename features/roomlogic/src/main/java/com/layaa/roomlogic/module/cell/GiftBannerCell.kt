package com.layaa.roomlogic.module.cell

import android.os.Looper
import android.util.LruCache
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import com.layaa.libutils.GsonUtils
import com.layaa.libutils.dp
import com.layaa.libutils.module_log.LogUtils
import com.layaa.libutils.module_thread.task.WeakHandler
import com.layaa.roomlogic.R
import com.layaa.roomlogic.databinding.LayoutGiftBannerBinding
import com.layaa.roomlogic.entity.GiftBannerItem
import com.layaa.roomlogic.entity.GiftInfoBean
import com.layaa.roomlogic.module.event.QuitRoomEvent
import com.layaa.roomlogic.module.event.ReceiveGiftEvent
import com.layaa.roomlogic.module.view.gift.GiftPanelShowEvent
import com.layaa.roomlogic.widget.GiftBannerView
import java.util.ArrayDeque

/**
 * Created by <PERSON> on 24/11/25_Mon
 */
class GiftBannerCell(order: Int) : RoomCell<LayoutGiftBannerBinding>(order) {

    private val bannerQueue = ArrayDeque<GiftBannerItem>()

    private val handler = WeakHandler(Looper.getMainLooper()) { msg ->
        when (msg.what) {
            SHOW_NEXT -> {
                showNext()
            }

            else -> {}
        }
        false
    }

    override fun changeRoomMode(roomMode: Int, roomType: String?) {
        addSelf(roomMode, roomType)
    }

    override fun getViewBinding(inflater: LayoutInflater): LayoutGiftBannerBinding? {
        return LayoutGiftBannerBinding.inflate(inflater)
    }

    override fun onViewCreate() {
        super.onViewCreate()
        viewBinding?.layoutBannerTop?.onEnd = {
            handler.sendEmptyMessage(SHOW_NEXT)
        }
        viewBinding?.layoutBannerBottom?.onEnd = {
            handler.sendEmptyMessage(SHOW_NEXT)
        }
    }

    private fun onReceive(item: GiftBannerItem) {
        bannerQueue.addOrReplace(item)
        showNext()
    }

    private fun showNext() {
        // 优先显示同一个 repeatId 的item
        bannerQueue.pollSame(viewBinding?.layoutBannerTop?.countCache)?.let {
            LogUtils.i(GiftBannerView.TAG, "same with top: ${it.repeatId}")
            viewBinding?.layoutBannerTop?.show(it)
            return
        }
        bannerQueue.pollSame(viewBinding?.layoutBannerBottom?.countCache)?.let {
            LogUtils.i(GiftBannerView.TAG, "same with bottom: ${it.repeatId}")
            viewBinding?.layoutBannerBottom?.show(it)
            return
        }

        val item = bannerQueue.peekFirst() ?: return
        val bannerView =
            if (viewBinding?.layoutBannerTop?.isRunning() != true) {
                viewBinding?.layoutBannerTop
            } else if (viewBinding?.layoutBannerBottom?.isRunning() != true &&
                (item.isMulti || item.isSendBySelf)
            ) {
                viewBinding?.layoutBannerBottom
            } else {
                null
            }
        LogUtils.i(GiftBannerView.TAG, "available bannerView: $bannerView")
        // 如果没有同一个 repeatId 的item，就显示队列中的第一个item
        bannerView?.show(bannerQueue.pollFirst() ?: return)
    }

    private fun ArrayDeque<GiftBannerItem>.addOrReplace(item: GiftBannerItem) {
        val iterator = this.iterator()
        while (iterator.hasNext()) {
            if (iterator.next().repeatId == item.repeatId) {
                iterator.remove()
            }
        }
        this.add(item)
        this.sortedWith(
            compareByDescending<GiftBannerItem> { it.isSendBySelf }
                .thenByDescending {
                    it.totalPrice
                }
        )
    }

    private fun ArrayDeque<GiftBannerItem>.pollSame(cache: LruCache<String, GiftBannerView.Count>?): GiftBannerItem? {
        val iterator = this.iterator()
        while (iterator.hasNext()) {
            val item = iterator.next()
            if (cache?.get(item.repeatId) != null) {
                iterator.remove()
                return item
            }
        }
        return null
    }

    override fun handleEvent(event: Any) {
        when (event) {
            is ReceiveGiftEvent -> {
                val infoBean =
                    GsonUtils.fromJson(event.getMsg().toString(), GiftInfoBean::class.java)
                if ((infoBean?.needShowBanner == false)){
                    return
                }
                infoBean?.receivers?.forEach {
                    val bannerBean = GiftBannerItem(
                        infoBean.sender,
                        it,
                        infoBean.receivers.size,
                        infoBean.sendGiftInfo,
                    )
                    onReceive(bannerBean)
                }
            }

            is QuitRoomEvent -> {
                bannerQueue.clear()
                viewBinding?.layoutBannerTop?.hide()
                viewBinding?.layoutBannerBottom?.hide()
                handler.removeMessages(SHOW_NEXT)
            }

            is GiftPanelShowEvent -> {
                if (event.isShow) {
                    val layoutParams = ConstraintLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        128.dp
                    )
                    layoutParams.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                    layoutParams.bottomMargin = event.height
                    viewBinding?.root?.layoutParams = layoutParams
                } else {
                    viewBinding?.root?.layoutParams = generateLayoutParams(roomMode, roomType)
                }
            }
        }
    }

    override fun generateLayoutParams(roomMode: Int, roomType: String?): ViewGroup.LayoutParams {
        val params = ConstraintLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            128.dp
        )
        params.bottomToBottom = R.id.anchor_list
        return params
    }

    override fun onDestroy() {
        super.onDestroy()
        handler.removeMessages(SHOW_NEXT)
    }

    companion object {
        const val SHOW_NEXT = 1
    }
}