package com.layaa.roomlogic.module.view

import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.layaa.widget.basepage.BaseViewHolder

/**
 * Created by <PERSON> on 2024/10/16
 */
class VoiceAnchorSpanSizeLookup(private val adapter: RecyclerView.Adapter<BaseViewHolder<*>>?) :
    GridLayoutManager.SpanSizeLookup() {
    override fun getSpanSize(position: Int): Int {
        val size = adapter?.itemCount
        when (size) {
            5, 9 -> {
                if (position == 0) {
                    return 4
                }
                return 1
            }

            12 -> {
                if (position < 2) {
                    return 5
                }
                return 2
            }
        }
        return 1
    }

    override fun getSpanIndex(position: Int, spanCount: Int): Int {
        val size = adapter?.itemCount
        when (size) {
            5, 9 -> {
                if (position == 0) {
                    return 0
                }
                return ((position - 1) % spanCount) + 1
            }

            12 -> {
                if (position < 2) {
                    return 0
                }
                return ((position - 1) % (spanCount / 2)) + 1
            }
        }
        return super.getSpanIndex(position, spanCount)
    }

    companion object {

        fun spanCount(seatCount: Int): Int {
            return if (seatCount <= 2) {
                2
            } else if (seatCount < 10) {
                4
            } else if (seatCount == 12) {
                10
            } else {
                5
            }
        }
    }
}