package com.layaa.roomlogic.module.cell

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentActivity
import com.bumptech.glide.Glide
import com.layaa.accountapi.web.WebRouter
import com.layaa.libui.dialog.CommonTipDialog
import com.layaa.libui.getString
import com.layaa.libutils.dp
import com.layaa.libutils.toast.ToastUtils
import com.layaa.roomlogic.R
import com.layaa.roomlogic.databinding.LayoutPkBinding
import com.layaa.roomlogic.module.annotations.RoomMode
import com.layaa.roomlogic.module.pk.PKEndEvent
import com.layaa.roomlogic.module.pk.PKOpenAnimDialog
import com.layaa.roomlogic.module.pk.PKOpenEvent
import com.layaa.roomlogic.module.pk.PKResultDialog
import com.layaa.roomlogic.module.pk.PKSettingDialog
import com.layaa.roomlogic.module.pk.PKSettingEvent
import com.layaa.roomlogic.module.pk.PKStatus
import com.layaa.roomlogic.module.pk.PKViewModel
import com.layaa.roomlogic.module.view.gift.GiftPanelShowEvent
import com.layaa.roomlogic.viewmodel.RoomInfoViewModel
import com.layaa.skinlib.SkinKit
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.IOException
import java.util.Locale

/**
 * Created by Todd on 25/08/26_周二
 */
class PKCell(orderIndex: Int) : RoomCell<LayoutPkBinding>(orderIndex) {

    private var roomInfoViewModel: RoomInfoViewModel? = null

    private var pkViewModel: PKViewModel? = null

    private val timerScope = CoroutineScope(Dispatchers.Main)
    private var timerJob: Job? = null

    private val switchSeatCountDialog: CommonTipDialog by lazy {
        CommonTipDialog(getContext()!!).apply {
            setContent(getString(R.string.pk_seats_warning_content))
            confirmCallback = {
                roomInfoViewModel?.updateSeatsCount(9, true)
                dismiss()
            }
        }
    }

    private val pkOpenAnimDialog by lazy {
        PKOpenAnimDialog(getContext()!!)
    }

    private val closePKWarningDialog: CommonTipDialog by lazy {
        CommonTipDialog(getContext()!!).apply {
            setContent(getString(R.string.pk_close_warning))
            confirmCallback = callback@{
                pkViewModel?.closePK(roomInfoViewModel?.getRoomId() ?: return@callback)
                dismiss()
            }
        }
    }

    private val endPKWarningDialog: CommonTipDialog by lazy {
        CommonTipDialog(getContext()!!).apply {
            setContent(getString(R.string.pk_end_warning))
            confirmCallback = callback@{
                pkViewModel?.endPK(pkViewModel?.pkInfoData?.value?.id ?: return@callback)
                dismiss()
            }
        }
    }

    private val pkResultDialog: PKResultDialog by lazy {
        PKResultDialog(getContext() ?: throw IOException("context is null"))
    }

    override fun onCreate() {
        super.onCreate()
        roomInfoViewModel = getViewModel(RoomInfoViewModel::class.java)
        pkViewModel = getViewModel(PKViewModel::class.java)
        roomInfoViewModel?.seatCountChangeData?.observe(this) { isFromPK ->
            if (isFromPK.not()) {
                return@observe
            }
            openSettingDialog()
        }

        pkViewModel?.pkInfoData?.observe(this) {
            when (it?.pkStatus) {
                PKStatus.PREPARE.value -> {
                    viewBinding?.root?.isVisible = true
                    viewBinding?.txtProgressLeft?.text = getString(R.string.pk_preparing)
                    viewBinding?.txtProgressRight?.text = getString(R.string.pk_preparing)
                    changeGuidelinePosition(0.5F)
                    viewBinding?.imgWeapon?.setImageResource(R.drawable.room_icon_pk_weapon_default)
                    viewBinding?.txtTime?.text = getString(R.string.pk_preparing)
                    viewBinding?.txtTime?.isVisible = true
                    viewBinding?.txtLevelLeft?.text = "LV.0 "
                    viewBinding?.txtLevelRight?.text = "LV.0 "
                    viewBinding?.txtCountDown?.isVisible = false
                    if (roomInfoViewModel?.isRoomOwn() == true || roomInfoViewModel?.isRoomManager() == true) {
                        viewBinding?.txtStart?.isVisible = true
                        viewBinding?.imgClose?.isVisible = true
                    } else {
                        viewBinding?.txtStart?.isVisible = false
                        viewBinding?.imgClose?.isVisible = false
                    }
                    viewBinding?.imgRules?.isVisible = false
                    viewBinding?.imgEnd?.isVisible = false
                    timerJob?.cancel()
                }

                PKStatus.IN_PROGRESS.value -> {
                    viewBinding?.root?.isVisible = true
                    viewBinding?.txtStart?.isVisible = false
                    if (roomInfoViewModel?.isRoomOwn() == true || roomInfoViewModel?.isRoomManager() == true) {
                        viewBinding?.imgClose?.isVisible = true
                        viewBinding?.imgEnd?.isVisible = true
                        viewBinding?.imgRules?.isVisible = false
                    } else {
                        viewBinding?.imgClose?.isVisible = false
                        viewBinding?.imgRules?.isVisible = true
                        viewBinding?.imgEnd?.isVisible = false
                    }
                    updateTimer(it.remainTime)
                }

                else -> {
                    timerJob?.cancel()
                    // 还原位置
                    viewBinding?.root?.layoutParams = generateLayoutParams(roomMode, roomType)
                    viewBinding?.root?.isVisible = false
                }
            }
        }

        pkViewModel?.pkValueData?.observe(this) {
            viewBinding?.txtLevelLeft?.text = "LV.${it.left.level} "
            viewBinding?.txtLevelRight?.text = "LV.${it.right.level} "
            viewBinding?.txtProgressLeft?.text = it.left.score.toString()
            viewBinding?.txtProgressRight?.text = it.right.score.toString()

            // 进度条
            if (it.left.score != 0L || it.right.score != 0L) {
                val totalScore = it.left.score + it.right.score
                var leftPercent = it.left.score / totalScore.toFloat()
                val rightPercent = it.right.score / totalScore.toFloat()
                // 最小宽度 0.25
                if (leftPercent < 0.25f) {
                    leftPercent = 0.25f
                } else if (rightPercent < 0.25f) {
                    leftPercent = 0.75f
                }
                changeGuidelinePosition(leftPercent)
            }

            // 武器
            var imgWeaponScale: Float
            var imgWeaponRes: Any
            if (it.left.score > it.right.score) {
                imgWeaponRes = it.left.levelIcon
                imgWeaponScale = weaponImageScale(true)
            } else if (it.left.score < it.right.score) {
                imgWeaponRes = it.right.levelIcon
                imgWeaponScale = weaponImageScale(false)
            } else {
                if (it.left.score == 0L) {
                    imgWeaponRes = R.drawable.room_icon_pk_weapon_default
                    imgWeaponScale = 1F
                } else if (it.left.lastScoreUpdateTs > it.right.lastScoreUpdateTs) {
                    imgWeaponRes = it.left.levelIcon
                    imgWeaponScale = weaponImageScale(true)
                } else {
                    imgWeaponRes = it.right.levelIcon
                    imgWeaponScale = weaponImageScale(false)
                }
            }
            Glide.with(viewBinding?.imgWeapon ?: return@observe)
                .load(imgWeaponRes)
                .into(viewBinding?.imgWeapon ?: return@observe)
            viewBinding?.imgWeapon?.scaleX = imgWeaponScale

        }
    }

    private fun weaponImageScale(isLeft: Boolean): Float {
        return if (isLeft) {
            if (SkinKit.getInstance().isRtl) {
                -1F
            } else {
                1F
            }
        } else {
            if (SkinKit.getInstance().isRtl) {
                1F
            } else {
                -1F
            }
        }
    }

    private fun changeGuidelinePosition(percent: Float) {
        val params =
            viewBinding?.guidelineProgress?.layoutParams as? ConstraintLayout.LayoutParams
                ?: return
        // 测试：添加RTL适配看看是否有影响
        val finalPercent = if (SkinKit.getInstance().isRtl) {
            1.0f - percent
        } else {
            percent
        }
        params.guidePercent = finalPercent
        viewBinding?.guidelineProgress?.layoutParams = params
    }

    override fun onViewCreate() {
        viewBinding?.txtStart?.setOnClickListener {
            pkViewModel?.startPK(roomInfoViewModel?.getRoomId() ?: return@setOnClickListener)
        }
        viewBinding?.imgClose?.setOnClickListener {
            if (pkViewModel?.pkInfoData?.value?.pkStatus == PKStatus.IN_PROGRESS.value) {
                closePKWarningDialog.show()
            } else {
                pkViewModel?.closePK(roomInfoViewModel?.getRoomId() ?: return@setOnClickListener)
            }
        }
        viewBinding?.imgEnd?.setOnClickListener {
            endPKWarningDialog.show()
        }
        viewBinding?.imgRules?.setOnClickListener {
            WebRouter.gotoWebDialog(PKSettingDialog.RULES_PAGE, 24, 0.5F)
        }
    }


    private fun updateTimer(remainTime: Int) {
        timerJob?.cancel()
        timerJob = timerScope.launch {
            for (second in remainTime downTo 1) {
                if (second <= 10) {
                    viewBinding?.txtCountDown?.isVisible = true
                    viewBinding?.txtTime?.isVisible = false
                    viewBinding?.imgRules?.isVisible = false
                    viewBinding?.imgEnd?.isVisible = false
                    viewBinding?.txtCountDown?.text = second.toString()
                    startBreathingAnim(viewBinding?.txtCountDown ?: return@launch)
                } else {
                    viewBinding?.txtCountDown?.isVisible = false
                    viewBinding?.txtTime?.isVisible = true
                    viewBinding?.txtTime?.text =
                        String.format(Locale.ENGLISH, "%02d:%02d", second / 60, second % 60)
                }
                delay(1000)
            }
        }
    }

    private fun openSettingDialog() {
        PKSettingDialog.start(
            (getActivity() as? FragmentActivity)?.supportFragmentManager ?: return
        )
    }

    override fun changeRoomMode(roomMode: Int, roomType: String?) {
        addSelf(roomMode, roomType)
    }

    override fun getViewBinding(inflater: LayoutInflater): LayoutPkBinding? {
        return LayoutPkBinding.inflate(inflater)
    }

    override fun handleEvent(event: Any) {
        if (event is PKSettingEvent) {
            if (roomInfoViewModel?.isRoomOwn() != true && roomInfoViewModel?.isRoomManager() != true) {
                ToastUtils.show(getString(R.string.room_permission_denied))
                return
            }
            if (roomInfoViewModel?.seatData?.value?.size != 9) {
                switchSeatCountDialog.show()
            } else {
                openSettingDialog()
            }
        } else if (event is PKOpenEvent) {
            pkOpenAnimDialog.show()
        } else if (event is PKEndEvent) {
            pkResultDialog.show(event.result)
        } else if (event is GiftPanelShowEvent) {
            if (viewBinding?.root?.isVisible != true) {
                return
            }
            if (event.isShow) {
                val layoutParams = ConstraintLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
                layoutParams.topToBottom = ConstraintLayout.LayoutParams.UNSET
                layoutParams.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                layoutParams.bottomMargin = event.height
                viewBinding?.root?.layoutParams = layoutParams
            } else {
                viewBinding?.root?.layoutParams = generateLayoutParams(roomMode, roomType)
            }
        }
    }

    override fun generateLayoutParams(
        @RoomMode roomMode: Int,
        roomType: String?
    ): ViewGroup.LayoutParams {
        val layoutParams = ConstraintLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        layoutParams.topToBottom = R.id.anchor_list
        layoutParams.bottomToBottom = ConstraintLayout.LayoutParams.UNSET
        layoutParams.topMargin = -(12.dp)
        return layoutParams
    }

    private fun startBreathingAnim(view: View) {
        val scaleUpX = ObjectAnimator.ofFloat(view, "scaleX", 1f, 1.3f)
        val scaleUpY = ObjectAnimator.ofFloat(view, "scaleY", 1f, 1.3f)
        val scaleDownX = ObjectAnimator.ofFloat(view, "scaleX", 1.3f, 1f)
        val scaleDownY = ObjectAnimator.ofFloat(view, "scaleY", 1.3f, 1f)

        scaleUpX.duration = 500
        scaleUpY.duration = 500
        scaleDownX.duration = 500
        scaleDownY.duration = 500

        val set = AnimatorSet()
        set.play(scaleUpX).with(scaleUpY)
        set.play(scaleDownX).with(scaleDownY).after(scaleUpX)

        set.interpolator = AccelerateDecelerateInterpolator()
        set.start()
    }

    fun onIMCellFullScreen(isFull: Boolean) {
        val pkStatus = pkViewModel?.pkInfoData?.value?.pkStatus ?: PKStatus.NOT_OPEN.value
        viewBinding?.root?.isVisible =
            !isFull && pkStatus != PKStatus.NOT_OPEN.value
    }
}