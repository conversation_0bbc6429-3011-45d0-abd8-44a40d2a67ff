package com.layaa.roomlogic.module.cell

import android.app.Activity
import android.content.Context
import android.view.ContextThemeWrapper
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.ComponentActivity
import androidx.annotation.CallSuper
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import androidx.viewbinding.ViewBinding
import com.layaa.libui.applyWindowInsets
import com.layaa.roomlogic.R
import com.layaa.roomlogic.module.annotations.RoomMode
import com.layaa.roomlogic.module.event.RoomEvent
import com.layaa.roomlogic.module.helper.RoomEventHelper
import com.layaa.widget.mvvm.BaseViewModel
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 *<AUTHOR>
 *@date 2021/5/27
 *@des
 **/
abstract class RoomCell<VB : ViewBinding>(val orderIndex: Int) :
    LifecycleOwner, LifecycleObserver {

    var mView: View? = null

    var mParent: RoomCellParent? = null

    var viewBinding: VB? = null

    private var _lifecycleRegistry: LifecycleRegistry? = null

    private var mCreate = false
    protected var addToWindow = false

    var roomMode = RoomMode.MODE_VOICE
    var roomType: String? = null

    private val mLifecycleRegistry: LifecycleRegistry
        get() = _lifecycleRegistry ?: LifecycleRegistry(this).also {
            _lifecycleRegistry = it
        }


    /**
     * view 加入到布局中 开始接受livedata处理数据
     */
    fun handlerAttachToWindow() {
        addToWindow = true
        mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_START)
        onAttachToWindow()
    }


    protected open fun onAttachToWindow() {

    }

    /**
     * view 从布局中移除 view停止处理数据
     */
    fun handlerDetachFromWindow() {
        addToWindow = false
        mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_STOP)
        onDetachFromWindow()
        mView?.setTag(R.id.view_order_index, null)
        (mView?.parent as? ViewGroup)?.removeView(mView)
        mView = null
        viewBinding = null
    }

    protected open fun onDetachFromWindow() {

    }

    /**
     * cell create 初始化viewmodel等相关数据
     */
    @CallSuper
    open fun onCreate() {
        if (mCreate) {
            return
        }
        mCreate = true
        (getActivity() as? ComponentActivity)?.lifecycle?.addObserver(this)
        mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_CREATE)
        RoomEventHelper.register(this)
    }

    /**
     * cell destroy
     */
    open fun onDestroy() {

    }

    open fun createView(
        order: Int,
        context: Context?
    ): Any? {
        context ?: return null
        viewBinding = getViewBinding(LayoutInflater.from(context))
        mView = viewBinding?.root
        onViewCreate()
        return mView
    }

    /**
     * view 加载会调用 只写view初始化相关
     */
    open fun onViewCreate() {

    }

    fun getView(): Any? {
        return mView
    }

    fun switchRoomMode(@RoomMode roomMode: Int, roomType: String?) {
        this.roomMode = roomMode
        this.roomType = roomType
        changeRoomMode(roomMode, roomType)
    }

    protected abstract fun changeRoomMode(@RoomMode roomMode: Int, roomType: String?)

    fun removeSelf() {
        mParent?.removeCell(getCellName())
//        mParent = null
    }

    fun addSelf(@RoomMode roomMode: Int, roomType: String?) {
        mParent?.addCell(orderIndex, getCellName(), roomMode, roomType)
    }

    @OnLifecycleEvent(value = Lifecycle.Event.ON_RESUME)
    open fun onResume() {

    }

    @OnLifecycleEvent(value = Lifecycle.Event.ON_START)
    open fun onStart() {

    }

    @OnLifecycleEvent(value = Lifecycle.Event.ON_PAUSE)
    open fun onPause() {

    }

    @OnLifecycleEvent(value = Lifecycle.Event.ON_STOP)
    open fun onStop() {
    }

    @OnLifecycleEvent(value = Lifecycle.Event.ON_DESTROY)
    fun onDestroyFromActivity() {
        handleDestroy()
    }

    private fun handleDestroy() {
        handlerDetachFromWindow()
        onDestroy()
        mParent = null
        mView = null
        viewBinding = null
        RoomEventHelper.unRegister(this)
        (getActivity() as? ComponentActivity)?.lifecycle?.removeObserver(this)
        mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    }

    open fun getLayoutId(): Int {
        return 0
    }

    abstract fun getViewBinding(inflater: LayoutInflater): VB?

    fun <T> findViewById(resId: Int): T? {
        return mView?.findViewById(resId)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: RoomEvent<Any>) {
        handleEvent(event.event)
    }

    abstract fun handleEvent(event: Any)

    fun getCellName(): String {
        return this.javaClass.simpleName
    }

    open fun dispatchKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        return false
    }

    open fun generateLayoutParams(
        @RoomMode roomMode: Int,
        roomType: String?
    ): ViewGroup.LayoutParams {
        var params = mView?.layoutParams
        if (params == null) {
            params = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            mView?.layoutParams = params
        }
        return params
    }

    fun <T : BaseViewModel> getViewModel(modelClass: Class<T>): T? {
        val activity: Activity = getActivity() ?: return null
        return ViewModelProvider(activity as ViewModelStoreOwner)[modelClass]
    }

    override val lifecycle: Lifecycle
        get() = mLifecycleRegistry

    fun getActivity(): Activity? {
        return getDialogActivity(getContext())
    }

    open fun getContext(): Context? {
        return mParent?.getContext()
    }

    open fun <T> getCell(cellPath: String): T? {
        return mParent?.getCell(cellPath)
    }

    private fun getDialogActivity(context: Context?): Activity? {
        try {
            var activity: Activity? = null
            if (context is Activity) {
                activity = context
            } else if (context is ContextThemeWrapper) {
                activity = context.baseContext as Activity
            }
            return activity
        } catch (e: Throwable) {
            e.printStackTrace()
        }
        return null
    }

    open fun setNavigationPadding(extraPadding: Int = 0, paddingView: View? = viewBinding?.root) {
        paddingView?.applyWindowInsets { view, insets ->
            val bars =
                insets.getInsets(WindowInsetsCompat.Type.systemBars() or WindowInsetsCompat.Type.displayCutout())
            view.setPadding(0, 0, 0, bars.bottom.plus(extraPadding))
        }
    }
}