package com.layaa.roomlogic.api

import com.layaa.accountapi.login.LoginRouter
import com.layaa.libnet.RetrofitFactory
import com.layaa.libnet.bean.ApiResponseNonDataWareEntity
import com.layaa.libnet.util.checkSuccess
import com.layaa.libnet.util.parseApiResponse
import com.layaa.libnet.util.parseApiResponseNullable
import com.layaa.libui.toEnString
import com.layaa.libutils.GsonUtils
import com.layaa.libutils.module_thread.task.ThreadPool
import com.layaa.roomapi.entity.RoomItemEntity
import com.layaa.roomlogic.entity.ActionEnum
import com.layaa.roomlogic.entity.CreateRoomPropParam
import com.layaa.roomlogic.entity.LiveLinkMicItemEntity
import com.layaa.roomlogic.entity.LivePKConfigEntity
import com.layaa.roomlogic.entity.LiveRankListEntity
import com.layaa.roomlogic.entity.LiveRoomAnchorListEntity
import com.layaa.roomlogic.entity.LockInfoEntity
import com.layaa.roomlogic.entity.PKRecordsListEntity
import com.layaa.roomlogic.entity.PlayCenterList
import com.layaa.roomlogic.entity.RelationListEntity
import com.layaa.roomlogic.entity.RestrictedTypeEnum
import com.layaa.roomlogic.entity.RoomBannerEntity
import com.layaa.roomlogic.entity.RoomListEntity
import com.layaa.roomlogic.entity.RoomManagerRecordBean
import com.layaa.roomlogic.entity.RoomManagerRecordList
import com.layaa.roomlogic.entity.RoomMediaEntity
import com.layaa.roomlogic.entity.RoomOperationRecordBean
import com.layaa.roomlogic.entity.RoomProfileEntity
import com.layaa.roomlogic.entity.RoomUserEntity
import com.layaa.roomlogic.entity.SearchRoomUserResult
import com.layaa.roomlogic.entity.SearchUserEntity
import com.layaa.roomlogic.entity.VideoRoomItemEntity
import com.layaa.roomlogic.entity.VideoRoomListEntity
import com.layaa.roomlogic.module.score.ScoreBoard
import com.layaa.roomlogic.module.score.ScoreRecordDetail
import com.layaa.roomlogic.module.score.ScoreRecordList
import com.layaa.roomlogic.ranking.date.RankingBean
import com.layaa.roomlogic.roomlist.RoomListViewModel
import com.layaa.roomlogic.voiceroom.entity.UserOnlineListEntity
import com.layaa.roomlogic.voiceroom.entity.VoiceGiftRecordEntity
import com.layaa.shopapi.bean.ProductOwnListEntity
import com.layaa.shopapi.bean.ProductStoreListEntity
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.io.IOException
import java.util.UUID

/**
 *<AUTHOR>
 *@date 2020/9/19
 *@des
 **/
class RoomRepository {

    companion object {

        const val LIMIT = 20

        @JvmStatic
        suspend fun getVideoList(params: MutableMap<String, String>): VideoRoomListEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher() + CoroutineName("getVideoList")) {
                params["uid"] = LoginRouter.getUserId()
                RetrofitFactory.getApi(RoomApi::class.java).getVideoList(params).parseApiResponse()
            }

        @JvmStatic
        suspend fun getHotList(params: MutableMap<String, String>): RoomListEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher() + CoroutineName("getHotList")) {
                params["uid"] = LoginRouter.getUserId()
                RetrofitFactory.getApi(RoomApi::class.java).getHotList(params).parseApiResponse()
            }

        @JvmStatic
        suspend fun getRoomList(params: Map<String, String>): RoomListEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher() + CoroutineName("getRoomList")) {
                when (params["type"]) {
                    RoomListViewModel.TYPE_RECENT -> {
                        RetrofitFactory.getApi(RoomApi::class.java).getRecentlyList(params)
                            .parseApiResponse()
                    }

                    RoomListViewModel.TYPE_FOLLOW -> {
                        RetrofitFactory.getApi(RoomApi::class.java).getFollowingList(params)
                            .parseApiResponse()
                    }

                    else -> {
                        throw IOException("type error")
                    }
                }
            }

        @JvmStatic
        suspend fun queryProfileRoom(roomId: String? = null): RoomItemEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher() + CoroutineName("queryProfileRoom")) {
                val params = HashMap<String, String>()
                params.put("uid", LoginRouter.getUserId())
                roomId?.let {
                    params.put("roomId", it)
                }
                RetrofitFactory.getApi(RoomApi::class.java).queryUserRoom(params).parseApiResponse()
            }

        @JvmStatic
        suspend fun queryPageInfo(): RoomItemEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher() + CoroutineName("queryPageInfo")) {
                RetrofitFactory.getApi(RoomApi::class.java).queryPageInfo().parseApiResponse()
            }

        @JvmStatic
        suspend fun queryUserOpeningRoomId(): String? =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher() + CoroutineName("queryProfileRoom")) {
                RetrofitFactory.getApi(RoomApi::class.java).queryUserOpeningRoomId()
                    .parseApiResponseNullable()
            }

        @JvmStatic
        suspend fun createRoom(
            roomName: String?,
            notice: String?,
            photo: String?,
            seatsCount: Int?,
            backgroundPropParam: CreateRoomPropParam?,
        ): RoomItemEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher() + CoroutineName("queryProfileRoom")) {
                val params = HashMap<String, String>()
                params["seatPermission"] = "1"
                params["roomName"] = roomName ?: LoginRouter.getNickName()
                notice?.let {
                    params["notice"] = it
                }
                //params["notice"] = notice ?: LoginRouter.getNickName()
                photo?.let {
                    params["photo"] = it
                }
                params["channelType"] = "1"
                params["roomMode"] = "1"
                seatsCount?.let {
                    params["seatsCount"] = it.toString()
                }
                GsonUtils.toJson(backgroundPropParam)?.let {
                    params["backgroundProp"] = it
                }
                RetrofitFactory.getApi(RoomApi::class.java).createRoom(params).parseApiResponse()
            }

        @JvmStatic
        suspend fun enterRoom(params: Map<String, String>): RoomMediaEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).enterRoom(params).parseApiResponse()
            }

        suspend fun leaveRoom(params: MutableMap<String, String?>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                params["uid"] = LoginRouter.getUserId()
                RetrofitFactory.getApi(RoomApi::class.java).leaveRoom(params)
                true
            }

        suspend fun closeRoom(params: MutableMap<String, String?>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                params["uid"] = LoginRouter.getUserId()
                RetrofitFactory.getApi(RoomApi::class.java).closeRoom(params)
                true
            }

        suspend fun asyncQueryRoomInfo(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                params["uid"] = LoginRouter.getUserId()
                RetrofitFactory.getApi(RoomApi::class.java).asyncQueryRoomInfo(params)
                true
            }

        suspend fun refreshRoomToken(roomId: String): String =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).refreshRoomToken(roomId)
                    .parseApiResponse()
            }

        suspend fun selectSeat(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                params["uid"] = LoginRouter.getUserId()
                RetrofitFactory.getApi(RoomApi::class.java).selectSeat(params)
                true
            }

        suspend fun leaveSeat(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                params["uid"] = LoginRouter.getUserId()
                RetrofitFactory.getApi(RoomApi::class.java).leaveSeat(params)
                true
            }

        suspend fun updateMicStatus(micStatus: Int, roomId: String): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .updateMicStatus(micStatus, roomId, LoginRouter.getUserId())
                true
            }

        @JvmStatic
        suspend fun lockSeat(type: Int, roomId: String, seatId: Int): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .seatManage(type, roomId, seatId, LoginRouter.getUserId())
                true
            }

        @JvmStatic
        suspend fun muteSeat(type: Int, roomId: String, seatId: Int): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .seatManage(type, roomId, seatId, LoginRouter.getUserId())
                true
            }

        @JvmStatic
        suspend fun seatInvite(roomId: String?, userId: String?, seatId: Int? = null): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .seatInvite(roomId, userId, seatId)
                true
            }

        @JvmStatic
        suspend fun acceptSeatInvite(
            roomId: String?,
            inviteId: String?,
            seatId: Int? = null
        ): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .acceptSeatInvite(roomId, inviteId, seatId)
                true
            }

        @JvmStatic
        suspend fun kickRoomUser(roomId: String, userId: String): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .kickRoomUser(roomId, userId, LoginRouter.getUserId())
                true
            }

        suspend fun queryBanner(params: MutableMap<String, String>): List<RoomBannerEntity> =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                val apiResult = RetrofitFactory.getApi(RoomApi::class.java).queryBanner(params)
                    .parseApiResponse()
                apiResult.filter {
                    LoginRouter.getLevel() >= (it.level ?: 0)
                }
            }

        @JvmStatic
        suspend fun getUserProfile(params: MutableMap<String, String>): RoomUserEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).getUserProfile(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun randomTitle(lastTitle: String?): String =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).getRandomTitle(lastTitle)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun getLivePageInfo(): VideoRoomItemEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                val params = mapOf(
                    "uid" to LoginRouter.getUserId(),
                    "regionId" to LoginRouter.getRegionId().toString(),
                    "roomId" to "LR-${LoginRouter.getUserId()}"
                )
                RetrofitFactory.getApi(RoomApi::class.java).getLivePageInfo(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun startLive(params: HashMap<String, String>): VideoRoomItemEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                params["uid"] = LoginRouter.getUserId()
                params["regionId"] = LoginRouter.getRegionId().toString()
                RetrofitFactory.getApi(RoomApi::class.java).startLive(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun enterLiveRoom(params: MutableMap<String, String>): RoomMediaEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                params["uid"] = LoginRouter.getUserId()
                params["regionId"] = LoginRouter.getRegionId().toString()
                RetrofitFactory.getApi(RoomApi::class.java).enterLiveRoom(params).parseApiResponse()
            }

        suspend fun syncLiveRoomInfo(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                params["uid"] = LoginRouter.getUserId()
                params["regionId"] = LoginRouter.getRegionId().toString()
                RetrofitFactory.getApi(RoomApi::class.java).syncLiveRoomInfo(params)
                true
            }

        suspend fun followLiveRoom(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).followLiveRoom(params)
                    .parseApiResponse()
            }

        suspend fun unFollowLiveRoom(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).unFollowLiveRoom(params)
                    .parseApiResponse()
            }

        suspend fun liveLike(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).liveLike(params)
                    .parseApiResponse()
            }

        suspend fun getLiveRank(params: MutableMap<String, String>): LiveRankListEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).getLiveRank(params)
                    .parseApiResponse()
            }

        suspend fun userMange(
            roomId: String?,
            remoteUid: String?,
            restrictedType: RestrictedTypeEnum,
            action: ActionEnum,
            durationSeconds: Int? = null,
        ): Boolean? = withContext(ThreadPool.instance.asDispatcher()) {
            RetrofitFactory.getApi(RoomApi::class.java)
                .userMange(roomId, remoteUid, restrictedType.value, action.value, durationSeconds)
                .parseApiResponseNullable()
        }

        suspend fun removeBlack(remoteUid: String?): Boolean =
            withContext(ThreadPool.instance.asDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).removeBlack(remoteUid)
                    .parseApiResponse()
            }

        suspend fun liveClearScreen(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).liveClearScreen(params)
                    .parseApiResponse()
            }

        suspend fun getRoomManagerList(params: MutableMap<String, String>): List<RoomUserEntity> =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                val roleCode = JSONArray()
                roleCode.put(10)
                params["roleCode"] = roleCode.toString()
                RetrofitFactory.getApi(RoomApi::class.java).getRoomManagerList(params)
                    .parseApiResponse()
            }

        suspend fun addRoomManager(roomId: String?, userId: String?): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                val roomManageInfoDTO = JSONObject()
                roomManageInfoDTO.put("uid", userId)
                roomManageInfoDTO.put("role", 10)
                RetrofitFactory.getApi(RoomApi::class.java)
                    .changeRoleManage(roomId, roomManageInfoDTO.toString(), 1)
                true
            }

        @JvmStatic
        suspend fun removeRoomManager(roomId: String?, userId: String?): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                val roomManageInfoDTO = JSONObject()
                roomManageInfoDTO.put("uid", userId)
                roomManageInfoDTO.put("role", 10)
                RetrofitFactory.getApi(RoomApi::class.java)
                    .changeRoleManage(roomId, roomManageInfoDTO.toString(), 2)
                true
            }

        suspend fun getManagementRecordList(
            roomId: String?,
            pageNum: Int,
            pageSize: Int
        ): RoomManagerRecordList<RoomOperationRecordBean> =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .getManagementRecordList(roomId, pageNum, pageSize)
                    .parseApiResponse()
            }

        suspend fun getRestrictedRecordList(
            roomId: String?,
            pageNum: Int,
            pageSize: Int,
            types: List<String>
        ): RoomManagerRecordList<RoomManagerRecordBean> =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .getRestrictedRecordList(
                        roomId,
                        pageNum,
                        pageSize,
                        GsonUtils.toJson(types).toString()
                    )
                    .parseApiResponse()
            }

        suspend fun liveLeave(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).liveLeave(params)
                    .parseApiResponse()
            }

        suspend fun closeLiveRoom(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).closeLiveRoom(params)
                    .parseApiResponse()
            }


        suspend fun updateLiveProfile(params: MutableMap<String, String>): VideoRoomItemEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).updateLiveProfile(params)
                    .parseApiResponse()
            }

        suspend fun addFollow(params: MutableMap<String, String>): Int? =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).addFollow(params)
                    .parseApiResponseNullable()
            }

        suspend fun cancelFollow(params: MutableMap<String, String>): Int? =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).cancelFollow(params)
                    .parseApiResponseNullable()
            }

        suspend fun searchRoomUser(roomId: String?, searchField: String): SearchRoomUserResult =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).searchRoomUser(roomId, searchField)
                    .parseApiResponse()
            }

        suspend fun searchUser(content: String): ArrayList<SearchUserEntity> =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).searchUser(content)
                    .parseApiResponse()
            }

        suspend fun searchFamily(familyId: String) =
            withContext(ThreadPool.instance.asDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).searchFamily(familyId)
                    .parseApiResponse()
            }

        suspend fun applyFamily(familyId: String) =
            withContext(ThreadPool.instance.asDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).applyFamily(familyId)
                    .parseApiResponse()
            }

        suspend fun kickMic(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).kickMic(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun liveLinkAnchorList(params: MutableMap<String, String>): LiveRoomAnchorListEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).getAnchorApplyList(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun liveLinkAudienceList(params: MutableMap<String, String>): LiveRoomAnchorListEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).getLinkAudienceApplyList(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun reactLinkAnchor(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).reactLinkAnchor(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun reactMatchAnchor(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).reactMatchAnchor(params)
                    .parseApiResponse()
            }


        @JvmStatic
        suspend fun reactLinkAudience(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).reactLinkAudience(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun getContributionList(params: MutableMap<String, String>): LiveRoomAnchorListEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).getContributionList(params)
                    .parseApiResponse()
            }


        @JvmStatic
        suspend fun searchUserById(params: MutableMap<String, String>): LiveLinkMicItemEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).searchUserById(params)
                    .parseApiResponse()
            }


        @JvmStatic
        suspend fun linkSearchedAnchor(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).linkAnchor(params).parseApiResponse()
            }


        @JvmStatic
        suspend fun audienceGetLinkMicList(params: MutableMap<String, String>): LiveRoomAnchorListEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).audienceGetLinkMicList(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun audienceDoLinkMic(params: MutableMap<String, String>): RoomUserEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).audienceDoLinkMic(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun audienceReactReceivedLink(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).audienceReactReceivedLink(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun anchorMatch(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).anchorMatch(params).parseApiResponse()
            }

        @JvmStatic
        suspend fun verifyMatch(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).verifyMatch(params).parseApiResponse()
            }

        @JvmStatic
        suspend fun anchorProcessInvitation(params: MutableMap<String, String>): RoomUserEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).anchorProcessInvitation(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun anchorMuteAudience(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).anchorMuteAudience(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun anchorHangUpAudience(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).anchorHangUpAudience(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun audienceHangUpAnchor(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).audienceHangUpAnchor(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun anchorMuteAnchor(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).anchorMuteAnchor(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun anchorHangUpAnchor(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).anchorHangUpAnchor(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun pkHangUp(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).pkHangUp(params).parseApiResponse()
            }

        @JvmStatic
        suspend fun pkCancelInvite(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).pkCancelInvite(params)
                    .parseApiResponse()
            }


        @JvmStatic
        suspend fun pkInviteUser(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).pkInviteUser(params).parseApiResponse()
            }

        @JvmStatic
        suspend fun pkConvert(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).pkConvert(params).parseApiResponse()
            }

        @JvmStatic
        suspend fun pkRefuse(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).pkRefuse(params).parseApiResponse()
            }

        @JvmStatic
        suspend fun pkAccept(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).pkAccept(params).parseApiResponse()
            }

        @JvmStatic
        suspend fun dealLinkToPK(params: MutableMap<String, String>): Boolean =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).dealLinkToPK(params).parseApiResponse()
            }

        @JvmStatic
        suspend fun getPKRecordsList(params: MutableMap<String, String>): PKRecordsListEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).getPKRecordsList(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun getPKStatus(params: MutableMap<String, String>): LivePKConfigEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).getPKStatus(params).parseApiResponse()
            }

        @JvmStatic
        suspend fun getPKContributionList(params: MutableMap<String, String>): LiveRankListEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).getLiveRank(params).parseApiResponse()
            }

        @JvmStatic
        suspend fun queryArgoToken(params: Map<String, String>): String =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).queryArgoToken(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun updateRoomInfo(params: Map<String, String>): RoomProfileEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).updateRoomInfo(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun voiceUserOnlineList(roomId: String, skip: Int): UserOnlineListEntity =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                val params = mapOf(
                    "roomId" to roomId,
                    "skip" to skip.toEnString(),
                    "limit" to LIMIT.toEnString()
                )
                RetrofitFactory.getApi(RoomApi::class.java).voiceUserOnlineList(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun clearScreenMessage(roomId: String?) =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).clearScreenMessage(roomId)
            }

        @JvmStatic
        suspend fun lockRoom(roomId: String?, password: String?) =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).lockRoom(roomId, password)
            }

        @JvmStatic
        suspend fun unLockRoom(roomId: String?) =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).unLockRoom(roomId)
            }

        suspend fun getLockInfo(roomId: String?): LockInfoEntity? =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).getLockInfo(roomId)
                    .parseApiResponseNullable()
            }

        @JvmStatic
        suspend fun updateSeatsCount(roomId: String?, seatCount: Int) =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java).updateSeatsCount(roomId, seatCount)
            }

        @JvmStatic
        suspend fun getRelationList(params: MutableMap<String, String>): RelationListEntity =
            withContext(Dispatchers.IO) {
                RetrofitFactory.getApi(RoomApi::class.java).getRelationList(params)
                    .parseApiResponse()
            }

        @JvmStatic
        suspend fun fetchGlobalRanking(
            roomType: Int,
            giftType: String,
            dateType: String,
        ): RankingBean = withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
            RetrofitFactory.getApi(RoomApi::class.java)
                .fetchGlobalRanking(
                    LoginRouter.getUserId(),
                    LoginRouter.getRegionId().toString(),
                    roomType,
                    giftType,
                    dateType
                ).parseApiResponse()
        }

        suspend fun scoreBoardSwitch(
            roomId: String,
            action: Int,
            duration: Int?,
            boardId: String?
        ): ScoreBoard =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .scoreBoardSwitch(
                        roomId,
//                        LiveRoomDataStore.voiceChatMappingId ?: "",
                        "",
                        action,
                        duration,
                        boardId
                    )
                    .parseApiResponse()
            }

        suspend fun fetchScoreRecordList(roomId: String): ScoreRecordList =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .fetchScoreRecordList(
                        roomId,
//                        LiveRoomDataStore.voiceChatMappingId ?: ""
                        ""
                    )
                    .parseApiResponse()
            }

        suspend fun fetchScoreDetail(roomId: String, boardId: String): ScoreRecordDetail =
            withContext(ThreadPool.instance.threadPool.asCoroutineDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .fetchScoreDetail(
                        roomId,
//                        LiveRoomDataStore.voiceChatMappingId ?: "",
                        "",
                        boardId
                    )
                    .parseApiResponse()
            }

        suspend fun queryUserLocatedRoom(uid: String): RoomProfileEntity =
            withContext(ThreadPool.instance.asDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .queryUserLocatedRoom(uid)
                    .parseApiResponse()
            }

        suspend fun queryUserLiveRoom(uid: String): RoomProfileEntity =
            withContext(ThreadPool.instance.asDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .queryUserLiveRoom(uid)
                    .parseApiResponse()
            }

        suspend fun queryOwnPropList(
            code: String,
            dataExpireType: Int,
            pageIndex: Int,
            pageSize: Int
        ): ProductOwnListEntity =
            withContext(ThreadPool.instance.asDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .queryOwnPropList(code, dataExpireType, pageIndex, pageSize)
                    .parseApiResponse()
            }

        suspend fun querySellingGoods(
            code: String,
            pageIndex: Int,
            pageSize: Int
        ): ProductStoreListEntity =
            withContext(ThreadPool.instance.asDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .querySellingGoods(code, pageIndex, pageSize)
                    .parseApiResponse()
            }

        suspend fun updateRoomPropWearState(
            code: String,
            propId: String,
            isWear: Boolean
        ): ApiResponseNonDataWareEntity = withContext(ThreadPool.instance.asDispatcher()) {
            val wearState = if (isWear) {
                1
            } else {
                0
            }
            RetrofitFactory.getApi(RoomApi::class.java)
                .updateRoomPropWearState(code, propId, wearState)
        }

        suspend fun buyProp(
            receiveUid: String,
            productId: String,
            propId: String,
            propNum: Int,
            propType: String
        ): ApiResponseNonDataWareEntity = withContext(ThreadPool.instance.asDispatcher()) {
            RetrofitFactory.getApi(RoomApi::class.java)
                .buyProp(
                    receiveUid,
                    UUID.randomUUID().toString(),
                    productId,
                    propId,
                    propNum,
                    propType
                )
        }

        suspend fun fetchGameVisibility(): PlayCenterList =
            withContext(ThreadPool.instance.asDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .fetchGameVisibility()
                    .parseApiResponse()
            }

        suspend fun fetchRoomGiftRecords(roomId: String?): List<VoiceGiftRecordEntity> =
            withContext(ThreadPool.instance.asDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .fetchRoomGiftRecords(roomId)
                    .parseApiResponse()
            }

        suspend fun resetRoomCover(roomId: String, reason: String) =
            withContext(ThreadPool.instance.asDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .resetRoomCover(roomId, reason)
            }

        suspend fun fetchShareData(roomId: String) =
            withContext(ThreadPool.instance.asDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .getShareUrl(roomId)
                    .parseApiResponse()
            }

        suspend fun queryRocketDetail(roomId: String) =
            withContext(ThreadPool.instance.asDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .queryRocketDetail(roomId)
                    .parseApiResponse()
            }

        suspend fun queryRocketRewardRecords(roomId: String, pageNum: Int, pageSize: Int) =
            withContext(ThreadPool.instance.asDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .queryRocketRewardRecords(roomId, pageNum, pageSize)
                    .parseApiResponse()
            }

        suspend fun queryRocketRewardRuler() =
            withContext(ThreadPool.instance.asDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .queryRocketRewardRuler()
                    .parseApiResponse()
            }

        suspend fun startGrabReward(roomId: String, roundId: String) =
            withContext(ThreadPool.instance.asDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .startGrabReward(roomId, roomId)
                    .parseApiResponse()
            }

        suspend fun openPK(roomId: String, duration: Int, hostParticipate: Int) =
            withContext(ThreadPool.instance.asDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .openPK(roomId, duration, hostParticipate)
                    .checkSuccess()
            }

        suspend fun closePK(roomId: String) =
            withContext(ThreadPool.instance.asDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .closePK(roomId)
                    .checkSuccess()
            }

        suspend fun startPK(roomId: String) =
            withContext(ThreadPool.instance.asDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .startPK(roomId)
                    .checkSuccess()
            }

        suspend fun endPK(pkId: Int) =
            withContext(ThreadPool.instance.asDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .endPK(pkId)
                    .checkSuccess()
            }

        suspend fun fetchRoomConfig(roomId: String?) =
            withContext(ThreadPool.instance.asDispatcher()) {
                RetrofitFactory.getApi(RoomApi::class.java)
                    .fetchRoomConfig(roomId)
                    .parseApiResponse()
            }
    }
}
