package com.layaa.roomlogic.liveroom

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.hjq.permissions.Permission
import com.layaa.accountapi.login.LoginRouter
import com.layaa.language.LanguageController
import com.layaa.libnet.util.Success
import com.layaa.libui.dialog.CommonPerDialog
import com.layaa.libui.widget.SelectImageActivity
import com.layaa.libutils.ClickValidUtil
import com.layaa.libutils.UIUtils
import com.layaa.libutils.dp
import com.layaa.libutils.toast.ToastUtils
import com.layaa.permission.BizPermission
import com.layaa.roomapi.RoomConst
import com.layaa.roomapi.entity.RoomItemEntity
import com.layaa.roomapi.router.RoomRouter
import com.layaa.roomlogic.BaseBindingVMFragment
import com.layaa.roomlogic.R
import com.layaa.roomlogic.databinding.RoomFragmentOpenChatBinding
import com.layaa.roomlogic.dialog.MicModeFragment
import com.layaa.roomlogic.entity.UserPropType
import com.layaa.roomlogic.event.OpenChatBackgroundSelectEvent
import com.layaa.roomlogic.liveroom.dialog.ChangeTitleDialog
import com.layaa.roomlogic.liveroom.dialog.SelectBackgroundFragment
import com.layaa.roomlogic.liveroom.viewmodle.LiveBroadcastDetailViewModel
import com.layaa.roomlogic.module.view.VoiceAnchorSpanSizeLookup
import com.layaa.roomlogic.voiceroom.dialog.SelectVoiceRoomTagDialog
import com.layaa.shopapi.bean.UserPropInfoDto
import com.layaa.widget.basepage.BaseActivity
import com.layaa.widget.basepage.BaseAdapter
import com.layaa.widget.basepage.BaseViewHolder
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode


/**
 *<AUTHOR>
 *@date 2023/9/11
 *@des
 **/
class OpenChatFragment :
    BaseBindingVMFragment<LiveBroadcastDetailViewModel, RoomFragmentOpenChatBinding>() {

    private var roomEntity: RoomItemEntity? = null

    private var backgroundProp: UserPropInfoDto? = null

    private val selectImageLauncher: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        if (it.resultCode != BaseActivity.RESULT_OK) {
            return@registerForActivityResult
        }
        val path = it.data?.getStringExtra(SelectImageActivity.PATH)
            ?: return@registerForActivityResult
        setAvatar(path)
    }

    companion object {
        @JvmStatic
        fun newInstance(): OpenChatFragment {
            return OpenChatFragment()
        }
    }

    private val seatAdapter by lazy {
        Adapter()
    }

    private val seatLayoutManager by lazy {
        GridLayoutManager(
            context,
            VoiceAnchorSpanSizeLookup.spanCount(roomEntity?.seatCount ?: 5)
        ).apply {
            spanSizeLookup = VoiceAnchorSpanSizeLookup(seatAdapter)
        }
    }

    private var imagePath: String? = null

    private val selectTagDialog by lazy {
        SelectVoiceRoomTagDialog(mContext).apply {
            setOwnerActivity(requireActivity())
        }
    }

    private val setTitleDialog by lazy {
        ChangeTitleDialog(mContext).apply {
            setOwnerActivity(requireActivity())
            saveCallback = {
                val title = this?.trim()
                if (title?.isNotBlank() != true) {
                    ToastUtils.show(
                        LanguageController.getInstance().getString(R.string.room_tips_name_empty)
                    )
                } else {
                    binding.nicknameTv.text = this
                }
            }
        }
    }

    private val perDialog: CommonPerDialog by lazy {
        CommonPerDialog(mContext)
            .apply {
                setContent(getString(R.string.need_record_permission))
            }
    }

    private val randomTitleAnim: Animator by lazy {
        val animator = ObjectAnimator.ofFloat(
            binding.refresh,
            View.ROTATION,
            0f,
            -359f
        )
        animator.repeatCount = ValueAnimator.INFINITE
        animator.duration = 1000
        animator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                binding.refresh.rotation = 0f
            }
        })
        animator
    }

    private var lastRandomTitle: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
    }

    override fun initBinding(
        inflater: LayoutInflater,
        parent: ViewGroup?,
        attachToParent: Boolean
    ): RoomFragmentOpenChatBinding {
        return RoomFragmentOpenChatBinding.inflate(inflater, parent, attachToParent)
    }

    override fun initData() {
        viewModel.roomPageInfo.observe(this, Observer {
            if (it is Success) {
                refreshMyRoomInfo(it.value)
            }
        })

        viewModel.createRoom.observe(this) {
            dismissLoading()
            if (it is Success) {
                val bundle = Bundle()
                bundle.putString(RoomConst.ROOM_ID, it.value.roomId)
                bundle.putString(RoomConst.ROOM_SOURCE, "2")
                RoomRouter.gotoVoice(bundle)
                activity?.finish()
            }
        }
        viewModel.randomRoomTitle.observe(this) {
            if (it is Success) {
                binding.nicknameTv.text = it.value
                lastRandomTitle = it.value
            }
            randomTitleAnim.cancel()
        }
    }

    override fun initEvent() {

        binding.background.setOnClickListener {
            SelectBackgroundFragment.start(
                childFragmentManager,
                true,
                backgroundProp?.propId
            )
        }

        binding.imgMicMode.setOnClickListener {
            MicModeFragment.startFromOpen(childFragmentManager, roomEntity?.seatCount ?: 0)
        }

        binding.startLiveTv.setOnClickListener {
            if (!ClickValidUtil.clickValidShort()) {
                return@setOnClickListener
            }

            BizPermission.with(requireActivity())
                .permission(Permission.RECORD_AUDIO)
                .onDenied { _, _ ->
                    perDialog.show()
                }
                .onGranted { _, _ ->
                    showLoading()
                    viewModel.createRoom(
                        binding.nicknameTv.text?.toString()?.trim(),
                        null,
                        imagePath,
                        roomEntity?.photo,
                        roomEntity?.seatCount,
                        backgroundProp
                    )
                }.start()
        }


        binding.refresh.setOnClickListener {
            if (randomTitleAnim.isRunning) {
                return@setOnClickListener
            }
            randomTitleAnim.start()
            viewModel.randomTitle(lastRandomTitle)
        }

        binding.nicknameTv.setOnClickListener {
            setTitleDialog.show(binding.nicknameTv.text.toString())
        }
        binding.tagBgView.setOnClickListener {
            selectTagDialog.show()
        }

        binding.headImg.setOnClickListener {
            selectImageLauncher.launch(Intent(context, SelectImageActivity::class.java))
        }
    }

    override fun navigationBarOffsetView(): Int {
        return R.id.layoutContent
    }

    fun setAvatar(imagePath: String?) {
        if (!TextUtils.isEmpty(imagePath)) {
            Glide.with(this)
                .load(imagePath)
                .transform(RoundedCorners(10.dp))
                .placeholder(R.drawable.icon_def_bg_grad)
                .error(R.drawable.icon_def_bg_grad)
                .into(binding.headImg)
            this.imagePath = imagePath
        }
    }

    private fun refreshMyRoomInfo(data: RoomItemEntity) {
        roomEntity = data
        binding.startLiveTv.isEnabled = true
        refreshBackground(data.themeData[UserPropType.ROOM_BACKGROUND.code])

        val photo = data.photo?.ifBlank {
            LoginRouter.getUserAvatar()
        }
        Glide.with(this)
            .load(photo)
            .transform(RoundedCorners(10.dp))
            .placeholder(R.drawable.icon_def_bg_grad)
            .error(R.drawable.icon_def_bg_grad)
            .into(binding.headImg)

        if (TextUtils.isEmpty(data.roomName)) {
            binding.nicknameTv.text = LoginRouter.getNickName()
        } else {
            binding.nicknameTv.text = data.roomName
        }
        binding.seatList.adapter = seatAdapter
        binding.seatList.layoutManager = seatLayoutManager
        showSeats(data.seatCount)
    }

    private fun refreshBackground(data: UserPropInfoDto?) {
        backgroundProp = data
        Glide.with(this)
            .load(data?.resourceContent)
            .transform(CenterCrop())
            .into(binding.roomBgViewIv)
    }

    override fun refreshData() {
        viewModel.getRoomPageInfo()
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMicModeSelect(event: MicModeFragment.MicModeSelectEvent) {
        val seatCount = event.mode.id
        roomEntity?.seatCount = seatCount
        val newSpanCount = VoiceAnchorSpanSizeLookup.spanCount(seatCount)
        seatLayoutManager.spanCount = newSpanCount
        showSeats(seatCount)
    }

    private fun showSeats(seatCount: Int) {
        val list = arrayListOf<String>()
        for (i in 0 until seatCount) {
            if (i == 0) {
                list.add(LoginRouter.getUserAvatar())
            } else {
                list.add("")
            }
        }
        seatAdapter.refreshList(list)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onBackgroundSelect(event: OpenChatBackgroundSelectEvent) {
        refreshBackground(event.bean)
    }

    private inner class Adapter : BaseAdapter<String>(context) {

        private val avatarMaxSize = (70F / 375F * UIUtils.getScreenWidth()).toInt()

        override fun getLayoutResId(viewType: Int): Int {
            return R.layout.item_open_chat_avatar
        }

        override fun getViewHolder(viewType: Int, view: View): BaseViewHolder<*> {
            (view.findViewById<View>(R.id.avatar).layoutParams as ConstraintLayout.LayoutParams).let {
                it.matchConstraintMaxWidth = avatarMaxSize
                it.matchConstraintMaxHeight = avatarMaxSize
            }
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: BaseViewHolder<*>, position: Int) {
            if (holder is ViewHolder) {
                holder.update(getItem(position) ?: "")
            }
        }

    }

    private inner class ViewHolder(view: View) : BaseViewHolder<String>(view) {
        private val seatNo = findViewById<TextView>(R.id.seatNo)
        private val avatar = findViewById<ImageView>(R.id.avatar)

        override fun update(bean: String) {
            seatNo.text = "${adapterPosition + 1}"
            if (bean.isNotBlank()) {
                Glide.with(avatar)
                    .load(bean)
                    .transform(CircleCrop())
                    .into(avatar)
            } else {
                avatar.setImageResource(R.drawable.room_icon_anchor_empty)
            }
        }

    }

}