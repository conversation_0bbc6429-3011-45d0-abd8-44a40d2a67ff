package com.layaa.roomlogic.util

import com.layaa.accountapi.login.LoginRouter
import com.layaa.im.gson.GsonUtils
import com.layaa.libutils.kv.KVDelegate
import com.layaa.libutils.module_log.LogUtils
import com.layaa.libutils.module_thread.task.ThreadPool
import com.layaa.roomlogic.api.RoomRepository
import com.layaa.roomlogic.voiceroom.entity.RoomConfig
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Created by <PERSON> on 25/09/02_周二
 */
object RoomConfigManager {

    private const val KV_ROOM_CONFIG = "room_config"

    val roomConfigs = hashMapOf<String, RoomConfig>()

    private val coroutineScope =
        CoroutineScope(ThreadPool.instance.asDispatcher() + CoroutineName("RoomConfigManager"))

    fun updateRoomConfig(roomId: String? = null) = coroutineScope.launch(Dispatchers.Main) {
        queryRoomConfig(roomId)
    }

    suspend fun queryRoomConfig(roomId: String?): Pair<String, RoomConfig> {
        val configKey = if (roomId == null) {
            LoginRouter.getUserId() + KV_ROOM_CONFIG
        } else {
            roomId + KV_ROOM_CONFIG
        }
        try {
            return (configKey to RoomRepository.fetchRoomConfig(roomId).also {
                KVDelegate.getInstance().save(configKey, GsonUtils.toJson(it))
            }).also {
                roomConfigs[it.first] = it.second
            }
        } catch (e: Exception) {
            LogUtils.e(e)
            return (configKey to (GsonUtils.fromJson(
                KVDelegate.getInstance().getString(configKey, ""),
                RoomConfig::class.java
            ) ?: RoomConfig())).also {
                roomConfigs[it.first] = it.second
            }
        }
    }

    fun canShowEmojiInChat(roomId: String?): Boolean {
        return roomConfigs[roomId]?.canShowEmojiInChat ?: true
    }

}