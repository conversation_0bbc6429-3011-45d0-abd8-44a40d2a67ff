package com.layaa.roomlogic.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.layaa.language.LanguageController
import com.layaa.libui.dialog.BaseBottomSheetDialogFragment
import com.layaa.libui.dialog.CommonTipDialog
import com.layaa.roomlogic.R
import com.layaa.roomlogic.databinding.FragmentMicModeBinding
import com.layaa.roomlogic.viewmodel.RoomInfoViewModel
import com.layaa.roomlogic.voiceroom.adapter.MicModeAdapter
import com.layaa.roomlogic.voiceroom.entity.MicModeEntity
import com.layaa.widget.GridLayoutManagerWrapper
import org.greenrobot.eventbus.EventBus

/**
 *<AUTHOR>
 *@date 2024/4/27
 *@des
 **/
class MicModeFragment :
    BaseBottomSheetDialogFragment<RoomInfoViewModel, FragmentMicModeBinding>() {

    companion object {
        private const val FROM_OPEN = "from_open"

        private const val SELECTED_MODE = "selected_mode"

        @JvmStatic
        fun start(fragmentManager: FragmentManager, selectedMode: Int) {
            val fragment = MicModeFragment()
            fragment.arguments = Bundle().apply {
                putInt(SELECTED_MODE, selectedMode)
            }
            fragment.showNow(fragmentManager, MicModeFragment::class.java.simpleName)
        }

        @JvmStatic
        fun startFromOpen(fragmentManager: FragmentManager, selectedMode: Int) {
            val fragment = MicModeFragment()
            fragment.arguments = Bundle().apply {
                putBoolean(FROM_OPEN, true)
                putInt(SELECTED_MODE, selectedMode)
            }
            fragment.showNow(fragmentManager, MicModeFragment::class.java.simpleName)
        }
    }

    private var isFromOpen = false
    private var selectedMode = 0

    private var confirmDialog: CommonTipDialog? = null

    private val adapter by lazy {
        MicModeAdapter(requireContext()).apply {
            callback = {
                if (isFromOpen) {
                    EventBus.getDefault().post(MicModeSelectEvent(this))
                    dismiss()
                } else {
                    showConfirmDialog(this)
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isFromOpen = arguments?.getBoolean(FROM_OPEN, false) ?: false
        selectedMode = arguments?.getInt(SELECTED_MODE, 0) ?: 0
    }

    override fun initBinding(
        inflater: LayoutInflater,
        parent: ViewGroup?,
        attachToParent: Boolean
    ): FragmentMicModeBinding {
        return FragmentMicModeBinding.inflate(inflater, parent, attachToParent)
    }

    override fun getViewModel(): Class<RoomInfoViewModel> {
        return RoomInfoViewModel::class.java
    }

    override fun initAction() {

        binding.micModeList.layoutManager = GridLayoutManagerWrapper(context, 3)
        binding.micModeList.itemAnimator = null
        binding.micModeList.adapter = adapter

        val map = hashMapOf<Int, MicModeEntity>()
        map[MicModeEntity.MODE_TWO] =
            MicModeEntity(MicModeEntity.MODE_TWO, R.drawable.icon_mic_two)
        map[MicModeEntity.MODE_FIVE] =
            MicModeEntity(MicModeEntity.MODE_FIVE, R.drawable.icon_mic_five)
        map[MicModeEntity.MODE_EIGHT] =
            MicModeEntity(MicModeEntity.MODE_EIGHT, R.drawable.icon_mic_eight)
        map[MicModeEntity.MODE_NINE] =
            MicModeEntity(MicModeEntity.MODE_NINE, R.drawable.icon_mic_nine)
        map[MicModeEntity.MODE_TWELVE] =
            MicModeEntity(MicModeEntity.MODE_TWELVE, R.drawable.icon_mic_twelve)
        map[MicModeEntity.MODE_FIFTEEN] =
            MicModeEntity(MicModeEntity.MODE_FIFTEEN, R.drawable.icon_mic_fiften)
        map[MicModeEntity.MODE_TWENTY] =
            MicModeEntity(MicModeEntity.MODE_TWENTY, R.drawable.icon_mic_twenty)
        map[MicModeEntity.MODE_THIRTY] =
            MicModeEntity(MicModeEntity.MODE_THIRTY, R.drawable.icon_mic_thirty)

        viewModel?.roomConfigData?.observe(this) {
            val list = arrayListOf<MicModeEntity>()
            it.supportMicCount.forEach { count ->
                list.add(map[count] ?: return@forEach)
            }
            adapter.selectedMicMode = selectedMode
            adapter.refreshList(list)
        }

        viewModel?.queryRoomConfig()
    }

    private fun showConfirmDialog(mode: MicModeEntity) {
        if (confirmDialog == null) {
            confirmDialog = CommonTipDialog(requireContext())
            confirmDialog?.setContent(
                LanguageController.getInstance().getString(R.string.room_tips_switch_mic_mode)
            )
            confirmDialog?.confirmCallback = {
                (confirmDialog?.tag as? MicModeEntity)?.let {
                    viewModel?.updateSeatsCount(it.id)
                    dismiss()
                }
            }
        }
        confirmDialog?.show(mode)
    }

    override fun onDestroy() {
        super.onDestroy()
        confirmDialog?.dismiss()
    }

    inner class MicModeSelectEvent(val mode: MicModeEntity)
}